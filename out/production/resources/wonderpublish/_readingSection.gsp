<%@ page import="javax.servlet.http.Cookie" %>
<asset:stylesheet href="landingpage/epubOverride.css" async="true"/>
<asset:stylesheet href="wonderslate/readSection.css"></asset:stylesheet>

<asset:stylesheet href="katex.min.css"/>

<script type="text/javascript" async
        src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS_HTML-full">
</script>

<asset:javascript src="katex.min.js"/>
<asset:javascript src="auto-render.min.js"/>
<script type="text/x-mathjax-config">
MathJax.Hub.Config({tex2jax: {inlineMath: [['$','$'], ['\\(','\\)']]}});
</script>

<% if("zaza".equals(book.vendor)){%>
<asset:stylesheet href="landingpage/zaza.css" async="true"/>
<script>
    $('#htmlreadingcontent').addClass('zaza');
</script>
<%}%>

<style>
/*.annotator-touch-controls{*/
/*    top:70% !important;*/
/*}*/
@media (min-width: 1600px) {
    .read-content #book-read-material,
    .read-content #book-read-material .tab-content .container {
        max-width: 1250px;
    }
}
@media (min-width: 1900px) {
    .read-content #book-read-material,
    .read-content #book-read-material .tab-content .container {
        max-width: 1450px;
    }
}
iframe{
    border: none !important;
}
.navBtns:disabled{
    cursor: not-allowed;
}
</style>
<% if(("sage".equals(session["entryController"])||"evidya".equals(session["entryController"])||"etexts".equals(session["entryController"])||"ebouquet".equals(session["entryController"])||"libwonder".equals(session['entryController']))){%>
<style>
#htmlContent p span{
    vertical-align: unset;
}

</style>
<%}%>

<% if("etexts".equals(session["entryController"])) {%>
<style>
#htmlreadingcontent #htmlContent a {
    color: #000;
}
</style>
<%}%>
<% if("ebouquet".equals(session["entryController"])) {%>
<style>
#htmlreadingcontent #htmlContent a {
    color: #000;
}
.bookTemplate .content-wrapper .chapterSection a.slide-toggle {
    top:250px;
}
.bookTemplate .export-notes {
    top: 150px;
}
.evidya .bookTemplate .content-wrapper #book-sidebar .side-content > h2 {
    margin-top: 150px;
}
.evidya.hasScrolled .bookTemplate .content-wrapper #book-sidebar .side-content > h2 {
    margin-top: 0;
}
</style>
<%}%>

<%if("3".equals(""+session["siteId"]) || "37".equals(""+session["siteId"]) || "38".equals(""+session["siteId"]) || "39".equals(""+session["siteId"]) || "46".equals(""+session["siteId"])){%>
<style type="text/css" media="print">
    * { display: none; }
</style>
<%}%>
<link rel="stylesheet" href="/assets/viewer.css">
<link rel="resource" type="application/l10n" href="locale/locale.properties">
<script src="/assets/pdf.js"></script>
<script src="/assets/pdf.worker.js"></script>
%{--<script src="/assets/require.js" ></script>--}%
<script src="/assets/viewer.js"></script>


<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.5/jszip.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/epubjs/dist/epub.min.js"></script>
<script src="https://unpkg.com/sweetalert/dist/sweetalert.min.js"></script>
<script src="//cdn.jsdelivr.net/npm/sweetalert2@11"></script>


<asset:javascript src="wonderslate/epub-full.min.js"/>
<script>
    var readId;
    var resSubFileNm="";
    var defaultZoom;
    var zoomLevel;
    var downloadResId;
    var bookTitle = "";
    function displayReadingMaterial(id){
        downloadResId=id;
        if(urlCheck!="true"){
            <% if("evidya".equals(session["entryController"])||"etexts".equals(session["entryController"])||"libwonder".equals(session['entryController'])) {%>
            <% def newCookie = new javax.servlet.http.Cookie( "siteName", "${session["entryController"]}");
            newCookie.path = "/"
            response.addCookie newCookie;
            %>
            <%}%>
        }
        hideMainAndDisplayResource(id);
        $('.loading-icon').removeClass('hidden');
        if(loggedInUser) updateUserView(id,"all","read");

        else updateView(id,"all","read");

        getHtmlsData(id);
        localStorage.setItem('lastReadPDF',JSON.stringify({resId:id,pdfOpen:true,chapterId:masterChapterID,bookId:${params.bookId}}))
        showTextFormatter("");

    }

    function downloadChapter(downloadResid){
        $('.loading-icon').removeClass('hidden');
        var bookIden = htmlDecode("${params.bookId}");
        var downloadResIden=htmlDecode(downloadResId);
        var chapterIden=htmlDecode(previousChapterId)
        <g:remoteFunction controller="funlearn" action="checkDownloadAccess"  onSuccess='redirectToDownload(data);' params="'resId='+downloadResIden+'&chapterId='+chapterIden+'&bookId='+bookIden" />
    }


    function redirectToDownload(data){
        $('.loading-icon').addClass('hidden');
        if(data.download!="fail") {
            if (data.hasAccess == "Yes" && data.exceededChapterLimit == "false") {
                var downloadBookId = data.bookId;
                var downloadChapterId = data.chapterId;
                var downloadResId = data.resId;
                var filename = data.fileName;
                $('#downloadModal').modal('show');
                fetch(serverPath + "/funlearn/downloadReadingMaterial?resId=" + downloadResId + "&chapterId=" + downloadChapterId + "&bookId=" + downloadBookId + "&encryptedKey=" + encryptedKey)
                    .then(resp => resp.blob())
                    .then(blob => {
                        const url = window.URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.style.display = 'none';
                        a.href = url;
                        // the filename you want
                        document.body.appendChild(a);
                        a.download = filename;
                        a.click();
                        window.URL.revokeObjectURL(url);
                        $('#downloadModal').modal('hide');// or you know, something with better UX...
                        $('#downloadComplete').modal('show');
                    })
                    .catch(() => alert('something went wrong!please try again after sometime'));
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Your access has been blocked because you have exceeded your download limit.',
                })
            }
        }else{
            Swal.fire({
                icon: 'error',
                title: 'This institute has crossed the download limit.',
            })
        }
    }

    $('#successOk').on('click',function (){
        $('#downloadComplete').modal('hide');
    })

    $(document).ready(function(){
        $("#downloadModal").modal({
            show:false,
            backdrop:'static'
        });
    });

    function showNotesAndHighlightList(data) {
        var annotatedRows = data.rows;
        var htmlStr = "";
        for(var _ar=0;_ar<annotatedRows.length;_ar++){
            if(annotatedRows[_ar].text != null && annotatedRows[_ar].text != undefined && annotatedRows[_ar].text != "") {
                htmlStr = htmlStr + "<li  class=\"notes-list-item\" id=\"2278\"><div class=\"notes-list-item-indicator\"></div><div class=\"individual-note-selection\" style=\"display: none;\"><label class=\"not-active\"><input type=\"checkbox\" name=\"chapter\" value=\"Chapter\" id=\"notescheckbox3\"> <span class=\"checkmark\"></span>\n" +
                    "</label></div><div class=\"notes-created-by-user\"> <div><span style=\"font-size: 16px; padding-right: 1px\" class=\"note-of-user anno ator-wrapper\" id=\"notes30\">"+annotatedRows[_ar].quote.replaceAll('__','')+"</span><p class=\"comment-by-user\">"+annotatedRows[_ar].text.replaceAll('__','')+"</p></div></div></li>";
            }else{ //onclick="onclickRenderedNotes(1)"
                htmlStr = htmlStr + "<li  class=\"notes-list-item\" id=\"2277\"><div class=\"notes-list-item-indicator\"></div><div class=\"individual-note-selection\" style=\"display: none;\"><label class=\"not-active\"><input type=\"checkbox\" name=\"chapter\" value=\"Chapter\" id=\"notescheckbox1\"> <span class=\"checkmark\"></span>\n" +
                    "</label></div><div class=\"notes-created-by-user\"> <div><span style=\"font-size: 16px; padding-right: 1px\" class=\"note-of-user anno ator-wrapper\" id=\"notes10\">"+annotatedRows[_ar].quote.replaceAll('__','')+"</span></div> </div></li>";
            }
        }
        $('#user-notes').html("");
        $('#user-notes').append(htmlStr);
        garabageClearAll();
    }

    function garabageClearAll() {
        if (bookLang != "English") {
            $('#user-notes .notes-created-by-user').addClass('d-none');
            $('#user-notes .notes-created-by-user:has("p")').removeClass('d-none').addClass('d-block');
            var clearGarbageAll = document.querySelectorAll("#user-notes .notes-created-by-user span");

            for (var i = 0; i < clearGarbageAll.length; i++) {
                clearGarbageAll[i].style.display = "none";
            }
        }
    }
    var firstTimeLoadingSingleEpubFile = false;

    window.document.addEventListener('annotationSearch', handleAnnotationSearchEvent, false);
    function handleAnnotationSearchEvent(e) {
        var idArr = [];
        var data = e.detail;
        var notes = data;
        var htmlStr = '';
        var userSelection = '';
        var userComment = '';
        if(data.length > 0) {
            for(var i = 0; i < notes.length; i++) {
                if(idArr.indexOf(notes[i].id) > -1) continue;
                else idArr.push(notes[i].id);
                var highlightsMetaData = notes[i].highlights;
                var quoteAndClassNamesArr =[];
                for(var q =0; q < highlightsMetaData.length; q++){
                    if(highlightsMetaData[q].offsetParent != null && highlightsMetaData[q].offsetParent != undefined){
                        quoteAndClassNamesArr.push({"quote":highlightsMetaData[q].innerText,"className":highlightsMetaData[q].offsetParent.className.replace('t',' ')});
                    }else{
                        quoteAndClassNamesArr.push({"quote":highlightsMetaData[q].innerText,"className":""});
                    }
                }
                userSelection = notes[i].quote;
                userComment = notes[i].text;
                // //console.log(notes[i]);
                // var scrollElement = JSON.stringify(notes[i]);
                if(userComment != null) {
                    htmlStr += "<li onclick='onclickRenderedNotes("+i+")' class='notes-list-item' id='"+ notes[i].id +"'>" +
                        "<div class='notes-list-item-indicator'></div>" +
                        "<div class='individual-note-selection' style='display: none;'>" +
                        "<label class='not-active'>" +
                        "<input type='checkbox' name='chapter' value='Chapter'  id='notescheckbox"+i+"'>" +
                        " <span class='checkmark'></span>\n" +
                        "</label>" +
                        "</div>"+
                        "<div class='notes-created-by-user'>" +
                        " <div >" ;
                    for(var k = 0; k<quoteAndClassNamesArr.length; k++){
                        htmlStr += "<span style='font-size: 16px; padding-right: 1px' class='note-of-user "+quoteAndClassNamesArr[k].className +"' id='notes"+i+"'>"+quoteAndClassNamesArr[k].quote+"</span>";
                    }
                    htmlStr += "<p class='comment-by-user'>"+userComment+"</p>" +
                        "</div>" +
                        "</div>" +
                        "</li>";
                } else {
                    htmlStr += "<li onclick='onclickRenderedNotes("+i+")' class='notes-list-item' id='"+ notes[i].id +"'>" +
                        "<div class='notes-list-item-indicator'></div>" +
                        "<div class='individual-note-selection' style='display: none;'>" +
                        "<label class='not-active'>" +
                        "<input type='checkbox' name='chapter' value='Chapter'  id='notescheckbox"+i+"'>" +
                        " <span class='checkmark'></span>\n" +
                        "</label>" +
                        "</div>"+
                        "<div class='notes-created-by-user'>" +
                        " <div >"  ;
                    for(var k = 0; k<quoteAndClassNamesArr.length; k++){
                        htmlStr += "<span  style='font-size: 16px; padding-right: 1px' class='note-of-user "+ quoteAndClassNamesArr[k].className +"' id='notes"+i+"'>"+quoteAndClassNamesArr[k].quote+"</span >";
                    }
                    htmlStr += "</div> " +
                        "</div>" +
                        "</li>";
                }
            }
            htmlStr += "<div class='export-btn-wrapper' style='display: none;'>" +
                "<a href='javascript:exportToStudySet();' class='export-notes-btn waves-effect'>"+"Export"+"</a>" +
                "</div>";
        } else {
            htmlStr += "<div class='no-notes-created' id='no-notes-created'>"+"No notes created yet."+"</div>";
        }
        document.getElementById('user-notes').innerHTML = htmlStr;

    }

    var epubContainerWidth = 0;
    if($(window).width > 767) epubContainerWidth = $("#htmlreadingcontent").width();
    else epubContainerWidth = $(window).width();
    var epubScreenWidth = (99/100) * (epubContainerWidth);
    var keyListener;
    var encryptedKey = "${encryptEpubKey}";
    var book = ePub();
    var epubPageNumber = 0;
    var epubChapterHref = [];
    var rendition;
    var ebupChapterLink = "";
    var currentSpineItem = "";
    var epubChapterIndex=[];
    var totalBookChapter;
    var totalChaptersList;
    var tocObj;
    var currChap = null;
    var previewMode=${previewMode};
    var genericReader = ${genericReader};

    const fontAwesomeLink = document.createElement('link')
    fontAwesomeLink.id = 'fntLink'
    fontAwesomeLink.href = "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.6.0/css/all.min.css"
    fontAwesomeLink.rel = "stylesheet"
    const htmlhead = document.querySelector('head')
    if(!htmlhead.querySelector('#fntLink')){
        htmlhead.appendChild(fontAwesomeLink)
    }

    function displayEpub(data,resId,bookID,chapterIdForPDF){
        epubPageNumber=0;
        currChap = chapterIdForPDF;
        var epubLastRead = localStorage.getItem('lastReadDetails');

        if (epubLastRead !="" && epubLastRead !=null && epubLastRead !=undefined){
            epubLastRead = JSON.parse(epubLastRead);
        }

        //checking no of total chapters splitted at left side column.
        totalChaptersList= $('.read-book-chapters-wrapper').children('li').length;
        $('.prevBtnEpub').addClass('d-flex').removeClass('d-none');
        // Load the opf
        if(data.ebupChapterLink == undefined || data.ebupChapterLink == "" || data.ebupChapterLink == null ) book = ePub();
        $('.loading-icon').addClass('hidden');
        $('.epub-action').css('display','flex');
        $("#htmlreadingcontent").show();
        $("#htmlreadingcontent").html("");
        $('#chapter-actions').removeClass('d-lg-flex');
        document.getElementById('reader').innerHTML = "";
        if($(window).width()<767) {
            $('#chapter-actions').removeClass('d-flex').addClass('d-none');
            $('.generateTest').addClass('d-none');
        }
        $('.chapter-notes,#print-all-action,#chapter-all-action').hide();
        if($(window).width()<767){
            $('#chapter-details-tabs').width('80%');
            $('.contentEdit').width('100px');
            $('.shadowHeader').height('45px');
        }

        $('.bookTemplate,header').hide();
        document.getElementById('reader').classList.add('readerAnimation');
        var item = document.getElementById('reader');
        var navElement = "<div class='epub_nav'>" +
                            "<div>"+
                                "<button class='epub_nav-backBtn' style='cursor: pointer' title='Back to resources'>" +
                                    "<i class='fa-solid fa-arrow-left'></i>"+
                                "</button>"+
                            "</div>"+
                            "<div class='d-flex align-items-center'>" +
                                "<button class='btn epubHighlightsBtn d-flex align-items-center' id='epubHighlights'>" +
                                    "<i class='fa-solid fa-note-sticky' style='margin-right: 10px'></i>"+
                                    "Highlights"+
                                "</button>";
                    if((siteId == 12 || siteId == 23 || siteId == 24) && downloadChapterAccess && (data.isSingleEpubFile != true || data.isSingleEpubFile != 'true')){
                        navElement+="<button class='btn epubDownloadBtn d-flex align-items-center ml-2' id='epubDownload'>" +
                            "<i class='fa-solid fa-download' style='margin-right: 10px;'></i>"+
                            "Download"+
                            "</button>";
                    }
                    navElement+="</div>";
                        <% if(!previewMode) {%>
                    navElement+="<div class='d-flex align-items-center'>"+
                                "<button class='btn navBtns' id='prevBtnEpub' title='Previous Page'>" +
                                    "<i class='fa-solid fa-arrow-left'></i>"+
                                "</button>" +
                                "<button class='btn navBtns' id='nextBtnEpub' title='Next Page'>" +
                                    "<i class='fa-solid fa-arrow-right'></i>"+
                                "</button>" +
                            "</div>"+
                        <%}%>
                        "</div>";


        var footerElement = "<div class='epub_footer' style='flex-direction: column'>" +
            "<div class='d-flex justify-content-center w-100 align-items-center'>"+
            "<p>" +
            "Page " +
            "<span class='epub_footer-pageNum'></span> of  " +
            "<span class='epub_footer-totalPages'></span> " +
            "</p>"+
            "<p>" +
            "<span class='epub_footer-percentage' style='margin-left: 5px;margin-right:5px;'></span>"+
            "</p>"+
            "</div>";
        <% if(!previewMode) {%>
        footerElement += "<div class='w-100'>" +
            "<input type='range' id='epubProgressSlider' min='1'  max='100' step='1' class='epubProgressSlider'>"+
            "</div>"+
            <%}%>
            "</div>";

        var epubNotesElement = "<section class='notesWrapper displayOpenClose' style='z-index: 4'>"+
            "<div class='notesDisplay'>"+
            "<div class='notesDisplay_header'>"+
            "<h3 style='width: 100%;text-align: center'>Highlights</h3>"+
            "<button class='close'><i class='fa-solid fa-xmark'></i></button>"+
            "</div>"+
            "<div class='notesDisplay_body'>"+
            "<ul class='notesList'></ul>"+
            "</div>"+
            "</div>"+
            "</section>";

        var epubLoaderElement = "<div class='lazyLoader' style='top: 75px !important;'>"+
            "<div class='anim'>"+
            "<div class='slider-anim'>"+
            "<div class='lottieLoader'>"+
            "</div>"+
            "<div style='margin-top: 12px;'>"+
            " <p style='font-size: 16px;'>Please wait while loading your book...</p>"+
            "</div>"+
            "</div>"+
            "</div>"+
            "</div>";

        item.insertAdjacentHTML('afterbegin',navElement);
        item.insertAdjacentHTML('beforeend',footerElement);
        item.insertAdjacentHTML('beforeend',epubNotesElement);
        item.insertAdjacentHTML('beforeend',epubLoaderElement);
        item.setAttribute('style','height:100%;');
        if ( document.querySelector('.hide-password') !=null){
            document.querySelector('.hide-password').style.fontFamily = 'Material Icons';
        }


        document.querySelector('.epub_nav-backBtn').addEventListener('click',function (){
            if ($(window).width() < 768){
                $('.bookTemplate').show();
                if((siteId != 12 && siteId != 23 && siteId != 24) ){
                    $("#content-data-all").show();
                }else{
                    $("#downloadChapter").removeClass('d-flex').addClass('d-none');
                }
                $("#htmlreadingcontent").hide();
                $('#overlay,.export-notes').addClass('d-none');
                $('.contentEdit').width('0');
            }else{
                $('.bookTemplate,header').show();
                if((siteId != 12 && siteId != 23 && siteId != 24) ){
                    $("#content-data-all").show();
                }else{
                    $("#content-data-all").hide();
                    $("#downloadChapter").removeClass('d-flex').addClass('d-none');
                }
                $("#htmlreadingcontent").hide();
                $('#overlay,.export-notes').addClass('d-none');
            }
            document.getElementById('reader').classList.remove('readerAnimation');
            item.innerHTML = '';
            keyListener=undefined;
            if (siteId!= "12" && siteId!= "23" && siteId!= "24"){
                if (data.isSingleEpubFile == true){
                    getChapterDetails(currChap)
                }
            }
            saveEpubDetails(resId,epubPageNumber);
        });

        document.getElementById('epubHighlights').addEventListener('click',function (){
            if (siteId!= "12" && siteId!= "23" && siteId!= "24"){
                if (loggedInUser){
                    viewChapterNotesEpub(resId,bookID,chapterIdForPDF);
                }else{
                    loginOpen();
                }
            }else{
                if (loggedInUser){
                    viewChapterNotesEpub(resId,bookID,chapterIdForPDF);
                }else{
                    document.getElementById('epubHighlights').style.display  = 'none';
                }
            }
        });

        if((siteId == 12 || siteId == 23 || siteId == 24)  && downloadChapterAccess && (data.isSingleEpubFile != true || data.isSingleEpubFile != 'true')){
            document.getElementById('epubDownload').addEventListener('click',function (){
                downloadChapter();
            })
        }

        var footerProgressSlider  = document.getElementById('epubProgressSlider');

        if(data.isSingleEpubFile == true && firstTimeLoadingSingleEpubFile == true){
            if($(window).width()<767){
                rendition = book.renderTo("reader", {
                    flow: "scrolled-doc",
                    width: epubScreenWidth,});
            }else {
                rendition = book.renderTo("reader", {
                    flow: "scrolled-doc",
                    width: epubScreenWidth,
                    // script:"../assets/annotator-template.js"
                });
            }
            if(data.ebupChapterLink != undefined && data.ebupChapterLink != "" && data.ebupChapterLink != null ) {
                ebupChapterLink = data.ebupChapterLink;
                currentSpineItem = ebupChapterLink;
                rendition.display(data.ebupChapterLink.split("#")[0]);
            }
            else rendition.display();

            rendition.on('rendered', function(section, view) {
                for(var b=0;b<book.spine.spineItems.length;b++){
                    if(book.spine.spineItems[b].href == currentSpineItem.split("#")[0]) epubPageNumber = book.spine.spineItems[b].index;
                }
                $('.loading-icon').addClass('hidden');
                firstTimeLoadingSingleEpubFile = true;
                var contents = rendition.getContents();
                for(var c=0;c<contents.length;c++){
                    // contents[c].css('color','red',true);
                    contents[c].document.oncontextmenu = document.body.oncontextmenu = function() {return false;}
                    var bookId = "${params.bookId}";
                    var resId = 0;
                    resId = readId;
                    $(contents[c].document).find("body").attr("annotator-data",""+resId+"__"+bookLang+"__"+serverPath+"__"+bookId+"__"+ebupChapterLink);
                    // $(contents[c].document).find("body").addClass('epub-body');
                    //console.log();
                    contents[c].addStylesheet(serverPath+"/assets/annotator.min.css");
                    contents[c].addStylesheet(serverPath+"/assets/landingpage/epubFixes.css");
                    contents[c].addStylesheet("https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.6.0/css/all.min.css");
                    contents[c].addStylesheet(serverPath+"/assets/annotator-template-whitelabel.css");
                    contents[c].addScript(serverPath+'/assets/annotator-template-whitelabel.js');
                    contents[c].contentWidth(epubScreenWidth);
                    if((siteId!=12||siteId!=23||siteId!=24)){
                        var printCSS =
                            '<style type="text/css">' +
                            '@media print {' +
                            'body { display: none !important; }' +
                            '}' +
                            '</style>';
                        $(contents[c].document).find("head").append(printCSS);
                    }
                    if(siteId==28) {
                        $(contents[c].document).find("body").addClass("inside_app_in_app");
                    }
                    if((siteId==12||siteId==23||siteId==24)) {
                        $(contents[c].document).find("body").bind('cut copy paste', function (e) {
                            e.preventDefault();
                        });
                        $(contents[c].document).keydown(function (event) {
                            if (event.ctrlKey === true && (event.which == '65') || event.metaKey === true && (event.which == '65')) {
                                return false;
                            }
                            if (event.ctrlKey === true && (event.which == '67') || event.metaKey === true && (event.which == '67')) {
                                return false;
                            }
                            if (event.ctrlKey === true && (event.which == '86') || event.metaKey === true && (event.which == '86')) {
                                return false;
                            }
                            if (event.ctrlKey === true && (event.which == '88') || event.metaKey === true && (event.which == '88')) {
                                return false;
                            }
                        });
                    }
                }
            });
        } else{
            book.open(serverPath+"/funlearn/getEpubFile?resId="+readId+"&encryptedKey="+encryptedKey,"epub").then(function(toc){
                    $('.loading-icon').addClass('hidden');
                },
                function (error) {
                    //console.log("Status : "+ error.status);
                    //console.log("Stack : " + error.stack);
                });
            if($(window).width()<767){
                rendition = book.renderTo("reader", {
                    flow: "scrolled-doc",
                    width: epubScreenWidth,});
            }else {
                rendition = book.renderTo("reader", {
                    flow: "scrolled-doc",
                    width: epubScreenWidth,
                    // script:"../assets/annotator-template.js"
                });
            }
            if(data.ebupChapterLink != undefined && data.ebupChapterLink != "" && data.ebupChapterLink != null ) {
                ebupChapterLink = data.ebupChapterLink;
                currentSpineItem = ebupChapterLink;
                rendition.display(data.ebupChapterLink.split("#")[0]);
            }
            else {
                rendition.display();
            }
        }
        // rendition.on('started',function(){
        //    //console.log("started")
        // });
        // rendition.on('attached',function(){
        //     //console.log("attached")
        // });
        // rendition.on('displayed',function(){
        //     //console.log("displayed")
        // });
        // rendition.on('displayError',function(){
        //     //console.log("displayError")
        // });
        // rendition.on('removed',function(){
        //     //console.log("removed")
        // });
        // rendition.on('resized',function(e){
        //     //console.log(e);
        // });
        // rendition.on('orientationchange',function(){
        //     //console.log("orientationchange")
        // });
        // rendition.on('locationChanged',function(){
        //     //console.log("locationChanged")
        // });
        // rendition.on('relocated',function(){
        //     //console.log("relocated")
        // });
        // rendition.on('selected',function(){
        //     //console.log("selected")
        // });
        // rendition.on('markClicked',function(){
        //     //console.log("markClicked")
        // });
        rendition.on('rendered', function(section, view) {
            for(var b=0;b<book.spine.spineItems.length;b++){
                if(book.spine.spineItems[b].href == currentSpineItem.split("#")[0]) epubPageNumber = book.spine.spineItems[b].index;
            }
            var totalPageNos = book.spine.spineItems.length;
            var epubPercentage = Math.round((100 * (epubPageNumber+1) / totalPageNos));
            document.querySelector('.epub_footer-totalPages').innerHTML = totalPageNos;
            document.querySelector('.epub_footer-pageNum').innerHTML = epubPageNumber+1;
            document.querySelector('.epub_footer-percentage').innerHTML = '<span> &#x2022; </span>' + epubPercentage + ' %';

            footerProgressSlider  = document.getElementById('epubProgressSlider');
            if(footerProgressSlider) {
                footerProgressSlider.value = epubPercentage;
                footerProgressSlider.addEventListener('input', function (e) {
                    var sliderPercentage = Math.round((e.target.value / 100) * totalPageNos);
                    epubPageNumber = sliderPercentage - 1;
                    openSpecificEPubPage(data, book, epubPageNumber);
                });
            }
            if (epubLastRead!=null){
                epubLastRead.map(item=>{
                    if (item.resId==resId){
                        openSpecificEPubPage(data,book,item.pageNo)
                    }
                })
            }
            if (data.isSingleEpubFile !=true && data.isSingleEpubFile!="true"){
                openSpecificEPubPage(data,book,epubPageNumber)
            }
            $('.loading-icon').addClass('hidden');
            firstTimeLoadingSingleEpubFile = true;
            var contents = rendition.getContents();
            for(var c=0;c<contents.length;c++){
                // contents[c].css('color','red',true);
                contents[c].document.oncontextmenu = document.body.oncontextmenu = function() {return false;}
                var bookId = "${params.bookId}";
                var resId = 0;
                resId = readId;
                $(contents[c].document).find("body").attr("annotator-data",""+resId+"__"+bookLang+"__"+serverPath+"__"+bookId+"__"+ebupChapterLink);
                // $(contents[c].document).find("body").addClass('epub-body');
                //console.log();
                contents[c].addStylesheet(serverPath+"/assets/annotator.min.css");
                contents[c].addStylesheet(serverPath+"/assets/landingpage/epubFixes.css");
                contents[c].addStylesheet("https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.6.0/css/all.min.css");
                contents[c].addStylesheet(serverPath+"/assets/annotator-template-whitelabel.css");
                contents[c].addScript(serverPath+'/assets/annotator-template-whitelabel.js');
                contents[c].contentWidth(epubScreenWidth);
                if(siteId==28) {
                    $(contents[c].document).find("body").addClass("inside_app_in_app");
                }
                if((siteId==12||siteId==23||siteId==24)) {
                    $(contents[c].document).find("body").bind('cut copy paste', function (e) {
                        e.preventDefault();
                    });
                    $(contents[c].document).keydown(function (event) {
                        if (event.ctrlKey === true && (event.which == '65') || event.metaKey === true && (event.which == '65')) {
                            return false;
                        }
                        if (event.ctrlKey === true && (event.which == '67') || event.metaKey === true && (event.which == '67')) {
                            return false;
                        }
                        if (event.ctrlKey === true && (event.which == '86') || event.metaKey === true && (event.which == '86')) {
                            return false;
                        }
                        if (event.ctrlKey === true && (event.which == '88') || event.metaKey === true && (event.which == '88')) {
                            return false;
                        }
                    });
                }
                //Setting no of Chapters in book.
                totalBookChapter=book.spine.spineItems.length;

                //disabeling and enabling next button based on selected chapter
                if(epubPageNumber===(totalBookChapter - 1)){
                    $('#nextBtnEpub').attr('disabled','disabled');
                }
                //disabeling and enabling prev button based on selected chapter

                if(epubPageNumber===0){
                    $('#prevBtnEpub').attr('disabled','disabled');
                    $('#nextBtnEpub').removeAttr('disabled','disabled');
                }
                else{
                    $('#prevBtnEpub').removeAttr('disabled','disabled');
                }
                //onclick of chapters
                //for section base epub file where left side chapterlist is less than total chapters in book
                if((totalBookChapter!=totalChaptersList)){
                    var bookChapterList = epubChapterIndex[0].toc;
                    for (var e = 0; e < bookChapterList.length; e++) {
                        if (bookChapterList[e].href.split("#")[0] === currentSpineItem) {
                            $(".read-book-chapters-wrapper li").each(function (id, elem) {
                                var bookLabel=bookChapterList[e].label.split('\n').join('');
                                bookLabel=bookLabel.split('\t').join('');
                                bookLabel=bookLabel.split(':').join('');
                                if (elem.innerText.toUpperCase() == bookLabel.toUpperCase()) {
                                    $(".read-book-chapters-wrapper li").removeClass('orangeText');
                                    $(elem).addClass('orangeText');
                                    console.log($(elem))
                                    currChap = $(elem).attr('id').match(/\d+/g)[0];
                                }
                            });
                        }
                    }
                }
            }
            setTimeout(function (){
                var iframeBodyElement = document.querySelector('.epub-container .epub-view iframe').contentWindow.document.querySelector('body');
                var readerElm = document.querySelector('.notesWrapper');
                iframeBodyElement.addEventListener('copy',function (e){
                    e.preventDefault();
                    return false;
                })
                iframeBodyElement.addEventListener('cut',function (e){
                    e.preventDefault();
                    return false;
                })
                readerElm.addEventListener('copy',function (e){
                    e.preventDefault();
                    return false;
                })
                readerElm.addEventListener('cut',function (e){
                    e.preventDefault();
                    return false;
                })
                document.addEventListener('keydown',function (e){
                    if (e.ctrlKey || e.metaKey){
                        if (e.keyCode==65){
                            e.preventDefault();
                            return false;
                        }
                    }
                });

                if (document.querySelector('.epub-container') !=null){
                    document.querySelector('.epub-container').setAttribute('style','height:100% !important;overflow:scroll !important;padding-bottom:8rem')
                }
                if (book.spine.spineItems.length<=1){
                    $('#nextBtnEpub').attr('disabled','disabled');
                }
            },500)
        });

        // Navigation loaded
        book.loaded.navigation.then(function(toc){
            epubChapterIndex.push(toc);
            $('.loading-icon').addClass('hidden');
            if(epubChapterHref.length < toc.length) toc.forEach(function(chapter) {
                if(epubChapterHref.indexOf(chapter.href) == -1 ) epubChapterHref.push(chapter.href);
            });

            tocObj = toc;

            var loaderSlogans = ["Expand your mind, not your shelves: switch to ebooks.",
                "Ebooks: countless stories at your fingertips.",
                "Ebooks: the future of reading, today.",
                "Take your library with you: read ebooks!",
                "Carry a library in your pocket with ebooks.",
                "Get lost in a good book, anytime, anywhere with ebooks.",
            ];
            sloganInterval = setInterval(function (){
                if (document.getElementById('sloganTexts')!=null){
                    document.getElementById('sloganTexts').innerHTML = loaderSlogans[Math.round(Math.random()*5)];
                }
            },2500);
            setTimeout(function (){
                $('#loadAnim-2').show();
                $('#loadAnim-1').hide();
            },2500);
            document.querySelector('.lazyLoader').classList.add('loaderHidden');
        });

        if(keyListener == undefined) {
            $('#nextBtnEpub').click(function () {
                epubPageNumber = epubPageNumber + 1;
                totalBookChapter=book.spine.spineItems.length;
                if(epubPageNumber > 0){
                    $('#prevBtnEpub').removeAttr('disabled','disabled');
                }
                for(var b=0;b<book.spine.spineItems.length;b++){
                    if(book.spine.spineItems[b].index == epubPageNumber) {
                        if(epubChapterHref.indexOf(book.spine.spineItems[b].href) > -1) {
                            if(ebupChapterLink != undefined && ebupChapterLink != "" && ebupChapterLink != null ) ebupChapterLink = book.spine.spineItems[b].href;
                        }
                        currentSpineItem = book.spine.spineItems[b].href;
                        rendition.display(book.spine.spineItems[b].href.split("#")[0]);
                    }
                }

                if(totalBookChapter==totalChaptersList){   //For epub file where left side chapterlist and total chapters in book are same
                    //Add active state and remove active state
                    var active = $('.read-book-chapters-wrapper li.orangeText');
                    if (active.is(':last-child')) {
                        $('.read-book-chapters-wrapper li:first-child').addClass('orangeText');
                        active.removeClass('orangeText')
                    }
                    active.next().addClass('orangeText');
                    active.removeClass('orangeText');
                    currChap =  active.next().children()[1].id.match(/\d+/g)[0];
                    localStorage.setItem('lastReadPDF',JSON.stringify({resId:resId,pdfOpen:true,chapterId:currChap,bookId:${params.bookId}}))
                }
                else { //for section base epub file where left side chapterlist is less than total chapters in book
                    var bookChapterList = epubChapterIndex[0].toc;//get chapterlist
                    for (var e = 0; e < bookChapterList.length; e++) {
                        //check chapterlist's chapterLink equal to right side chapterLink
                        if (bookChapterList[e].href.split("#")[0] === currentSpineItem) {
                            $(".read-book-chapters-wrapper li").each(function (id, elem) {
                                //check if chaptername at left side equal to Right side chapter name
                                if (elem.innerText.toUpperCase().trim() == bookChapterList[e].label.toUpperCase().trim()) {
                                    $(".read-book-chapters-wrapper li").removeClass('orangeText');
                                    $(elem).addClass('orangeText');
                                    currChap = $(elem).attr('id').match(/\d+/g)[0];
                                    localStorage.setItem('lastReadPDF',JSON.stringify({resId:resId,pdfOpen:true,chapterId:currChap,bookId:${params.bookId}}))
                                }
                            });
                        }
                    }
                }

                var epubPercentage = Math.round((100 * (epubPageNumber+1) / book.spine.spineItems.length));
                document.querySelector('.epub_footer-pageNum').textContent = epubPageNumber+1;
                document.querySelector('.epub_footer-percentage').innerHTML = '<span> &#x2022; </span>' + epubPercentage + ' %';
                if(footerProgressSlider){
                    footerProgressSlider.value = epubPercentage;
                }
            });

            $('#prevBtnEpub').click(function () {
                if(epubPageNumber > 0) {
                    var epubPercentage = Math.round((100 * (epubPageNumber) / book.spine.spineItems.length));
                    document.querySelector('.epub_footer-pageNum').textContent = epubPageNumber;
                    document.querySelector('.epub_footer-percentage').innerHTML = '<span> &#x2022; </span>' + epubPercentage + ' %';
                    if(footerProgressSlider){
                        footerProgressSlider.value = epubPercentage;
                    }

                    $('#nextBtnEpub').removeAttr('disabled', 'disabled');
                    epubPageNumber = epubPageNumber - 1;
                    for (var b = 0; b < book.spine.spineItems.length; b++) {
                        if (book.spine.spineItems[b].index == epubPageNumber) {
                            if (epubChapterHref.indexOf(book.spine.spineItems[b].href) > -1) {
                                if (ebupChapterLink != undefined && ebupChapterLink != "" && ebupChapterLink != null) ebupChapterLink = book.spine.spineItems[b].href;
                            }
                            currentSpineItem = book.spine.spineItems[b].href;
                            rendition.display(book.spine.spineItems[b].href.split("#")[0]);
                        }
                    }
                    if (totalBookChapter == totalChaptersList) {  //Previous state when prev button clicked
                        var active = $('.read-book-chapters-wrapper li.orangeText');
                        if (active.is(':last-child')) {
                            $('.read-book-chapters-wrapper li:last-child').addClass('orangeText');
                            active.removeClass('orangeText')
                        }
                        active.prev().addClass('orangeText');
                        active.removeClass('orangeText');
                        currChap =  active.prev().children()[1].id.match(/\d+/g)[0];
                        localStorage.setItem('lastReadPDF',JSON.stringify({resId:resId,pdfOpen:true,chapterId:currChap,bookId:${params.bookId}}))
                    }else { //for section base epub file where left side chapterlist is less than total chapters in book
                        var bookChapterList = epubChapterIndex[0].toc;//get chapterlist
                        for (var e = 0; e < bookChapterList.length; e++) {
                            //check chapterlist's chapterLink equal to right side chapterLink
                            if (bookChapterList[e].href.split("#")[0] === currentSpineItem) {
                                $(".read-book-chapters-wrapper li").each(function (id, elem) {
                                    //check if chaptername at left side equal to Right side chapter name
                                    if (elem.innerText.toUpperCase().trim() == bookChapterList[e].label.toUpperCase().trim()) {
                                        $(".read-book-chapters-wrapper li").removeClass('orangeText');
                                        $(elem).addClass('orangeText');
                                        currChap = $(elem).attr('id').match(/\d+/g)[0];
                                        localStorage.setItem('lastReadPDF',JSON.stringify({resId:resId,pdfOpen:true,chapterId:currChap,bookId:${params.bookId}}))
                                    }
                                });
                            }
                        }
                    }
                }
            });

            keyListener = function(e){
                // Left Key
                if ((e.keyCode || e.which) == 37) {
                    epubPageNumber = epubPageNumber - 1;
                    for(var b=0;b<book.spine.spineItems.length;b++){
                        if(book.spine.spineItems[b].index == epubPageNumber) {
                            if(epubChapterHref.indexOf(book.spine.spineItems[b].href) > -1) {
                                if(ebupChapterLink != undefined && ebupChapterLink != "" && ebupChapterLink != null ) ebupChapterLink = book.spine.spineItems[b].href;
                            }
                            currentSpineItem = book.spine.spineItems[b].href;
                            rendition.display(book.spine.spineItems[b].href.split("#")[0]);
                        }
                    }
                }
                // Right Key
                if ((e.keyCode || e.which) == 39) {
                    epubPageNumber = epubPageNumber + 1;
                    for(var b=0;b<book.spine.spineItems.length;b++){
                        if(book.spine.spineItems[b].index == epubPageNumber) {
                            if(epubChapterHref.indexOf(book.spine.spineItems[b].href) > -1) {
                                if(ebupChapterLink != undefined && ebupChapterLink != "" && ebupChapterLink != null ) ebupChapterLink = book.spine.spineItems[b].href;
                            }
                            currentSpineItem = book.spine.spineItems[b].href;
                            rendition.display(book.spine.spineItems[b].href.split("#")[0]);
                        }
                    }
                }

            };
            rendition.on("keyup", keyListener);
        }


        document.addEventListener("keyup", keyListener, false);
        if((siteId==12||siteId==23||siteId==24)){
            window.onbeforeprint = function() {
                const printHeader = document.querySelector('.epub_nav');
                printHeader.style.display = 'none';
                const printFooter = document.querySelector('.epub_footer');
                printFooter.style.display = 'none';
                var iframe = document.querySelector('.epub-container .epub-view iframe')
                iframe.style.width="700px"
                var iframeBodyElement = iframe.contentWindow.document.querySelector('body') || iframe.contentDocument.document.querySelector('body') || iframe.contentDocument.document.body
                iframeBodyElement.classList.add("wid");
            };

            window.onafterprint = function() {
                const printHeader = document.querySelector('.epub_nav');
                printHeader.style.display = 'flex';
                const printFooter = document.querySelector('.epub_footer');
                printFooter.style.display = 'flex';
                var iframe = document.querySelector('.epub-container .epub-view iframe')
                iframe.style.width="100%"
                var iframeBodyElement = iframe.contentWindow.document.querySelector('body') || iframe.contentDocument.document.querySelector('body')
                iframeBodyElement.classList.remove("wid");
            };
        }
    }

    function displayHtmls(data,resId){
        var filename,resourceName,resLink;
        var cData;

        for(i=0;i<chapterDetailsData.length;i++){

            if(readId==chapterDetailsData[i].id){
                filename=chapterDetailsData[i].filename;
                resourceName=chapterDetailsData[i].resName;
                resLink=chapterDetailsData[i].resLink;
            }
        }

        var extraPath = "/OEBPS";
        var isZipBook = filename.toLowerCase().endsWith(".zip") || filename.toLowerCase().endsWith(".pdf");

        var replaceStr = "/funlearn/downloadEpubImage"+
            "?source="+resLink.substring(0,resLink.lastIndexOf('/')+ 1)+"extract"+
            (isZipBook?"":extraPath)+"/"+resSubFileNm.substring(0,resSubFileNm.lastIndexOf("/") +1);


        if("CreatedOnline"==resourceName) resourceName="Chapter Reading Content";
        var htmls ="";


        htmls += "<div class=\"row pb-4 justify-content-center\">";

        if(!(siteId==12||siteId==23||siteId==24 || siteId==28))  {
            htmls += "<a onclick='javascript:closeResourceScreen();' class='pr-back-btn'><i class=\"material-icons mr-1\">\n" +
                "keyboard_backspace\n" +
                "</i>Back</a>";
        }

        if(siteId!=12) {
            htmls += "<h4>" + resourceName + "</h4>";
        } else {
            htmls += "<h4 class='resource_title' style='padding-bottom: 30px;'>" + resourceName + "</h4>";
        }

        htmls +=  "        <div class=\"text-right\"></div>\n" +
            "    </div>"+data;

        $("#notesnavigationsection").show();
        $("#notesnavigation").show();

        //document.getElementById("htmlreadingcontent").height=screen.availHeight;
        $('.loading-icon').addClass('hidden');
        var $iframe = $('#htmlreadingcontent');
        $iframe.ready(function() {
            if(isZipBook) htmls = replaceAll(htmls,"src=\"", "src=\""+replaceStr);
            else if(htmls.search('class="container mcqRead"') == -1){
                htmls = replaceAll(htmls,"src=\"", "src=\""+replaceStr);
            }
            htmls =  replaceAll(htmls,"href=\"", "href=\""+replaceStr);
            htmls =  replaceAll(htmls,"\"../Styles/", "\""+replaceStr+"../Styles/");
            if(isZipBook) htmls = replaceAll(htmls,"data=\"", "data=\""+replaceStr );
            htmls = replaceAll(htmls,"src: url(\"", "src: url(\""+replaceStr);
            htmls = replaceAll(htmls,"background-image: url('", "background-image: url('"+replaceStr);
            //htmls = htmls.replace(/\\\\/g , '\\');
            //htmls = htmls.replace(/\\mathring\ *\{\ *([A-Za-z])\ *\}\ */g , '\\overset\{\\ \\ \\circ\}\{$1\}');

            if(isZipBook) {
                //move the content to left side
                $('#htmlreadingcontent').css({
                    'max-width' : '100%',
                    'padding' : '16px 48px'
                });
                $('.text-format').hide();
            } else {
                $('#htmlreadingcontent').css({
                    //'max-width' : '768px',
                    //'padding' : '16px 0',
                    //'margin':'0 auto'
                });
                $('.text-format').show();
            }

            var __replaceHref = "";



            document.getElementById('htmlreadingcontent').innerHTML = '<div id="htmlContent">'
                +htmls+'</div>';

                $("#htmlreadingcontent").find("a").each(function() {

                    __replaceHref = $(this).attr('href');

                    if(__replaceHref){
                        if(__replaceHref.split('#').length > 1){
                            $(this).attr('href',"#"+__replaceHref.split('#')[1]);

                        }
                        else{
                            $(this).attr('href',"#");
                        }
                    }
                });
                if($('#htmlreadingcontent').hasClass('zaza')){
                    $('#htmlContent').find('.Basic-Paragraph.ParaOverride-1').addClass('fontInherit');
                    $('#htmlContent').find('div._idGenObjectLayout-1A').addClass('moveleft');
                    $('#htmlContent').find('#_idContainer010 > .Basic-Paragraph.ParaOverride-1.fontInherit').addClass('headerOverride');
                    $('#htmlContent').find('#_idContainer006 > .Basic-Paragraph.ParaOverride-1.fontInherit').addClass('handbookoverride');
                    $('#htmlContent').find('#_idContainer001 > .Basic-Paragraph.ParaOverride-1.fontInherit > .CharOverride-6').addClass('bulksales');
                    $('#htmlContent').find('.table-responsive').addClass('red');
                    $('#htmlContent').find('table').each(function () {
                        $('table').wrap('<div class="table-responsives">');
                    });
                }

            if(!isZipBook) {
                renderMathInElement(document.body);
            }
            $('#htmlContent').find('table').each(function () {
                $('table').wrap('<div class="table-responsive">');
            });

        });

        <sec:ifLoggedIn>
        loadAnnotator(readId);
        </sec:ifLoggedIn>
        setTimeout(function (){
            $("#htmlreadingcontent").show();
            document.getElementById("htmlreadingcontent").addEventListener('copy',function (event){
                event.preventDefault();
            })
        },100)

        $("#book-read-material").animate({ scrollTop: 0 });
        $("#notesLoading").hide();
        // htmls = htmls.replace(/\\mathring\ *\{\ *([A-Za-z])\ *\}\ */g , '\\overset\{\\ \\ \\circ\}\{$1\}');
        // //console.log(htmls);
        if(siteId==12||siteId==23||siteId==24) {
            $('#htmlContent').bind('cut copy paste', function(e) {
                e.preventDefault();
            });
        }
    }

    function displaySplittedEpubs(data,resId){
        const chapterIdsArr = []
        const chaptersListItems = document.querySelectorAll('.read-book-chapters-wrapper li')
        chaptersListItems.forEach(li=>{
            const id = li.id.split('chapterName')[1]
            chapterIdsArr.push(Number(id))
        })
        const lazyLoaderHTMl = "<div class='lazyLoader' style='top: 75px !important;'><div class='anim'><div class='slider-anim'><div class='lottieLoader'></div><div style='margin-top: 12px;'> <p style='font-size: 16px;'>Please wait while loading your book...</p></div></div></div></div>"
        let navToolHTML = '<div class=\'epub_nav\'>' +
            '<div class=\'d-flex align-items-center wrap-1\'>' +
            '<div class=\'d-flex align-items-center\' >' +
            '<button class=\'epub_nav-backBtn\' title=\'Back to resources\'>' +
            '<i class=\'fa-solid fa-arrow-left-long\'></i>' +
            '</button>';
        if(!previewMode && genericReader){
            navToolHTML+='<div id="navLeftItem"></div>';
        }
        navToolHTML+='</div>' +
            '</div>' +
            '<div class=\'d-flex align-items-center wrap-2\'>' +
            '<button class=\'btn epubHighlightsBtn d-flex align-items-center\' id=\'epubHighlights\'>' +
            '<i class=\'fa-solid fa-file-pen\'></i>' +
            '</button>';
        if (!previewMode){
            navToolHTML+= '<div class=\'d-flex align-items-center\'>' +
                '<button class=\'btn navBtns\' id=\'prevBtnEpub\' title=\'Previous Page\'>' +
                '<i class=\'fa-solid fa-left-long\'></i></button>' +
                '<button class=\'btn navBtns\' id=\'nextBtnEpub\' title=\'Next Page\'>' +
                '<i class=\'fa-solid fa-right-long\'></i>' +
                '</button>' +
                '</div>';
        }
        navToolHTML+='</div>' +
            '</div>';
        const iframeHTML = "<iframe id='readerFrameTwo' src=\""+serverPath+"/resources/epubReader?bookId=${params.bookId}&resId="+resId+"&chapterId="+masterChapterID+"\" width='100%' style='height: 100vh;border:none'></iframe>"

        const footerToolHTMl = '<div class="epub_footer">' +
            '<div id="previousChapter" style="visibility: hidden">' +
            '</div>' +
            '<div class="ePubFooterWrap" style="width:auto!important;">' +
            '<div class="d-flex justify-content-center align-items-center">' +
            '<p>Page <span class="epub_footer-pageNum" id="footerPageno"></span>' +
            ' of <span class="epub_footer-totalPages" id="epubFooterTotalPages"></span>' +
            '</p>' +
            '</div>' +
            '<div class="" style="display: none">' +
            '<input type="range" id="epubProgressSlider" max="100" min="1" step="1" class="epubProgressSlider">' +
            '</div>' +
            '</div>' +
            '<div id="nextChapter" style="visibility: hidden"></div>' +
            '</div>'
        var epubNotesElement = "<section class='notesWrapper displayOpenClose' style='z-index: 4'>"+
            "<div class='notesDisplay'>"+
            "<div class='notesDisplay_header'>"+
            "<h3 style='width: 100%;text-align: center'>Highlights</h3>"+
            "<button class='close'><i class='fa-solid fa-xmark'></i></button>"+
            "</div>"+
            "<div class='notesDisplay_body'>"+
            "<ul class='notesList'></ul>"+
            "</div>"+
            "</div>"+
            "</section>";

        const readerHTMl = lazyLoaderHTMl+navToolHTML+epubNotesElement+iframeHTML+footerToolHTMl
        $("#reader").html(readerHTMl);
        $('.loading-icon').addClass('hidden');
        $('.epub-action').css('display','flex');
        $("#htmlreadingcontent").show();
        $("#htmlreadingcontent").html("");
        $('#chapter-actions').removeClass('d-lg-flex');
        if($(window).width()<767) {
            $('#chapter-actions').removeClass('d-flex').addClass('d-none');
            $('.generateTest').addClass('d-none');
        }
        $('.chapter-notes,#print-all-action,#chapter-all-action').hide();
        if($(window).width()<767){
            $('#chapter-details-tabs').width('80%');
            $('.contentEdit').width('100px');
            $('.shadowHeader').height('45px');
        }

        $('.bookTemplate,header').hide();
        document.getElementById('reader').classList.add('readerAnimation');

        document.querySelector('.epub_nav-backBtn').addEventListener('click',function (){
            if(genericReader){
                history.back();
            }else {
                if ($(window).width() < 768){
                    $('.bookTemplate').show();
                    if((siteId != 12 && siteId != 23 && siteId != 24) ){
                        $("#content-data-all").show();
                    }else{
                        $("#downloadChapter").removeClass('d-flex').addClass('d-none');
                    }
                    $("#htmlreadingcontent").hide();
                    $('#overlay,.export-notes').addClass('d-none');
                    $('.contentEdit').width('0');
                }else{
                    $('.bookTemplate,header').show();
                    if((siteId != 12 && siteId != 23 && siteId != 24) ){
                        $("#content-data-all").show();
                    }else{
                        $("#content-data-all").hide();
                        $("#downloadChapter").removeClass('d-flex').addClass('d-none');
                    }
                    $("#htmlreadingcontent").hide();
                    $('#overlay,.export-notes').addClass('d-none');
                }
                document.getElementById('reader').classList.remove('readerAnimation');
                keyListener=undefined;
                if (siteId!= "12" && siteId!= "23" && siteId!= "24"){
                    if (data.isSingleEpubFile == true){
                        getChapterDetails(currChap)
                    }
                }
                saveEpubDetails(resId,epubPageNumber);
            }
        });


        const nextBtnEpub = document.getElementById('nextBtnEpub')
        const prevBtnEpub = document.getElementById('prevBtnEpub')
        const footerPageno = document.getElementById('footerPageno')
        const epubHighlights = document.getElementById('epubHighlights')
        const navLeftItem = document.getElementById('navLeftItem')
        const epubFooterTotalPages = document.getElementById('epubFooterTotalPages')
    }
    function hideLazyLoader(){
        document.querySelector('.lazyLoader').classList.add('loaderHidden');
    }

    function showLazyLoader(){
        document.querySelector('.lazyLoader').classList.remove('loaderHidden');
    }
    function displayPDF(resId,title){
        //  $("#notesnavigationsection").show();
        //   $("#notesnavigation").show();

        //document.getElementById("htmlreadingcontent").height=screen.availHeight;
        if(allTabMode) {
            $('#chapter-details-tabs a[href="#read"]').tab('show');
        }
        $("#content-data-readingMaterial").hide();

        $("#notesLoading").show();
        $('.loading-icon').addClass('hidden');
        document.getElementById('htmlreadingcontent').innerHTML ="<a href='javascript:closeReadScreen()' class='pr-back-btn'><i class=\"material-icons\">\n" +
            "keyboard_arrow_left\n" +
            "</i>Back</a>"+
            "        <div class=\"col-md-1 text-right\"></div>\n" +
            "    </div>"+
            "<div class='container'> "+
            "<div class='row mt-4'> "+
            " <div class='col-md-12'>"+
            "<div class='embed-responsive' style='padding-bottom:150%'>"+
            "<object data='/funlearn/download?id="+resId+"' type='application/pdf'  />"+
            "</object>"+
            "</div>"+
            "</div>"+
            "</div>"+
            "</div>";
        $("#htmlreadingcontent").show();
        $("#book-read-material").animate({ scrollTop: 0 });

        $("#notesLoading").hide();

        if(loggedInUser){
            if(allTabMode)
                updateUserView(resId,"all","read");
            else
                updateUserView(resId,"read","read");
        } else {
            if(allTabMode)
                updateView(resId,"all","read");
            else
                updateView(resId,"read","read");
        }

    }

    //showing pdf documents

    function displayPdfReadingMaterial(pdfLink,allowDownload,id,zoomLevelTwo,resName){
        if(loggedInUser) updateUserView(id,"all","read");
        else updateView(id,"all","read");
        hideMainAndDisplayResource(id);
        localStorage.setItem('lastReadPDF',JSON.stringify({resId:id,pdfOpen:true,chapterId:masterChapterID,bookId:${params.bookId}}))
        if(allowDownload != "yes") {

            var pdfKey = "${encryptPdfKey}";
            var bookId=0;
            <% if(params.bookId == null || params.bookId == '') { %>
            bookId=urlBookId;
            <% } else {%>
            bookId="${params.bookId}";
            <%}%>
            $('#notesMenu').on('click',function () {
                <g:remoteFunction controller="wonderpublish" action="annotateSearch" params="'limit=100&all_fields=1&uri='+readId+'&bookId='+bookId" onSuccess = "showNotesAndHighlightList(data);"/>
            });

            //Zoom Level for yes no pdf
            if (zoomLevelTwo !=null || zoomLevelTwo !=""){
                if (zoomLevelTwo == "1"){
                    defaultZoom = 130;
                }else if (zoomLevelTwo == "2"){
                    defaultZoom = 160;
                }else if (zoomLevelTwo == "3"){
                    defaultZoom = 190;
                }

                if($(window).width()<768){
                    defaultZoom = 48;
                }
            }else {
                defaultZoom = 100;
            }

            var iframeId;
            bookTitle = resName;
            if($(window).width()<768){
                mobileView = true;
            }
            if(siteId != 12 && siteId != 23 && siteId != 24 && siteId!=28) {
                $("#reader").html("<iframe id='wSecondIframe' src=\""+serverPath+"/wonderpublish/pdfReader?bookLang="+bookLang+"&resId="+id+"&bookId="+bookId+"&bookUrl="+serverPath+"/funlearn/getPdfFile?resId="+id+"__encryptedKey="+pdfKey+"&chapterId="+masterChapterID+"&title="+bookTitle+"&mobileView="+mobileView+"#zoom="+defaultZoom+"\" width='100%' style='height: 100vh;' ></iframe>");
                iframeId = 'wSecondIframe'
            } else {
                iframeId = 'pr';
                $("#reader").html("<iframe id='pr' src=\""+serverPath+"/wonderpublish/pdfReader?bookLang="+bookLang+"&resId="+id+"&bookId="+bookId+"&bookUrl="+serverPath+"/funlearn/getPdfFile?resId="+id+"__encryptedKey="+pdfKey+"&chapterId="+masterChapterID+"&title="+bookTitle+"&mobileView="+mobileView+"#zoom="+defaultZoom+"\" width='100%' style='height: 100vh;' ></iframe>");
            }

            $('.bookTemplate,header').hide();
            document.getElementById('reader').classList.add('readerAnimation');
            document.querySelector('body').removeAttribute('style');
            document.querySelector('body').setAttribute('style','position: relative;min-height: 100%;top: 0px;overflow: hidden;');
            $("#notesnavigationsection").show();
            $("#notesnavigation").show();
            $('.loading-icon').addClass('hidden');
            $("#htmlreadingcontent").show();


        }
        else if(allowDownload == "yes") {
            displayDownloadablePdf(id);
        }
        if($(window).width()<767){
            $('#chapter-details-tabs').width('80%');
            $('.contentEdit').width('100px');
            $('.shadowHeader').height('45px');
        }
    }
    function getEncodedPdfDataUsingAjax(url,pdfLink,id){
        $.ajax({
            url : url,
            type : 'GET',
            tryCount : 0,
            retryLimit : 3,
            success : function(data) {
                joinEncodedStringPartsAndDisplay(data,pdfLink,id);
            },
            error : function(xhr, textStatus, errorThrown ) {
                this.tryCount++;
                if (this.tryCount <= this.retryLimit) {
                    //try again
                    $.ajax(this);
                    return;
                }else{
                    <g:remoteFunction controller="funlearn" action="getEncodedPdf" onSuccess="displayEncodedPdf(data)" params="'link='+pdfLink+'&id='+id" />
                }
            }
        });
    }

    function displayDownloadablePdf(id){

        if(siteId==12||siteId==23||siteId==24) {
            if($(window).width()<767){
                document.getElementById('htmlreadingcontent').innerHTML = "<div class='w-100'>" +
                    "<div class='text-center'><a target='_blank' href=\"/wonderpublish/downloadEncodedPdf?id="+id+"\" style=\"width: 100%;\" class='btn btn-primary download mt-4'>Download</a>"+
                    "</div>"+
                    "</div>";
            } else {
            document.getElementById('htmlreadingcontent').innerHTML = "<embed src=\"/wonderpublish/downloadEncodedPdf?id=" + id + "\" type=\"application/pdf\"   height=\"700px\" width=\"800\">";
            }
        } else {
            var readerViewElement = document.getElementById('reader');
            readerViewElement.style.height = '100vh';
            readerViewElement.classList.add('readerAnimation');
            $('.bookTemplate,header').hide();
            if($(window).width()<767){
                if(siteId==28) {
                    document.getElementById('reader').innerHTML = "<div class='w-100'>" +
                        "<div class='text-center'><a href='javascript:downloadAppInAppPDF("+id+")' style=\"width: 100%;\" class='mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect download mt-4'>Download</a>" +
                        "</div>" +
                        "</div>";
                } else {
                    document.getElementById('reader').innerHTML = "<div class='w-100'>" +
                        "<div class='text-center'><a target='_blank' href=\"/wonderpublish/downloadEncodedPdf?id=" + id + "\" style=\"width: 100%;\" class='mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect download mt-4'>Download</a>" +
                        "</div>" +
                        "</div>";
                }
            } else {
            document.getElementById('reader').innerHTML = "<div class='row'><button onclick='javascript:closePDFDnw();' class='btn btn-back my-3 d-flex align-items-center' style='background: none;'>" +
                "<i class='material-icons mr-1'>keyboard_backspace</i> Back</button></div>" +
                "<embed src=\"/wonderpublish/downloadEncodedPdf?id=" + id + "\" type=\"application/pdf\"   height=\"100%\" width=\"100%\">";
            }
        }
        $("#htmlreadingcontent").show();
        // '<iframe id="fraDisabled" src="data:application/pdf;base64,'+resData.encodedString+'"+></iframe>' ;
        // '<object  style="position:relative" data="data:application/pdf;base64,'+resData.encodedString+'" type="application/pdf" ></object>' ;
        $('.color-mode-list-item:first').addClass('active');
        $('#formatMenu').show();
        $('#notesMenu').show();
        $('.section-btns').show();
        if($(window).width()<767){
            $('#chapter-details-tabs').width('80%');
            $('.contentEdit').width('100px');
            $('.shadowHeader').height('45px');
        }

    }
    function displayEncodedPdf(resData) {
        var encodedPdfHtmlStr = "<div>\n" +
            "    <button id=\"prev\">Previous</button>\n" +
            "    <button id=\"next\">Next</button>\n" +
            "    &nbsp; &nbsp;\n" +
            "    <span>Page: <span id=\"page_num\"></span> / <span id=\"page_count\"></span></span>\n";
        if(resData.allowDownload == "yes") encodedPdfHtmlStr = encodedPdfHtmlStr+"    <button onclick=\"downLoadEncodedPdf('"+resData.id+"')\">Download PDF</button>\n"
        encodedPdfHtmlStr = encodedPdfHtmlStr+  "</div>" +
            "<canvas id=\"the-canvas\"></canvas>";
        if(!appInApp) {
            document.getElementById('htmlreadingcontent').innerHTML = encodedPdfHtmlStr;
        } else {
            document.getElementById('htmlreadingcontent').innerHTML = setBackButton() + encodedPdfHtmlStr;
        }
        $("#htmlreadingcontent").show();
        // '<iframe id="fraDisabled" src="data:application/pdf;base64,'+resData.encodedString+'"+></iframe>' ;
        // '<object  style="position:relative" data="data:application/pdf;base64,'+resData.encodedString+'" type="application/pdf" ></object>' ;
        $('.color-mode-list-item:first').addClass('active');
        $('#formatMenu').show();
        $('#notesMenu').show();
        $('.section-btns').show();
        if($(window).width()<767){
            $('#chapter-details-tabs').width('80%');
            $('.contentEdit').width('100px');
            $('.shadowHeader').height('45px');
        }
        // If absolute URL from the remote server is provided, configure the CORS
        // header on that server.

        var pdfData = atob(resData.encodedString);

        // var url = '/assets/wonderslate/Percentage1.pdf';

        // Loaded via <script> tag, create shortcut to access PDF.js exports.
        // var pdfjsLib = window['/assets'];

        // The workerSrc property shall be specified.
        // pdfjsLib.GlobalWorkerOptions.workerSrc = 'http://localhost:8080/assets/pdf.worker.js';

        var pdfDoc = null,
            pageNum = 1,
            pageRendering = false,
            pageNumPending = null,
            scale = 2,
            canvas = document.getElementById('the-canvas'),
            ctx = canvas.getContext('2d');

        /**
         * Get page info from document, resize canvas accordingly, and render page.
         * @param num Page number.
         */
        function renderPage(num) {
            pageRendering = true;
            // Using promise to fetch the page
            pdfDoc.getPage(num).then(function(page) {
                var viewport = page.getViewport({scale:scale});
                canvas.height = viewport.height;
                canvas.width = viewport.width;

                // Render PDF page into canvas context
                var renderContext = {
                    canvasContext: ctx,
                    viewport: viewport
                };
                var renderTask = page.render(renderContext);
                // var screenWidth = (window.innerWidth > 0) ? window.innerWidth : screen.width;
                // var sideBarWidth = document.getElementById("book-sidebar").offsetWidth;
                var containerWidth = $(".tab-content").width();
                var canvasWidth = (90/100) * (containerWidth);
                $('#the-canvas').css('width',canvasWidth);
                // Wait for rendering to finish
                renderTask.promise.then(function() {
                    pageRendering = false;
                    if (pageNumPending !== null) {
                        // New page rendering is pending
                        renderPage(pageNumPending);
                        pageNumPending = null;
                    }
                });
            });

            // Update page counters
            document.getElementById('page_num').textContent = num;
        }

        /**
         * If another page rendering in progress, waits until the rendering is
         * finised. Otherwise, executes rendering immediately.
         */
        function queueRenderPage(num) {
            if (pageRendering) {
                pageNumPending = num;
            } else {
                renderPage(num);
            }
        }

        /**
         * Displays previous page.
         */
        function onPrevPage() {
            if (pageNum <= 1) {
                return;
            }
            pageNum--;
            queueRenderPage(pageNum);
        }
        document.getElementById('prev').addEventListener('click', onPrevPage);

        /**
         * Displays next page.
         */
        function onNextPage() {
            if (pageNum >= pdfDoc.numPages) {
                return;
            }
            pageNum++;
            queueRenderPage(pageNum);
        }
        document.getElementById('next').addEventListener('click', onNextPage);

        /**
         * Asynchronously downloads PDF.
         */
        pdfjsLib.getDocument({data: pdfData}).promise.then(function(pdfDoc_) {
            pdfDoc = pdfDoc_;
            document.getElementById('page_count').textContent = pdfDoc.numPages;

            // Initial/first page rendering
            renderPage(pageNum);
        }).catch(function(e) {
            //console.log(e);
        });
        $(document).on({
            "contextmenu": function(e) {
                //console.log("ctx menu button:", e.which);

                // Stop the context menu
                e.preventDefault();
            },
            "mousedown": function(e) {
                // //console.log("normal mouse down:", e.which);
            },
            "mouseup": function(e) {
                ////console.log("normal mouse up:");
                ////console.log(e)
            }
        });

    }
    var encodedStringPartsObj = {}
    function joinEncodedStringPartsAndDisplay(resData,pdfLink,id){
        encodedStringPartsObj["'"+resData.requestNo+"'"] = resData.shortMessage;
        var checkAllParts = 1;
        for(var i=1;i<=10;i++){
            if(encodedStringPartsObj["'"+i+"'"] == undefined) checkAllParts = 0;
        }
        if(checkAllParts == 1) {
            var msg = "";
            for(var i=1;i<=10;i++){
                msg = msg + encodedStringPartsObj["'"+i+"'"];
            }
            displayEncodedPdf({
                "encodedString":msg,
                "id":resData.id,
                "pdfLink":pdfLink
            });
        }
    }

    function downLoadEncodedPdf(id){
        <g:remoteFunction controller="wonderpublish" action="downloadEncodedPdf"  params="'id='+id" />
    }

    //support functions
    function hideMainAndDisplayResource(id){
        $("#content-data-all").hide();
        $("#htmlreadingcontent").show();
        showLoadingShimmer("htmlreadingcontent");
        readId = id;
    }
    function showLoadingShimmer(divId){
        var shimmerStr="<div class=\"container\">\n" +
            "                    <div class=\"content-wrapper mt-20\">\n" +
            "                        <div class=\"content row flexAlign\">\n" +
            "                            <photo class=\"shine col-md-3 col-sm-6 col-xs-12\" ></photo>\n" +
            "                        </div>\n" +
            "                    </div>\n" +
            "                    <br>";
        for(var i=0;i<9;i++){
            shimmerStr +="<div class=\"content-wrapper\">\n" +
                "                        <div class=\"content row flexAlign\">\n" +
                "                            <div class=\"line-wrapper col-md-6 col-sm-12 col-xs-12\">\n" +
                "                                <lines class=\"shine\"></lines>\n" +
                "                                <lines class=\"shine\"></lines>\n" +
                "                                <lines class=\"shine\"></lines>\n" +
                "                            </div>\n" +
                "                        </div>\n" +
                "                    </div>\n" +
                "                    <br>";
        }
        shimmerStr +="</div>\n" +
            "            </div>";

        document.getElementById(divId).innerHTML = shimmerStr;

    }

    function changeColor(field) {
        $(field).siblings().removeClass('active');
        $(field).addClass('active');
        $('#book-read-material').removeClass().addClass('book-read-material'+' '+($(field).attr('id')));
    }

    function hideTextFormatter() {
        $('.color-mode-list-item').removeClass('active');
        $('#formatMenu').hide();
        $('#notesMenu').hide();
        $('.section-btns').hide();
        $('#book-read-material').removeClass('white-bg black-bg sepia-bg grey-bg').addClass('book-read-material'+' ');
    }

    function showTextFormatter() {
        $('.color-mode-list-item:first').addClass('active');
        $('#formatMenu').show();
        $('#notesMenu').show();
        $('.section-btns').show();
        if($(window).width()<767){
            $('#chapter-details-tabs').width('80%');
            $('.contentEdit').width('100px');
            $('.shadowHeader').height('45px');
        }
    }

    function decreaseLineHeight() {
        $('#htmlreadingcontent').css({
            'line-height' : 'normal'
        });
        $('#htmlreadingcontent iframe').contents().find('body').css({
            'line-height' : 'normal'
        });
    }

    function increaseLineHeight() {
        $('#htmlreadingcontent').css({
            'line-height' : '24px'
        });
        $('#htmlreadingcontent iframe').contents().find('body').css({
            'line-height' : '24px'
        });
    }

    function decreaseLetterSpacing() {
        $('#htmlreadingcontent').css({
            'letter-spacing' : 'normal'
        });
        $('#htmlreadingcontent iframe').contents().find('body').css({
            'letter-spacing' : 'normal'
        });
    }

    function increaseLetterSpacing() {
        $('#htmlreadingcontent').css({
            'letter-spacing' : '0.04em'
        });
        $('#htmlreadingcontent iframe').contents().find('body').css({
            'letter-spacing' : '0.04em'
        });
    }

    function replaceAll(str, find, replace) {
        return str.replace(new RegExp(escapeRegExp(find), 'g'), replace);
    }

    function escapeRegExp(str) {
        return str.replace(/([.*+?^=!:$\{\}()|\[\]\/\\])/g, "\\$1");
    }
    function closeReadScreen(){
        $("#htmlreadingcontent").hide();
        $('#content-data-all').show();

    }

    $(document).keydown(function(event) {
        if((siteId==12||siteId==23||siteId==24)) {
            if (event.ctrlKey === true && (event.which == '65') || event.metaKey === true && (event.which == '65')) {
                return false;
            }
            if (event.ctrlKey === true && (event.which == '67') || event.metaKey === true && (event.which == '67')) {
                return false;
            }
            if (event.ctrlKey === true && (event.which == '86') || event.metaKey === true && (event.which == '86')) {
                return false;
            }
            if (event.ctrlKey === true && (event.which == '88') || event.metaKey === true && (event.which == '88')) {
                return false;
            }
        }
    });

    <%if("3".equals(""+session["siteId"]) || "37".equals(""+session["siteId"]) || "38".equals(""+session["siteId"]) || "39".equals(""+session["siteId"]) || "46".equals(""+session["siteId"])){%>
    if ('matchMedia' in window) {
        // Chrome, Firefox, and IE 10 support mediaMatch listeners
        window.matchMedia('print').addListener(function(media) {
            if (media.matches) {
                beforeBookPrint();
            } else {
                // Fires immediately, so wait for the first mouse movement
                $(document).one('mouseover', afterBookPrint);
            }
        });
    } else {
        // IE and Firefox fire before/after events
        $(window).on('beforeprint', beforeBookPrint);
        $(window).on('afterprint', afterBookPrint);
    }
    function beforeBookPrint() {
        $("html,body").hide();
    }
    function afterBookPrint() {
        $("html,body").show();
    }
    <%}%>

    function downloadAppInAppPDF(id) {
        var origin = window.location.origin;
        var downloadLink = origin+"/wonderpublish/downloadEncodedPdf?id="+id;
        JSInterface.onWebLinkClicked(downloadLink);
    }

    function closePDFDnw(){
        document.getElementById('reader').classList.remove('readerAnimation');
        $('.bookTemplate,header').show();
        $("#content-data-all").show();
        $("#htmlreadingcontent").hide();
        $('#allAddButton').show();
        if($(window).width()<767) {
            $('.contentEdit').width('0');
        }
        $('.shadowHeader').height('0');
        $(".price-wrapper").show();
        $('.prevnextbtn').addClass('d-none').removeClass('d-flex');
        $('#overlay,.export-notes').addClass('d-none');
    }


    function viewChapterNotesEpub(resId,bookID,chapterIdForPDF) {
        var paramsList  = ""
        paramsList  = 'limit=100&all_fields=1&uri='+resId+'&bookId='+bookID+'&ebupChapterLink='+ebupChapterLink
        <g:remoteFunction controller="wonderpublish" action="annotateSearch"  onSuccess='renderChapterNotesEpub(data);' params="paramsList" />
    }

    function renderChapterNotesEpub(data){
        var notesData = data.rows;
        var notesHTML = "";

        let borderCol=''
        if (notesData.length>0){
            for (var i=0;i<notesData.length;i++){
                if (notesData[i].annotateColor!=undefined&&notesData[i].annotateColor!=null&&notesData[i].annotateColor!=""){
                    if (notesData[i].annotateColor == 'annotateColorYellow'){
                        borderCol = 'yellowBorder'
                    }else if(notesData[i].annotateColor=='annotateColorGreen'){
                        borderCol = 'greenBorder'
                    }else if(notesData[i].annotateColor == 'annotateColorBlue'){
                        borderCol = 'blueBorder'
                    }
                }
                if (notesData[i].text==null){
                    notesHTML+= "<li class='"+borderCol+"'>" +
                        "<p>Highlight</p>"+
                        "<a style='font-size: 16px'>"+(i+1)+". "+notesData[i].quote+"</a>" +
                        "</li>";
                }else{
                    notesHTML+= "<li class='"+borderCol+"'>" +
                        "<p>Note</p>"+
                        "<a style='font-size: 16px'>"+(i+1)+". "+notesData[i].quote+"</a>" +
                        "<p style='margin-left: 15px;margin-top: 10px;word-break: break-all;font-size: 15px'>"+notesData[i].text+"</p>"+
                        "</li>";
                }
            }
        } else{
            notesHTML+="No Highlights found."
        }
        document.querySelector('.notesList').innerHTML = notesHTML;
        epubOpenCloseNotesWindow();
    }

    function epubOpenCloseNotesWindow(){
        var notesWrapper = document.querySelector('.notesWrapper');
        notesWrapper.classList.toggle('openPopupWindow');
        notesWrapper.classList.toggle('displayOpenClose');
        if (notesWrapper.classList.contains('openPopupWindow')){
            notesWrapper.addEventListener('click',function (){
                epubOpenCloseNotesWindow()
            })
        }
    }

    function saveEpubDetails(resIdVal,epubPageNumber){
        var lastReadDetailsArr = [];
        var oldLastReadDetails = localStorage.getItem('lastReadDetails');
        var lastReadDetailsObj = {
            resId:resIdVal,
            pageNo:epubPageNumber
        }

        //localStorage.setItem('lastReadPDF',JSON.stringify({resId:resIdVal,pdfOpen:false,chapterId:"${params.chapterId}"}))
        if (oldLastReadDetails){
            oldLastReadDetails =  JSON.parse(oldLastReadDetails);
            oldLastReadDetails.map(item=>{
                if (item.resId == resIdVal){
                    item.pageNo = epubPageNumber;
                }else{
                    oldLastReadDetails.push(lastReadDetailsObj)
                }
            });
            oldLastReadDetails = Array.from(new Set(oldLastReadDetails.map(JSON.stringify))).map(JSON.parse);
            localStorage.setItem('lastReadDetails',JSON.stringify(oldLastReadDetails));
        }else{
            lastReadDetailsArr.push(lastReadDetailsObj)
            localStorage.setItem('lastReadDetails',JSON.stringify(lastReadDetailsArr));
        }
    }

    function openSpecificEPubPage(data,book,pageNo){
        for(var b=0;b<book.spine.spineItems.length;b++){
            if(book.spine.spineItems[b].index == pageNo) {
                if(epubChapterHref.indexOf(book.spine.spineItems[b].href) > -1) {
                    if(ebupChapterLink != undefined && ebupChapterLink != "" && ebupChapterLink != null ) ebupChapterLink = book.spine.spineItems[b].href;
                }
                currentSpineItem = book.spine.spineItems[b].href;
                rendition.display(book.spine.spineItems[b].href.split("#")[0]);
            }
        }

        var epubPercentage = Math.round((100 * (pageNo+1) / book.spine.spineItems.length));
        document.querySelector('.epub_footer-pageNum').innerHTML = pageNo+1;
        document.querySelector('.epub_footer-percentage').innerHTML = '<span> &#x2022; </span>' + epubPercentage + ' %';
        if(document.getElementById('epubProgressSlider')){
            document.getElementById('epubProgressSlider').value = epubPercentage;
        }

    }

    function closePDFReaderView(){
        if ($(window).width() < 768){
            $('.bookTemplate').show();
            if((siteId != 12 && siteId != 23 && siteId != 24) ){
                $("#content-data-all").show();
            }else{
                $("#downloadChapter").removeClass('d-flex').addClass('d-none');
            }
            $("#htmlreadingcontent").hide();
            $('#overlay,.export-notes').addClass('d-none');
            $('.contentEdit').width('0');
        }else{
            $('.bookTemplate,header').show();
            if((siteId != 12 && siteId != 23 && siteId != 24) ){
                $("#content-data-all").show();
            }else{
                $("#downloadChapter").removeClass('d-flex').addClass('d-none');
            }
            $("#htmlreadingcontent").hide();
            $('#overlay,.export-notes').addClass('d-none');
        }

        document.getElementById('reader').classList.remove('readerAnimation');
        document.querySelector('body').removeAttribute('style');
        document.querySelector('body').setAttribute('style','position: relative;min-height: 100%;top: 0px;overflow: auto;'); if ($(window).width() < 768){
            $('.bookTemplate').show();
            if((siteId != 12 && siteId != 23 && siteId != 24) ){
                $("#content-data-all").show();
            }else{
                $("#downloadChapter").removeClass('d-flex').addClass('d-none');
            }
            $("#htmlreadingcontent").hide();
            $('#overlay,.export-notes').addClass('d-none');
            $('.contentEdit').width('0');
        }else{
            $('.bookTemplate,header').show();
            if((siteId != 12 && siteId != 23 && siteId != 24) ){
                $("#content-data-all").show();
            }else{
                $("#downloadChapter").removeClass('d-flex').addClass('d-none');
            }
            $("#htmlreadingcontent").hide();
            $('#overlay,.export-notes').addClass('d-none');
        }

        document.getElementById('reader').classList.remove('readerAnimation');
        document.querySelector('body').removeAttribute('style');
        document.querySelector('body').setAttribute('style','position: relative;min-height: 100%;top: 0px;overflow: auto;');
    }

    function openNextPDFResource(item){
        closePDFReaderView();
        if(item.sharing==null) {
            if (item.resLink.includes(".pdf")) {
                displayPdfReadingMaterial(item.resLink ,item.videoPlayer,item.id,item.zoomLevel,item.resName,item.topicId);
            } else {
                displayReadingMaterial(item.id,item.resName);
            }
        }else{
            if (item.quizMode == "file") {
                if (item.resLink.includes(".pdf")) {
                    displayPDFNotes(item.id,item.resName);
                }else {
                    downloadFile(item.id);
                }
            } else {
                displayReadingMaterial(item.id,item.resName);
            }
        }
    }
    document.addEventListener('keydown', event => {
        if (event.ctrlKey || event.metaKey && event.key === 's') {
            event.preventDefault();
            event.stopPropagation();
            return false;
        }
    },false);
</script>