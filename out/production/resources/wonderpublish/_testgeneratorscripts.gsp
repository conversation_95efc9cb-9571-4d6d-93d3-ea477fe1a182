
<style>
    .test-gen-box-main  {
        display: none;
    }
    .book-content-wrapper {
        list-style-type: none;
    }
    .book-image-wrapper {
        display: flex;
        justify-content: center;
    }
</style>

<asset:javascript src="clock.js"/>
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS_HTML-full" async>
</script>
<script>
    $('#score-container').on('click', '.analysis-book-name', function() {
        $('.analysis-book-name').children('.icon-chevron').removeClass('rotated-i');
        $(this).children('.icon-chevron').toggleClass('simple rotated-i');
    });
</script>
<script>
    var bookIds = new Set();
    var currentNumber=1;
    var testingMode="";
    var booksMap;
    var noOfChapters=0;
    var quizType='';
    var noOfDifficultyLevels=0;
    var noOfQuestions=0;
    var difficultyLevel;
    var difficultyLevels="";
    var testMode;
    var testName="";
    var testBatchId="";

    var selectedChapters = [];

    function startTestGenerator(mode){

        //initializations
        testMode = mode;

        $('.previous-btn').hide();
        $('#error_message').hide();
        testingMode = testMode;
        $("#divi4").fadeOut();
        $("#divi5").fadeOut();
        $("#divi6").fadeOut();
        // $("#divi3").fadeIn(100);
        // $("#divi3").show();

        noOfQuestions=0;
        document.getElementById("noOfQuestions").disabled=true;
        document.getElementById("noOfQuestions").value="";
        document.getElementById("next-button").innerText="Next";
        document.getElementById("divi2").innerHTML="";
        document.getElementById("divi3").innerHTML="";
        document.getElementById("divi4").innerHTML="";
        document.getElementById("divi5").innerHTML="";

        document.getElementById("noofquestionslabel").innerHTML = "Number of questions";
        //$("#divi2").fadeOut();
        currentNumber = 1;
        getTGUserBooks();
        <%if(institutes!=null&&institutes.size()>0){%>
        $("#testInputDev").hide();
        $(".previous-btn").show();
        <%}%>


    }
    function getTGUserBooks(){
        $("#loading").show();
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="onlineTest" action="getUserBooksForTestGenerator" params="'batchId='+testBatchId+'&subject='+subject" onSuccess='displayTGBooks(data);'/>

    }
    function displayTGBooks_new(data){
        $("#loading").hide();
        var books = data.books;
        noOfBooks = books.length;
        var imgSrc = "";
        bookIds = new Set();
        booksMap = {};

        var displayStr="";

        displayStr = "<div class='container books-content-wrapper'>"+
            "<div class='row'>";
        for(i=0;i<noOfBooks;i++){
            booksMap[books[i].id] = books[i].title;
            if (books[i].coverImage === null || books[i].coverImage == "null" || books[i].coverImage == "") {
                imgSrc = "/assets/booksmojo/img_cover_placeholder.png";
            } else {
                imgSrc = "/funlearn/showProfileImage?id="+books[i].id+"&fileName="+books[i].coverImage+"&type=books&imgType=passport";
            }


            displayStr+= "<div class='col-6 col-md-4 col-lg-3 pb-2'>" +
                "<a class='book-item-link' href='javascript:bookSelected(this,"+books[i].id+");'  id='id_"+books[i].id+"'>"+
                "<div class='card book-image-wrapper'>" +
                "<img src='"+imgSrc+"' alt=''/>" +
                "<div class='overlay-testgen-book'>" +
                "<div class='book-selected'>";
            displayStr+= "<i class='material-icons'>check_box</i>";

            displayStr+= "</div>" +
                " </div>" +
                "<div class='card-body'>" +
                "<p class='card-text'>"+books[i].title+"</p>" +
                "</div>" +
                "</div>" +
                "</a>" +
                "</div>";

        }

        displayStr += "</div>"+"</div>";
        document.getElementById("divi2").innerHTML=displayStr;
        currentNumber=2;
        $('#error_message').hide();
        $("#divi2").fadeIn(100);
    }

    function displayTGBooks(data){
        $("#loading").hide();
        var books = data.books;
        noOfBooks = books.length;

        var imgSrc = "";
        bookIds = new Set();
        booksMap = {};

        var displayStr="";

        if(noOfBooks>0) {
            $(".testgen-next-pre").attr('style','display: flex !important');
        } else {
            $(".testgen-next-pre").attr('style','display: none !important');
        }


        displayStr +=  "<h5 class='stpe-count text-left pl-3 mb-3'>Step 1 of 3: Select eBook/eBooks to generate test from.</h5>";

        displayStr +="          <ul class='test-gen-books-flex d-flex justify-content-start flex-wrap w-100 pl-0'>";

        for(i=0;i<noOfBooks;i++){
            booksMap[books[i].id] = books[i].title;
            if (books[i].coverImage === null || books[i].coverImage == "null" || books[i].coverImage == "") {
                imgSrc = "/assets/booksmojo/img_cover_placeholder.png";
            } else {
                if (books[i].coverImage.startsWith("https")) {
                    imgSrc = books[i].coverImage;
                    imgSrc = imgSrc.replace("~", ":");
                }
                else imgSrc = "/funlearn/showProfileImage?id="+books[i].id+"&fileName="+books[i].coverImage+"&type=books&imgType=passport";
            }

            displayStr+= "<li class='col-6 col-md-3 d-block justify-content-center mb-md-4 mb-3 book-content-wrapper'>\n";

            displayStr+="              <div class='book-item book-item-test-gen'>\n" +
                "                <a class='book-item-link' href='javascript:bookSelected(this,"+books[i].id+");'  id='id_"+books[i].id+"'>\n" +
                "                  <div class='book-image-wrapper'>\n" +
                "                    <img class='book-image' src='"+imgSrc+"' alt=''/>\n" +
                "                    <div class='overlay-testgen-book'>\n" +
                "                      <div class='book-selected'>\n";
            displayStr+="<i class=\"material-icons text-radial-gradient\">\n" +
                "check_box\n" +
                "</i>";

            displayStr+="                      </div>\n" +
                "                    </div>\n" +
                "                  </div>\n" +
                "                 <div class='book-info'>\n" +
                "                   <div class='book-name-author'>\n" +
                "                     <p class='book-name book-full-name'>"+books[i].title+"</p>\n" +
                "                   </div>\n" +
                "                 </div>" +
                "                </a>\n" +
                "              </div>\n" +
                "            </li>";

        }

        displayStr += "</ul>";
        document.getElementById("divi2").innerHTML=displayStr;
        currentNumber=2;
        $('#error_message').hide();
        $("#divi2").fadeIn(100);
        $('.loading-icon').addClass('hidden');

    }

    function bookSelected(field,bookId){
        $('#count-div').removeClass('d-flex justify-content-center align-items-center');
        $('#count-div').css('color','');
        if("singlebook"==testMode||"singlechapter"==testMode){
            $('.book-image-wrapper').children('.overlay-testgen-book').hide();
            bookIds = new Set();
        }

        $("#id_"+bookId).children('.book-image-wrapper').children('.overlay-testgen-book').toggle();
        $("#id_"+bookId).parent().toggleClass("border-css");
        if(bookIds.has(bookId)){
            bookIds.delete(bookId);
        }else{
            bookIds.add(bookId);
        }

        const str = bookIds.size > 1 ? " eBooks selected": " eBook selected";
        const countDivStr = bookIds.size + str;
        if(bookIds.size > 0) {document.getElementById("count-div").innerHTML = '<p>'+countDivStr+'</p>';}
        else{
            document.getElementById("count-div").innerHTML = "";
        }


    }

    function chapterSelected(field){
        if("singlechapter"==testMode) {
            for (var i = 0; i < noOfChapters; i++) document.getElementById("chapter" + i).checked = false;
            field.checked = true;
        }
        $(field).parent().parent().toggleClass('selected-chapter-name');
        $(field).parent().parent().children('.overlay-testgen-chapter').toggleClass('show-checkbox');

        if(selectedChapters.includes(field.value)){
            selectedChapters = selectedChapters.filter(element => element != field.value);
        } else{
            selectedChapters = selectedChapters.concat(field.value);
        }

        const str = selectedChapters.length > 1 ? " chapters selected": " chapter selected";
        const countDivStr = selectedChapters.length + str;
        if(selectedChapters.length>0){
            document.getElementById("count-div").innerHTML = '<p>'+countDivStr+'</p>';
        }else{
            document.getElementById("count-div").innerHTML = '';
        }

    }

    function next(){
        document.getElementById("error_message").innerHTML= "";
         document.getElementById("count-div").innerHTML = "";
        if(currentNumber==1){
            captureFormValues();
        }
        else if(currentNumber==2){
            if(bookIds.size==0){
                if($(window).width() > 767) {
                    document.getElementById("error_message").innerHTML = "<i class='material-icons'>priority_high</i> Please select atleast one book.";
                    $('#error_message').show();
                }
                else{
                    document.getElementById("count-div").innerHTML = "<p><i class='material-icons'>priority_high</i> Please select atleast one book.</p>";
                    $('#count-div p').css('color','red');
                    $('#count-div p i').css('fontSize','16px');
                    $('#count-div p').addClass('d-flex justify-content-center align-items-center');
                }
            }
            else{
                var books="";
                for (var it = bookIds.values(), val= null; val=it.next().value; ) {
                    if(bookIds.size>0) books+=","+val;
                    else books=val;
                }
                if(books.startsWith(",")){
                    books = books.substring(1);
                }
                $("#divi2").hide();
                $("#loading").show();

                $('.loading-icon').removeClass('hidden');

                <g:remoteFunction controller="testgenerator" action="getTestChapters" onSuccess='displayChapters(data);' params="'bookId='+books+'&batchId='+testBatchId"/>
            }
        }
        else if(currentNumber==3){

            chapterIds="";
            for(i=0;i<noOfChapters;i++){
                if(document.getElementById('chapter'+i).checked){
                    if(chapterIds.length>0) chapterIds+=","+document.getElementById('chapter'+i).value;
                    else chapterIds=document.getElementById('chapter'+i).value;
                }

            }
            if(chapterIds.length>0){
                $('#error_message').hide();
                for(var i=0;i<noOfChapters;i++)//document.getElementById("chapter"+i).disabled=true;
                $("#divi3").hide();
                $("#loading").show();
                <g:remoteFunction controller="testgenerator" action="getTestQuizTypes" onSuccess='displayQuizTypes(data);' params="'chaptersList='+chapterIds"/>
            }
            else{
                if($(window).width() > 767) {
                    document.getElementById("error_message").innerHTML = "<i class='material-icons'>priority_high</i> Please select atleast one chapter.";
                    $('#error_message').show();
                }
                else{
                    document.getElementById("count-div").innerHTML = "<p><i class='material-icons'>priority_high</i> Please select atleast one chapter.</p>";
                    $('#count-div p').css('color','red');
                    $('#count-div p i').css('fontSize','16px');
                    $('#count-div p').addClass('d-flex justify-content-center align-items-center');
                }
            }
        }
        else if(currentNumber==4){
            var field = document.getElementById("noOfQuestions");
            if(field.value==""){
                if($(window).width() > 767) {
                    document.getElementById("error_message").innerHTML = "<i class='material-icons'>priority_high</i>  Please enter number of questions";
                    $('#error_message').show();
                }
                else{
                    document.getElementById("count-div").innerHTML = "<p><i class='material-icons'>priority_high</i> Please enter number of questions.</p>";
                    $('#count-div p').css('color','red');
                    $('#count-div p i').css('fontSize','16px');
                    $('#count-div p').addClass('d-flex justify-content-center align-items-center');
                }
            }else if(isNaN(field.value)){
                if($(window).width() > 767) {
                    document.getElementById("error_message").innerHTML = "<i class='material-icons'>priority_high</i>  Please enter number only";
                    $('#error_message').show();
                }
                else{
                    document.getElementById("count-div").innerHTML = "<p><i class='material-icons'>priority_high</i> Please enter number only.</p>";
                    $('#count-div p').css('color','red');
                    $('#count-div p i').css('fontSize','16px');
                    $('#count-div p').addClass('d-flex justify-content-center align-items-center');
                }
            }else if(field.value==0 || noOfQuestions<field.value){
                if($(window).width() > 767) {
                    document.getElementById("error_message").innerHTML = "<i class='material-icons'>priority_high</i>  Number of questions should be greater than 0 and less than or equal to available questions.";
                    $('#error_message').show();
                }
                else{
                    document.getElementById("count-div").innerHTML = "<p><i class='material-icons'>priority_high</i> Number of questions should be greater than 0 and less than or equal to available questions.</p>";
                    $('#count-div p').css('color','red');
                    $('#count-div p i').css('fontSize','16px');
                    $('#count-div p').addClass('d-flex justify-content-center align-items-center');
                }
            }else if(field.value> 200){
                if($(window).width() > 767) {
                    document.getElementById("error_message").innerHTML = "<i class='material-icons'>priority_high</i>Maximum number of questions allowed is 200";
                    $('#error_message').show();
                }
                else{
                    document.getElementById("count-div").innerHTML = "<p><i class='material-icons'>priority_high</i> Maximum number of questions allowed is 200.</p>";
                    $('#count-div p').css('color','red');
                    $('#count-div p i').css('fontSize','16px');
                    $('#count-div p').addClass('d-flex justify-content-center align-items-center');
                }
            }else{
                quiz = {};
                quiz.title = "Created Test";
                quiz.link = "";
                quiz.type=quizType;
                quiz.mode="play";
                quiz.generated="true";
                $("#testgenerator").modal('hide');
                startTime = new Date();
                $('#test-gen-modal').modal().hide();

                $('.loading-icon').removeClass('hidden');

                $('#beforeGenerateTest').hide();
                if("Match the answers"==quizType)
                    <g:remoteFunction controller="funlearn" action="quizQuestionAnswers" onSuccess='initializeQuestions(data);' params="'chaptersList='+chapterIds+'&resType='+quizType+'&difficultyLevel='+difficultyLevels+'&noOfQuestions='+field.value+'&createtest=true'"/>
                else{
                    var params = "mode=bookTest&quizType=testSeries&learn=false&noOfQuestions=" + field.value + "&chaptersList=" + chapterIds + "&resType=Multiple Choice Questions&batchId=" + testBatchId + "&testName=" + testName + "&startDateTime=" + startDateTime + "&endDateTime=" + endDateTime + "&resultDateTime=" + resultDateTime + "&duration=" + duration + "&timeZone=" + timeZone;
                    <g:remoteFunction controller="testgenerator" action="createTest" params="params" onSuccess="testsCreated(data);" />
                  }
            }

        }
    }
    function testsCreated(data){
        $('.loading-icon').addClass('hidden');
        //redirect the page to /onlineTest/listPages?instituteId=
        window.location.href = "/onlineTest/listTests?instituteId="+data.instituteId;
    }
    function displayQuizTypes(data){
        document.getElementById("noOfQuestions").value="";
        document.getElementById("divi5").innerHTML="";
        currentNumber=4;
        var quizTypes = data.results;
        if(quizTypes.length==1){
            getTestDifficultyLevelsAuto(quizTypes[0].resType);
        }else {
            var displayStr = "";

            if ("singlebook" == testMode) {
                displayStr = "<h5 class='stpe-count'>Step 2 of 2</h5>";
            } else if ("singlechapter" == testMode) {
                displayStr = "<h5 class='stpe-count'>Step 1 of 1</h5>";
            } else {
                displayStr = "<h5 class='stpe-count'>Step 3 of 3</h5>";
            }
            displayStr += "<div class='col-md-12'>\n" +
                "            <p class='test-type'>Select test type</p>\n" +
                "            <table class='table chapter-selection-table table-responsive table-bordered'>\n" +
                "              <tbody>";


            for (var i = 0; i < quizTypes.length; i++) {
                displayStr += "<tr class='chapter-selection'>\n" +
                    "                  <td class='chapter-name'>\n" +
                    "                    <label class='not-active'>" + quizTypes[i].resType +
                    "                      <input type='radio' name='testSelection' onclick='javascript:getTestDifficultyLevels(this)' value='" + quizTypes[i].resType + "'>\n" +
                    "                      <span class='checkmark checkmark-radio'></span>\n" +
                    "                    </label>\n" +
                    "                  </td>\n" +
                    "                </tr>";


            }
            displayStr += "  </tbody>\n" +
                "            </table>\n" +
                "          </div>";

            document.getElementById("divi4").innerHTML = displayStr;
        }
        $("#loading").hide();

        $("#divi4").fadeIn(100);
        $("#divi5").fadeIn(100);

        document.getElementById("next-button").innerText="Generate Test";
    }
    function prev(){
        $("#error_message").text('');
        document.getElementById("count-div").innerHTML = "";
         console.log("currentNumber="+currentNumber);
        if(currentNumber==2){
            currentNumber = 1;
            $("#divi2").hide();
            $("#testInputDev").show();
            $(".previous-btn").hide();
        }
        else if(currentNumber==3){

            const str = bookIds.size > 1 ? " eBooks selected": " eBook selected";
            const countDivStr = bookIds.size + str;
            document.getElementById("count-div").innerHTML = '<p>'+countDivStr+'</p>';
            selectedChapters = [];

            currentNumber = 2;
            $("#divi3").hide();
            $("#divi2").fadeIn(100);
            $(".previous-btn").hide();
            if($(window).width() < 767) {
                $("#next-button").css('position', 'fixed');
            }
        }
        else if(currentNumber==4){
            const str = selectedChapters.length > 1 ? " chapters selected": " chapter selected";
            const countDivStr = selectedChapters.length + str;
            document.getElementById("count-div").innerHTML = '<p>'+countDivStr+'</p>';
           currentNumber = 3;
            $("#divi4").fadeOut();
            $("#divi5").fadeOut();
            $("#divi6").fadeOut();
            $("#divi3").show();
            document.getElementById("next-button").innerText="Next";
            $("#next-button").css('position','static');
        }

    }

    function displayChapters(data){
        $("#loading").hide();
        var chapters = data.results;
        noOfChapters = chapters.length;
        var currentBookId=-1;

        var displayStr=" ";
            displayStr+="<div class='col-md-12'>\n";


        if("singlebook"==testMode){
            displayStr+="<h5 class='stpe-count text-left mb-3'>Step 1 of 2: Select chapter/chapters to generate test from.</h5></div>";
        }
        else {
            displayStr+="<h5 class='stpe-count text-left mb-3'>Step 2 of 3: Select chapter/chapters to generate test from.</h5></div>";
        }


$("#next-button").css('position','static');
if(chapters.length > 0) {
    console.log("number of chapters="+chapters.length);
    for (var i = 0; i < noOfChapters; i++) {

        if (("" + currentBookId) != ("" + chapters[i].bookId)) {
            //logic for closing the table of the new book starts
           console.log("currentBookId="+currentBookId);
            if (i > 0) {
                displayStr += "</div></div>\n";
            }



            //logic for starting the table if the new book starts
            displayStr += " <div class='container'><p class='container book-name book-accordion' onclick='javascript:showChapters(this);'>" + booksMap[chapters[i].bookId] +
                    "<i class=\"material-icons-round\">\n" +
                "keyboard_arrow_down\n" +
                "</i>" + "</p>\n" +
                "            <div class='d-flex flex-column chapter-selection-table hidden'><div class='chapter-selection'>" +
                "              ";
            currentBookId = chapters[i].bookId;
        } else {
            displayStr += "<div class='chapter-selection'>";
        }

        displayStr += "<div class='chapter-name'>\n" +
            "                    <label class='not-active'>" + chapters[i].name + "" +
            "                      <input type='checkbox' name='chapter" + i + "' id='chapter" + i + "' value='" + chapters[i].id + "' onclick='javascript:chapterSelected(this);'>\n" +
            "                    </label>\n" +
            "                    <div class='overlay-testgen-chapter'>\n" +
            "                      <div class='chapter-selected'>\n" +
            "<i class=\"material-icons\">\n" +
            "check_box\n" +
            "</i>" +
            "                      </div>\n" +
            "                    </div>\n" +
            "                  </div> </div>";


    }

    displayStr += "</div>\n" +
        "          </div>";
} else {
    displayStr += "<p class='stpe-count'>Sorry! Could not find any chapters. Please select another book.</p> </div>"
}


        document.getElementById("divi3").innerHTML=displayStr;
        $('#error_message').hide();

        if(testMode!="singlebook") {
            $('.previous-btn').show();
        }
        else{
            $('.previous-btn').show();
        }




        currentNumber=3;
        // $("#divi3").fadeIn(100);
        $('.loading-icon').addClass('hidden');
        $("#divi3").show();

    }

    function showChapters(element) {
        $(element).next().toggleClass("hidden");
    }


    function getTestDifficultyLevelsAuto(quizTypeInput){
        quizType = quizTypeInput;

        $("#loading").show();
        <g:remoteFunction controller="testgenerator" action="getTestDifficultyLevels" onSuccess='displayDifficultyLevels(data);' params="'chaptersList='+chapterIds+'&resType='+quizType"/>
    }
    function getTestDifficultyLevels(field){
        quizType = field.value;

        $("#loading").show();
        <g:remoteFunction controller="testgenerator" action="getTestDifficultyLevels" onSuccess='displayDifficultyLevels(data);' params="'chaptersList='+chapterIds+'&resType='+quizType"/>
    }

    function displayDifficultyLevels(data){
        $("#loading").hide();
        difficultyLevel = data.results;
        noOfDifficultyLevels = difficultyLevel.length;

        if(noOfDifficultyLevels>0) {
            if(noOfDifficultyLevels==1) {
                calculateTotalQuestionsAuto(difficultyLevel[0].diffLevel);
                $("#divi6").fadeIn(100);
            }else{
                var displayStr = "<div class='col-md-12'>\n" +
                    "            <p class='test-type'>Select difficult level</p>\n";
                displayStr +="            <select class='custom-select' id='difficulty-level-select' onchange='javascript:calculateTotalQuestions(this)'>" +
                    "            <option selected>Choose...</option>";
                for (i = 0; i < noOfDifficultyLevels; i++) {

                    displayStr += "<option value='"+ difficultyLevel[i].diffLevel +"'>"+ difficultyLevel[i].diffLevel +"</option>";

                }
                displayStr += " </select>\n";



                displayStr +="          </div>";
                document.getElementById("divi5").innerHTML = displayStr;
                $("#divi5").fadeIn(100);
                $("#divi6").fadeIn(100);
            }
        }
        else{
            difficultyLevels="all";
            $("#loading").show();
            <g:remoteFunction controller="testgenerator" action="getTestNumberOfQuestions" onSuccess='displayNumberOfQuestions(data);' params="'chaptersList='+chapterIds+'&resType='+quizType+'&difficultyLevel=all'"/>
        }
    }

    function calculateTotalQuestionsAuto(difficultyLevelInput){
        noOfQuestions = difficultyLevel[0].count;
        difficultyLevels="\'"+difficultyLevelInput+"\'";
        document.getElementById("noofquestionslabel").innerHTML="Enter number of questions ("+noOfQuestions+" available).";
         document.getElementById("count-div").innerHTML="<p>"+noOfQuestions+" Questions available from Selected eBooks & chapters.</p>";
        document.getElementById("noOfQuestions").disabled=false;
        if(noOfQuestions === 0){
            document.getElementById("noOfQuestions").disabled=true;
            document.getElementById("noOfQuestions").value="";
            document.getElementById("noofquestionslabel").innerHTML="Enter number of questions.";
        }

    }

     function calculateTotalQuestions(element){
        noOfQuestions=0;
        difficultyLevels="";
        const selectDropdownValue = $(element).val();
        const selectedDifficultyLevel = difficultyLevel.find(element => element.diffLevel == selectDropdownValue);
        noOfQuestions = selectedDifficultyLevel.count;
        document.getElementById("noofquestionslabel").innerHTML="Enter number of questions ("+noOfQuestions+" available).";
        document.getElementById("noOfQuestions").disabled=false;
        if(noOfQuestions === 0){
            document.getElementById("noOfQuestions").disabled=true;
            document.getElementById("noOfQuestions").value="";
            document.getElementById("noofquestionslabel").innerHTML="Enter number of questions.";
        }

    }



    <%if(instructor){%>
    displayInstrcutorOptions();

    function displayInstrcutorOptions(){
        $("#testDetailsRow").show();
        getInstructorBatches();

    }
    function getInstructorBatches(){
        <g:remoteFunction controller="institute" action="getAllBatchesForInstructor" onSuccess='displayInstructorBatches(data);'/>
    }
    var batches;
    var batchesExist=false;

    function displayInstructorBatches(data){
        batches = data.batches;
        batchesExist=false;
        var htmlStr="<select name='batchId' id='batchId' style='margin-top: .5rem!important;width: 100%;padding: .375rem .75rem;background-color: #fff;border-radius: .25rem;'><option value='' class='form-control'>Select Batch</option>";
        if("OK"==data.status){
            batchesExist=true;
            for(i=0;i<batches.length;i++){
                htmlStr +="<option value='"+batches[i].batchId+"'>&nbsp;&nbsp;"+batches[i].name+"</option>";
            }
        }
        htmlStr +="</select>";
        if(elementExists('sharingOptions')) {
            document.getElementById("sharingOptions").innerHTML = htmlStr;
        }
    }
    <%}%>
    $('#test-gen-modal').on('hidden.bs.modal', function () {
        document.getElementById("noofquestionslabel").innerHTML="Number of questions";
        resetTestType();
    });
    $('#close-quizz-modal').on('click',function () {
        document.getElementById("noofquestionslabel").innerHTML="Number of questions";
        resetTestType();
    });

    $("body").bind("DOMNodeInserted", function() {
        $(this).find('.chapter-selection-table tr').addClass('chapter-selection');
    });


    $("#test-gen-modal,#quizModal").on("hidden.bs.modal", function () {
        $('.previous-btn').removeClass('disabled');
    });
</script>
