<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:javascript src="moment.min.js"/>
<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />
<asset:javascript src="multiselect.js"/>
<script>
    var loggedIn=false;
</script>
<style>

.table-bordered th,td {
    padding: 10px;
}


@media (min-width: 576px) {
    .modal-dialog-centered {
        min-height: calc(100% - 3.5rem);
    }
}
@media (min-width: 576px) {
    .modal-dialog {
        /*max-width: 500px;*/
        margin: 1.75rem auto;
    }
}
/* Add a border to the table */
table {
    border-collapse: collapse;
    width: 100%;
}

/* Add a border to table cells */
th, td {
    border: 1px solid #ddd;
    padding: 8px;
}

/* Set a light background color for the row headers */
th {
    background-color: #f2f2f2;
    color: black;
}
.form-group a {
    color: white;
}
</style>
<style>
/* Basic styling if needed */
.sortable-column {
    cursor: pointer;
    text-decoration: underline;
}
.chart-container {
    width: 100%;
    max-width: 800px;
    margin: 0 auto;
}
.chart-title {
    text-align: center;
    margin-top: 30px;
}
</style>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<script src="/assets/katex.min.js"></script>
<asset:stylesheet href="katex.min.css"/>
<script src="/assets/auto-render.min.js"></script>
<script type="text/javascript" async
        src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS_HTML-full">
</script>
<script type="text/x-mathjax-config">
MathJax.Hub.Config({tex2jax: {inlineMath: [['$','$'], ['\\(','\\)']]}});
</script>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container-fluid" style="min-height: calc(100vh - 160px);" >
    <div class='row' >
        <div class='col-md-9 main' style=" margin: 40px auto; float: none; padding: 15px;">
            <div id="content-books">
                <div>
                    <div class="container">
                        <h2 class="text-center">Question Analytics : ${testName}</h2>
                        <hr/>
                        <!-- SCORE DISTRIBUTION CHART -->
                        <div class="chart-title">
                            <h3>Score Distribution</h3>
                        </div>
                        <div class="chart-container">
                            <canvas id="scoreDistChart"></canvas>
                        </div>

                        <!-- QUESTION DIFFICULTY CHART -->
                        <div class="chart-title">
                            <h3>Question Difficulty (Correct / Incorrect / Skipped)</h3>
                        </div>
                        <div class="chart-container">
                            <canvas id="questionDiffChart"></canvas>
                        </div>

                        <!-- AVERAGE TIME PER QUESTION CHART -->
                        <div class="chart-title">
                            <h3>Average Time per Question (Seconds)</h3>
                        </div>
                        <div class="chart-container">
                            <canvas id="avgTimeChart"></canvas>
                        </div>
                        <!-- Sorting Links -->
                        <p>Click on column headers to sort.</p>

                        <table class="table table-striped table-hover">
                            <thead>
                            <tr>
                                <th>No</th>
                                <th>
                                    <g:link action="testQuestionAnalytics"
                                            params="[testId: testId, sortColumn:'questionText', sortDir: sortDir == 'asc' && sortColumn=='questionText' ? 'desc' : 'asc']"
                                            class="sortable-column">
                                        Question
                                    </g:link>
                                </th>
                                <th>
                                    <g:link action="testQuestionAnalytics"
                                            params="[testId: testId, sortColumn:'correctCount', sortDir: sortDir == 'asc' && sortColumn=='correctCount' ? 'desc' : 'asc']"
                                            class="sortable-column">
                                        Correct
                                    </g:link>
                                </th>
                                <th>
                                    <g:link action="testQuestionAnalytics"
                                            params="[testId: testId, sortColumn:'incorrectCount', sortDir: sortDir == 'asc' && sortColumn=='incorrectCount' ? 'desc' : 'asc']"
                                            class="sortable-column">
                                        Incorrect
                                    </g:link>
                                </th>
                                <th>
                                    <g:link action="testQuestionAnalytics"
                                            params="[testId: testId, sortColumn:'skippedCount', sortDir: sortDir == 'asc' && sortColumn=='skippedCount' ? 'desc' : 'asc']"
                                            class="sortable-column">
                                        Skipped
                                    </g:link>
                                </th>
                                <th>
                                    <g:link action="testQuestionAnalytics"
                                            params="[testId: testId, sortColumn:'avgTime', sortDir: sortDir == 'asc' && sortColumn=='avgTime' ? 'desc' : 'asc']"
                                            class="sortable-column">
                                        Avg Time (sec)
                                    </g:link>
                                </th>
                            </tr>
                            </thead>
                            <tbody>
                            <% int i=1 %>
                            <g:if test="${analytics}">
                                <g:each in="${analytics}" var="row">
                                    <tr>
                                        <td>${i}</td>
                                        <td>
                                            <%= row.questionText %>
                                        </td>

                                        <td>${row.correctCount}</td>
                                        <td>${row.incorrectCount}</td>
                                        <td>${row.skippedCount}</td>
                                        <td>${String.format('%.2f', row.avgTime ?: 0.0)}</td>
                                    </tr>
                                    <% i++ %>
                                </g:each>
                            </g:if>
                            <g:else>
                                <tr>
                                    <td colspan="5">No data found for this test.</td>
                                </tr>
                            </g:else>
                            </tbody>
                        </table>
                    </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!--   //name, userPrompt, systemPrompt, response, promptType, feedbackType, feedback-->
<g:render template="/${session['entryController']}/footer_new"></g:render>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<asset:javascript src="searchContents.js"/>


</body>
</html>
<script type="text/javascript">
    // For demonstration, we assume the controller passes data as lists in the model:
    // 1) scoreDistribution = [ [range:'0-10', count:5], [range:'11-20', count:8], ... ]
    // 2) questionStats     = [ [questionText:'Q1', correct:20, incorrect:10, skipped:3], ... ]
    // 3) questionStats     also has avgTime if you want to combine or a separate data array.
    // Alternatively, you might have separate timeStats.

    // Convert the Grails data to JavaScript arrays
    var scoreDistData =<% out << (scoreDistribution as grails.converters.JSON).toString() %>;
    // e.g. [ {"range":"0-10","count":5}, {"range":"11-20","count":8}, ... ]

    var questionStatsData = <% out << (questionStats as grails.converters.JSON).toString() %>;
    // e.g. [ {"questionText":"Q1..","correct":20,"incorrect":10,"skipped":3,"avgTime":12.5}, ... ]

    $(document).ready(function() {

        // 1. SCORE DISTRIBUTION CHART
        // Build labels from 'range' and data from 'count'
        var scoreLabels = scoreDistData.map(function(item){ return item.range; });
        var scoreValues = scoreDistData.map(function(item){ return item.count; });

        var ctxScore = document.getElementById('scoreDistChart').getContext('2d');
        new Chart(ctxScore, {
            type: 'bar',
            data: {
                labels: scoreLabels,
                datasets: [{
                    label: 'Number of Students',
                    data: scoreValues,
                    backgroundColor: 'rgba(54, 162, 235, 0.6)'
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Count of Students'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Score Range'
                        }
                    }
                }
            }
        });

        // 2. QUESTION DIFFICULTY CHART
        // We'll do a grouped bar chart: correct vs incorrect vs skipped
        var qLabels = questionStatsData.map(function(q){ return q.questionNo; });
        var correctVals = questionStatsData.map(function(q){ return q.correct; });
        var incorrectVals = questionStatsData.map(function(q){ return q.incorrect; });
        var skippedVals = questionStatsData.map(function(q){ return q.skipped; });

        var ctxQDiff = document.getElementById('questionDiffChart').getContext('2d');
        new Chart(ctxQDiff, {
            type: 'bar',
            data: {
                labels: qLabels,
                datasets: [
                    {
                        label: 'Correct',
                        data: correctVals,
                        backgroundColor: 'rgba(75, 192, 192, 0.6)'
                    },
                    {
                        label: 'Incorrect',
                        data: incorrectVals,
                        backgroundColor: 'rgba(255, 99, 132, 0.6)'
                    },
                    {
                        label: 'Skipped',
                        data: skippedVals,
                        backgroundColor: 'rgba(201, 203, 207, 0.6)'
                    }
                ]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Number of Students'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Question'
                        }
                    }
                }
            }
        });

        // 3. AVERAGE TIME PER QUESTION CHART
        // We'll do a bar chart for average time
        var avgTimeVals = questionStatsData.map(function(q){ return q.avgTime || 0; });

        var ctxAvgTime = document.getElementById('avgTimeChart').getContext('2d');
        new Chart(ctxAvgTime, {
            type: 'bar',
            data: {
                labels: qLabels,
                datasets: [{
                    label: 'Avg Time (sec)',
                    data: avgTimeVals,
                    backgroundColor: 'rgba(153, 102, 255, 0.6)'
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Seconds'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Question'
                        }
                    }
                }
            }
        });

    });
</script>
