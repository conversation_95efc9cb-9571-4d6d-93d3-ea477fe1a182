<%if(showSearchAtTop){ %>
<div class="d-flex justify-content-left align-items-end order-1 mt-4 search-bar-title">
    <h5>Start your journey here</h5>
</div>
<div class="home-section search-area p-4 mt-2 order-1">
    <%}else{%>
<div class="d-flex justify-content-left align-items-end order-6 search-bar-title mt-4">
    <h5>Find your books here</h5>
</div>
<div class="home-section search-area p-4 mt-2 order-6">
    <%}%>
    <form class="position-relative">
        <div class="input-group input-group-lg">
            <div class="input-group-prepend">
                <span class="input-group-text bg-transparent px-2" id="search-box"><i class="material-icons-round">search</i></span>
            </div>
            <input type="text" class="form-control form-control-modifier typeahead" id="search-book-home" name="search" aria-label="eBooks search form" aria-describedby="search-box" autocomplete="off" placeholder="Search title, subject, ISBN, publisher, etc..">
            <a href="javascript:removeSearchValue();" id="removeSearchTextBtn" class="btn bg-transparent border-0 position-absolute p-2"><i class="material-icons-round">close</i></a>
        </div>
        <div class="quick-search hide">
            <h6 id="searchEmpty" class="text-center mt-3" style="display: none;">No books found!</h6>
            <p class="text-black-50">QUICK SEARCH</p>
            <div class="list-group">
                <a href="/books/ebooks?searchString=CBSE%20books&level=School&syllabus=CBSE" class="list-group-item list-group-item-action"><i class="material-icons-round">moving</i> CBSE books - All classes</a>
                <a href="/books/ebooks?level=Medical-Entrances&syllabus=NEET" class="list-group-item list-group-item-action"><i class="material-icons-round">moving</i> NEET - MCQ books</a>
                <a href="/books/ebooks?level=School&syllabus=Olympiad&grade=10" class="list-group-item list-group-item-action"><i class="material-icons-round">moving</i> Olympiad Class 10th books</a>
                <a href="/books/ebooks?level=Competitive-Exams&syllabus=All-India-Job-Exams" class="list-group-item list-group-item-action"><i class="material-icons-round">moving</i> All India Job Exams books</a>
            </div>
        </div>
    </form>
    <div class="search-by-category" id="browseByCategories">

    </div>
</div>

<script>
    var quickSearchElement = document.querySelector(".quick-search");
    var searchInputElement = document.querySelector("#search-book-home");
    var removeSearchElement = document.querySelector("#removeSearchTextBtn");
    var quickSearchLinkElements = quickSearchElement.querySelectorAll('.list-group a');

    searchInputElement.addEventListener("focusin", () => {
        quickSearchElement.classList.replace("hide", "show");
    });
    searchInputElement.addEventListener("focusout", () => {
        quickSearchElement.classList.replace("show", "hide");
        document.querySelector("#searchEmpty").setAttribute("style", "display: none");
    });

    var enteredValue = "";
    searchInputElement.addEventListener("keyup", (event) => {
        var searchTextLength = event.target.value.length;
        if (searchTextLength <= 2) {
            quickSearchElement.classList.replace("hide", "show");
            document.querySelector("#searchEmpty").setAttribute("style", "display: none");
            removeSearchElement.setAttribute("style", "display: none");
            enteredValue = "";
        } else {
            enteredValue = event.target.value;
            removeSearchElement.setAttribute("style", "display: block");
        }
    });

    quickSearchLinkElements.forEach(element => {
        element.addEventListener('click', () => {
            searchInputElement.value = element.lastChild.nodeValue;
            $(".loading-icon").removeClass("hidden");
        });
    });

    function removeSearchValue() {
        searchInputElement.value = "";
        searchInputElement.focus();
        removeSearchElement.setAttribute("style", "display: none");
    }

    var activeCategories = [];
    if("${session["activeCategories_"+session["siteId"]]}" != "") {
        activeCategories = JSON.parse("${session["activeCategories_"+session["siteId"]]}".replace(/&quot;/g, '"').replaceAll('&#92;u0026', '&'));
    }

    var browseCategories="<h6>Browse by Categories</h6>";
    for(var i=0;i<activeCategories.length;i++){
        browseCategories +="<a href=\"/ebooks?level="+ encodeURIComponent(activeCategories[i].level)+"&\" class=\"btn btn-light\">"+activeCategories[i].level+"</a>";
    }
    document.getElementById("browseByCategories").innerHTML=browseCategories;
</script>
