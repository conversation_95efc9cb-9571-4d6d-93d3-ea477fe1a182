<%@ page import="javax.servlet.http.Cookie" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/books/navheader_new"></g:render>
<asset:stylesheet href="wonderslate/ntseLandingPage.css" async="true" media="all"/>
<script>
    $('link[data-role="baseline"]').attr('href', '');
</script>


<section class="mt-5 institute-homepage">
    <div class="showcase">
        <div class="container d-flex flex-column flex-lg-row align-items-center">
            <div class="showcase__first gap first">
                <h2 class="mb-3"><strong>What is a<span style="color:#f2994a;"> NTSE exam</span> and why should you take it</strong></h2>
                <section class="video-promo-hero container section__odd">
                    <div class="video-promo-hero-bg">
                        <div class="row vcenter">
                            <div class="col-md-12 vcenter"><a class="video-promo-watch" id="play-button" href="#"><i class="fa-duotone video-promo-play-button"></i><i class="fa-solid fa-circle-play video-promo-play-button"></i></a></div>
                        </div>
                    </div>
                    <div class="video-promo-video-wrapper">
                        <div class="video-promo-youtube-wrapper">
                            <div class="video-container" id="player">
                            </div>
                        </div>
                    </div>
                </section>
                <p>NTSE is a national level exam conducted by the NCERT for granting scholarships to meritorious students studying in class 10th. The exam will be in two stages, stage 1 and stage 2</p>
                <div class="institute-homepage mt-3">
                    <div class="d-flex justify-content-center justify-content-lg-start flex-wrap align-items-center buttons-wrapper">
                        <a href="/ebooks?level=School&syllabus=NTSE&" class="btn btn-lg btn-warning border-0 mx-3 mt-4 mt-md-0">Explore</a>
                    </div>
                </div>

            </div>
            <div class="showcase__second showcase-img">
                <img src="${assetPath(src: 'ntse/showcase.svg')}">
            </div>
        </div>
    </div>

    <div class="exploring-ebooks d-none mt-5">
        <div class="container">
            <h1 class="mb-3"><strong>Explore eBooks</strong></h1>
            <div id="instituteGrades" class="pt-3 mb-5">

            </div>
            <div id="instituteCategories" class="row">

            </div>
        </div>
    </div>

    <div class="section__one  ">
        <div class="container section section__odd d-flex flex-column flex-lg-row gap">
            <div class="section__one-first first">
                <h1><strong>SCHOLARSHIP</strong></h1>
                <p>The biggest advantage is that the Central Government awards scholarships to the students who have cleared the NTSE exam. Students seeking admission in Science and Social Science stream are granted scholarships till Doctorate level while students in professional courses get the scholarship till postgraduate level.</p>
            </div>
            <div class="section__one-second second d-flex justify-content-end">
                <img src="${assetPath(src: 'ntse/scholarship.svg')}">
            </div>
        </div>
    </div>

    <div class="section__two ">
        <div class="container section section__even d-flex reverse-row gap">
            <div class="section__two-first first">
                <h1><strong>GET PREFERENCE IN <span style="color:#f2994a;">STUDY ABROAD</span></strong></h1>
                <p>The universities and education institutes abroad are familiar with the NTSE scholarship exam and give preference to students who have qualified it.</p>
            </div>
            <div class="section__two-second second">
                <img src="${assetPath(src: 'ntse/study_abroad.svg')}" >
            </div>
        </div>
    </div>

    <div class="section__three  ">
        <div class="container section section__odd d-flex flex-column flex-lg-row gap">
            <div class="section__three-first first">
                <h1><strong>BUILD <span style="color:#f2994a;">CONFIDENCE</span></strong></h1>
                <p>NTSE is a competitive examination and only about 2000 qualified students are granted scholarships. Qualifying in this examination needs perseverance and talent, and helps to instill confidence in to crack similar exams in future.</p>
            </div>
            <div class="section__three-second second d-flex justify-content-end">
                <img src="${assetPath(src: 'ntse/confidence.svg')}" >
            </div>
        </div>
    </div>

    <div class="section__four ">
        <div class="container section section__even d-flex reverse-row gap">
            <div class="section__four-first first">
                <h1><strong>GET ADMISSION IN TOP EDUCATION <span style="color:#f2994a;">INSTITUTES</span></strong></h1>
                <p>In India, there are some colleges that have reserved seats exclusively for students who have qualified NTSE. An NTSE scholar can pass the admission test of a college and get direct entry to it.</p>
            </div>
            <div class="section__four-second second">
                <img src="${assetPath(src: 'ntse/institutes.svg')}" >
            </div>
        </div>
    </div>

    <div class="section__five ">
        <div class="container section section__odd d-flex flex-column flex-lg-row gap">
            <div class="section__five-first first">
                <h1><strong>YOU GET A <span style="color:#f2994a;">COMPETITIVE EDGE</span> WHEN APPLYING FOR A <span style="color:#f2994a;">JOB</span></strong></h1>
                <p>An NTSE scholar is given more preference in government jobs and is also helpful when appearing for interviews in different prestigious jobs. It is also widely accepted for private jobs</p>
            </div>
            <div class="section__five-second second d-flex justify-content-end">
                <img src="${assetPath(src: 'ntse/job.svg')}" >
            </div>
        </div>
    </div>

    <div class="section__six">
        <div class="container section section__even d-flex reverse-row gap">
            <div class="section__six-first first">
                <h1><strong>DISCOUNTS</strong></h1>
                <p>Many colleges give preference to the NTSE scholars and give them a good discount in the fees, sometimes up to 50 percent.</p>
            </div>
            <div class="section__fix-second second">

                <img src="${assetPath(src: 'ntse/discounts.svg')}" >
            </div>
        </div>
    </div>

    <div class="section__seven pb-5">
        <div class="container section section__odd d-flex flex-column flex-lg-row gap">
            <div class="section__seven-first first">
                <h1><strong>HOW TO PREPARE?</strong></h1>
                <p>NTSE is MCQs based exam. You can get the best smart eBooks on Wonderslate from best publishers and practice. Do you know that the toppers spend about 20 percent of their time learning the concepts and 80 percent of their time practicing it. For best practice use Wonderslate NTSE smart eBooks. To access these ebooks go to </p>
            </div>
            <div class="section__seven-second second d-flex justify-content-end">
                <img src="${assetPath(src: 'ntse/preparation.svg')}" >
            </div>
        </div>
    </div>

    <div class="container">
        <div class="ebooks-categories d-flex flex-wrap justify-content-center justify-content-md-between align-items-center w-100">
            <div class="text-center d-flex justify-content-center align-items-center flex-column institute-homepage w-100">
                <h2 class="mb-0">eBooks Store</h2>
                <p class="mt-0">Use eBooks save trees <span class="tree_emoji">&#127795;</span></p>
                <div class="d-flex flex-wrap justify-content-center align-items-center buttons-wrapper mt-4">
                    <a href="/store" class="btn btn-lg btn-warning border-0 mx-3 mt-4 mt-md-0"><span>Store</span></a>
                    <sec:ifLoggedIn>
                        <a href="/wsLibrary/myLibrary" class="btn btn-lg btn-warning border-0 mx-3 mt-4 mt-md-0" ><span>My eBooks</span></a>
                    </sec:ifLoggedIn>
                    <sec:ifNotLoggedIn>
                        <a href="javascript:loginOpen();" class="btn btn-lg btn-warning border-0 mx-3 mt-4 mt-md-0"><span>Login</span></a>
                        <a href="javascript:signupModal();" class="btn btn-lg btn-warning border-0 mx-3 mt-4 mt-md-0"><span>Sign-up</span></a>
                    </sec:ifNotLoggedIn>
                </div>
            </div>
        </div>
    </div>
</section>



<g:render template="/books/footer_new"></g:render>

<script>
    var showGrades = "";
    var showCategories = "";
    var instituteCategories = ["Olympiad", "Karnataka CET", "Medical Entrances", "Engineering Entrances", "Fiction"];

    <g:remoteFunction controller="institute" action="getGradesList" params="'level=School&syllabus=CBSE'" onSuccess="displayGrades(data);"/>

    function displayGrades(data) {
        var level = 'School'
        level = replaceAll(decodeURI(level), ' ', '-');
        var syllabus =
            syllabus = 'NTSE'
        var preUniversity = 'false';
        var grades = JSON.parse(data.grades);

        //add additional things for schools
        if("School"==level&&"true"!=preUniversity){
            showGrades += "<a href='/store?level=" + level + "&syllabus=NTSE' class='grade-link' target='_blank'>NTSE</a>";
            showGrades += "<a href='/store?level=" + level + "&syllabus=Olympiad' class='grade-link' target='_blank'>Olympiad</a>";
        }
        showGrades += "<a href='/store?level=General' class='grade-link' target='_blank'>General</a>";
        showGrades += "<a href='/store?level=Engineering-Entrances' class='grade-link' target='_blank'>Engineering Entrances</a>";
        showGrades += "<a href='/store?level=Medical-Entrances' class='grade-link' target='_blank'>Medical Entrances</a>";
        showGrades += "<a href='/store?level=Competitive-Exams' class='grade-link' target='_blank'>Competitive Exams</a>";

        document.getElementById("instituteGrades").innerHTML = showGrades;
    }

    <g:remoteFunction controller="institute" action="getSyllabusList" params="'level=School'" onSuccess="displaySyllabus(data);"/>

    function displaySyllabus(data){
        var syllabus = JSON.parse(data.syllabus);
        var level = 'School'
        level = replaceAll(decodeURI(level),' ','-');
        var skip=false;
        for (var i = 0; i < syllabus.length; i++) {
            skip=false;
            if(!skip) showGrades += "<a href='/store?level="+level+"&syllabus="+replaceAll(syllabus[i].syllabus,' ','-')+"' class='grade-link' target='_blank'>"+syllabus[i].syllabus+"</a>";
        }
        showGrades += "<a href='/store?level=General' class='grade-link' target='_blank'>General</a>";
        showGrades += "<a href='/store?level=Competitive-Exams' class='grade-link' target='_blank'>Competitive Exams</a>";

        document.getElementById("instituteGrades").innerHTML= showGrades;
    }

    var tag = document.createElement('script');
    tag.src = "https://www.youtube.com/iframe_api";
    var firstScriptTag = document.getElementsByTagName('script')[0];
    firstScriptTag.parentNode.insertBefore(tag, firstScriptTag);

    var player;
    function onYouTubeIframeAPIReady() {
        player = new YT.Player('player', {
            height: '390',
            width: '640',
            videoId: 'bsuo5ajQ3cs',
            playerVars: {
                'playsinline': 1
            },
        });
    }

    var playVideo = document.querySelector('.video-promo-watch');
    var stopVideo = document.querySelector('.video-promo-video-close');

    playVideo.addEventListener('click',function (e){
        e.preventDefault();
        $(".video-promo-hero").addClass("playing-video");
        player.playVideo();
    })
    stopVideo.addEventListener('click',function (e){
        e.preventDefault();
        $(".video-promo-hero").removeClass("playing-video");
        player.stopVideo();
    })

</script>