<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
<script>
    var loggedIn = false;
</script>
<sec:ifLoggedIn>
    <script>
        loggedIn = true;
    </script>
</sec:ifLoggedIn>
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS_HTML-full" async></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/ckeditor/4.18.0/ckeditor.js" ></script>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<section class="row p-4 justify-content-center">
    <div class="col-9 p-4 shadow round">
        <div class="row justify-content-center">
            <div class="col-6">
                <label for="iname">Name</label>

                <div class="input-group mb-4">
                    <input class="form-control" type="text" value="${institute.name}" id="iname"   onblur="javascript:updateInstitute(this);">
                </div>
                <label for="cname">Contact Name</label>

                <div class="input-group mb-4">
                    <input class="form-control" type="text" value="${institute.contactName}" id="cname" onblur="javascript:updateInstitute(this);">
                </div>
                <label for="cnumber">Contact Number</label>

                <div class="input-group mb-4">
                    <div class="input-group-prepend">
                        <span class="input-group-text" style="">+</span>
                    </div>
                    <input class="form-control" placeholder="number with country code" type="text" oninput="numberOnly(this.id)" value="${institute.contactNumber}" id="cnumber" maxlength="13" onblur="javascript:updateInstitute(this);">
                </div>
                <label for="cemail">Contact Email</label>

                <div class="input-group mb-4">
                    <input class="form-control" type="text" value="${institute.contactEmail}" id="cemail" onblur="javascript:updateInstitute(this);">
                </div>
                <label for="fb">Facebook</label>

                <div class="input-group mb-4">
                    <input class="form-control" type="text" value="${institute.facebook}" id="fb" onblur="javascript:updateInstitute(this);">
                </div>
                <label for="fax">Fax</label>

                <div class="input-group mb-4">
                    <div class="input-group-prepend">
                        <span class="input-group-text" style="">+</span>
                    </div>
                    <input class="form-control" placeholder="number with country code" oninput="numberOnly(this.id)" type="text" value="${institute.fax}" id="fax" maxlength="13" onblur="javascript:updateInstitute(this);">
                </div>
                <label for="website">Website</label>

                <div class="input-group mb-4">
                    <input class="form-control" type="text" value="${institute.website}" id="website" onblur="javascript:updateInstitute(this);">
                </div>

            </div>

            <div class="col-6">
                <label>Address</label>

                <div class="input-group mb-1">
                    <input placeholder="Line 1" class="form-control" type="text" value="${institute.addressLine1}"
                           id="aline1" onblur="javascript:updateInstitute(this);">
                    <input placeholder="Line 2" class="form-control" type="text" value="${institute.addressLine2}"
                           id="aline2" onblur="javascript:updateInstitute(this);">
                </div>

                <div class="input-group mb-1">
                    <input placeholder="Town" class="form-control" type="text" value="${institute.town}" id="town">
                    <input placeholder="ZIP code" class="form-control" type="text" oninput="numberOnly(this.id)" value="${institute.zipcode}"
                           id="zipcode" onblur="javascript:updateInstitute(this);">
                </div>

                <div class="input-group mb-4">
                    <input placeholder="State" class="form-control" type="text" value="${institute.state}" id="state" onblur="javascript:updateInstitute(this);">
                    <input placeholder="Country" class="form-control" type="text" value="${institute.country}"
                           id="country" onblur="javascript:updateInstitute(this);">
                </div>
                <% if(session["siteId"].intValue()==1||session["siteId"].intValue()==25){%>
                <div class="row m-0 p-0 align-items-center justify-content-start">
                    <label for="checkOutDays">Check Out Days</label>
                    <i style="font-size:24px; cursor: pointer" class="fa p-2" onclick="alert('Number of days the user can borrow the library eBook.\nIf check out days is empty, by default check out days is considered as 14 days.')">&#xf05a;</i>
                </div>
                <div class="input-group mb-4">
                    <input placeholder="Check Out Days" class="form-control" type="number" min="1" value="${institute.checkOutDays}" id="checkOutDays" oninput="numberOnly(this.id)" onblur="javascript:updateInstitute(this);">
                </div>
                <%}%>
                <div class="input-group  mb-4">
                    <label for="publogo">Logo</label>
                    <% if (institute.logo != "" && institute.logo != null) { %>
                    <div class=" input-group">
                        <img class="border rounded" style="max-height: 5rem; max-width: 100%" id="publogo"
                             src="/institute/instituteImage?instituteId=${institute.id}">
                    </div>
                    <% } else { %>
                    <div class="input-group">
                        <div class=" text-center border rounded p-4 col">
                            <i class="material-icons">description</i>

                            <p>Logo Not Uploaded</p>
                        </div>
                    </div>
                    <% } %>
                    <form id="instututeLogoForm" method="post" enctype="multipart/form-data"
                          action="/institute/uploadInstituteLogo"
                          target="_parent">
                        <div class="custom-file mt-1">
                            <input type="hidden" value="${institute.id}" name="instituteId">
                            <input class="custom-file-input" id="selectFile" name="file" type="file"
                                   accept="image/png, image/jpeg, image/gif ,image/svg"
                                   onchange="updateInstituteImage()">
                            <label class="custom-file-label" id="logoFileName">Choose file</label>
                        </div>
                    </form>

                    <p id="file-error" class="input-group" style="font-size: 12px;
                    margin-top: 0.5rem;
                    text-align: center;">Please Upload Image below 2mb</p>
                </div>
                <label for="yt">Youtube</label>

                <div class="input-group mb-4">
                    <input class="form-control" type="text" value="${institute.youtube}" id="yt" onblur="javascript:updateInstitute(this);">
                </div>
                <label for="li">LinkedIn</label>

                <div class="input-group mb-4">
                    <input class="form-control" type="text" value="${institute.linkedin}" id="li" onblur="javascript:updateInstitute(this);">
                </div>
                <label for="twt">Twitter</label>

                <div class="input-group mb-4">
                    <input class="form-control" type="text" value="${institute.twitter}" id="twt" onblur="javascript:updateInstitute(this);">
                </div>
            </div>

        </div>
        <label for="urlName">Url Name</label>
        <%if(wsAdmin){%>
        <div class="input-group mb-1">
            <div class="input-group-prepend">
                <span class="input-group-text" style="">https://www.wonderslate.com/institution/</span>
            </div>
            <input type="text" class="form-control" name="urlname" id="urlName" value="${institute.urlname}"
                   placeholder="URL Name" onchange="onUrlNameChanged()"/>
        </div>
        <%}else{%>
        <div class="input-group mb-1">
            <%if(institute.urlname!=null){%>
            <div class="input-group-prepend">
                <span class="input-group-text" style="">https://${institute.urlname}.wonderslate.com</span>
            </div>
        <%}%>
            <input type="hidden"  name="urlname" id="urlName" value="${institute.urlname}"/>
        </div>
        <%}%>
        <div class="input-group mb-4">
            <label for="tagline">Tagline</label>

            <div class="input-group">
                <textarea class="input-group" rows="3" name='tagline' id="tagline"
                          placeholder="Description" onblur="javascript:updateInstitute(this);" >${institute.tagline}</textarea>
            </div>
        </div>
        <div class="input-group mb-4">
            <label for="tagline">SEO Description</label>

            <div class="input-group">
                <textarea class="input-group" rows="3" name='seoDescription' id="seoDescription"
                          placeholder="SEO Description" onblur="javascript:updateInstitute(this);" >${institute.seoDescription}</textarea>
            </div>
        </div>

        <div class="input-group mb-4">
            <label for="aboutUs">Section 1</label>

            <div class="input-group">
                <textarea class="input-group" rows="3" name='aboutUs' id="aboutUs" placeholder="About Us"
                          maxlength="2000"></textarea>
            </div>
        </div>

        <div class="input-group mb-4">
            <label for="contactDetails">Section 2</label>

            <div class="input-group">
                <textarea class="input-group" rows="3" name='contactDetails' id="contactDetails"
                          placeholder="Contact Details" maxlength="2000"></textarea>
            </div>
        </div>
        <div class="input-group mb-4">
            <label for="section3">Section 3</label>

            <div class="input-group">
                <textarea class="input-group" rows="3" name='section3' id="section3" placeholder="About Us"
                          maxlength="2000"></textarea>
            </div>
        </div>
        <div class="input-group mb-4">
            <label for="section4">Section 4</label>

            <div class="input-group">
                <textarea class="input-group" rows="3" name='section4' id="section4" placeholder="About Us"
                          maxlength="2000"></textarea>
            </div>
        </div>

        <div class="input-group mb-4">
            <h3 class="mt-4">Banner Management</h3>
            <p>Please add all the banners of same size. (Recommended size: 2050px * 780px)</p>
            <% for (int i = 0; i < 6; i++) { %>

            <form class="form-horizontal mt-4" enctype="multipart/form-data" role="form"
                  name="uploadbanner${(i + 1)}"
                  id="uploadbanner${(i + 1)}" action="/institute/addBannersForInstitute"
                  onsubmit="return addBanner(${(i+1)})"
                  method="post">
                <h5><strong>Banner ${(i + 1)}</strong></h5>
                <input type="hidden" name="bannerId" value="${bannersMstList[i] ? bannersMstList[i].id : ""}">
                <input type="hidden" name="instituteId" value="${institute.id}">
                <div class="row add_banners">

                    <div class="form-group col mb-3">
                        <label for="name${(i + 1)}">Name</label>
                        <input type="text" class="form-control" name="name" id="name${(i + 1)}"
                               value="${bannersMstList[i] ? bannersMstList[i].imageName : ""}"
                               autocomplete="off">
                    </div>
                    <div class="form-group col mb-3">
                        <label for="bookId${(i + 1)}">Book Id</label>
                        <input type="text" class="form-control" name="bookId" id="bookId${(i + 1)}"
                               value="${bannersMstList[i] ? bannersMstList[i].bookId : ""}"
                               autocomplete="off" onblur="checkBookId(this.value, ${(i+1)})">
                    </div>
                    <div class="form-group col mb-3">
                        <label for="bannerImage${(i + 1)}">Choose Image</label>
                        <input type="file" name="file" class="form-control"
                               id="bannerImage${(i + 1)}"
                               value="${bannersMstList[i] ? bannersMstList[i].imagePath : ""}" accept="image/*">
                    </div>

                </div>

                <div class="row align-items-center banner_details">
                    <div class="form-group col-12 col-md-6 col-lg-4 mb-3">
                        <input type="text" class="form-control form-control-sm" name="bannerImageName" id="bannerImageName${(i + 1)}"
                               value="${bannersMstList[i] ? bannersMstList[i].imagePath : ""}" readonly="true" autocomplete="off">
                    </div>
                    <% if (bannersMstList[i] != null && bannersMstList[i].imagePath) { %>
                    <div class="form-group col-12 col-md-6 col-lg-4 mb-3">
                        <a class="p-0" href="/institute/showInstituteBannerImage?instituteId=${institute.id}&fileName=${bannersMstList[i] ? bannersMstList[i].imagePath : ""}">View Banner Image</a>
                    </div>
                    <% } %>
                </div>

                <% if (bannersMstList[i] != null) { %>
                <div class="d-flex">
                    <button class="btn btn-success btn-success-modifier mb-2 mr-2 px-4" type="submit">Update</button>
                    <button class="btn btn-outline-secondary btn-outline-secondary-modifier mb-2 px-3" type="button"
                            onclick="deleteInstituteBanner(${bannersMstList[i].id});">Delete</button>
                </div>
                <% } else { %>
                <button type="submit" class="btn btn-primary btn-primary-modifier mb-2 px-5">Add</button>
                <% } %>
            </form>

            <% } %>
        </div>

        <div class="mb-4">
            <h3 class="mt-4">Gallery Images</h3>
            <% for (int i = 0; i < 12; i++) { %>
            <form class="form-horizontal mt-4" enctype="multipart/form-data" role="form"
                  name="uploadGalleryImage${(i + 1)}"
                  id="uploadGalleryImage${(i + 1)}" action="/institute/uploadInstituteImage"
                  onsubmit="return uploadGalleryImages(${(i+1)})"
                  method="post">
                <div class="row add_photos">
                    <div class="form-group col-md-6 mb-3">
                        <label for="galleryImage${(i + 1)}">Image ${(i + 1)}</label>
                        <input type="file" name="file" class="form-control"
                               id="galleryImage${(i + 1)}"
                               value="" accept="image/*">
                    </div>
                </div>
                 <input type="hidden" name="galleryImageId" value="${galleryImageList[i] ? galleryImageList[i].id : ""}">
                <input type="hidden" name="instituteId" value="${institute.id}">

            <div class="row align-items-center">
                <div class="form-group col-12 col-md-6 col-lg-4 mb-3">
                    <input type="text" class="form-control form-control-sm" name="galleryImageName" id="galleryImageName${(i + 1)}"
                           value="${galleryImageList[i] ? galleryImageList[i].imageName : ""}" readonly="true" autocomplete="off">
                </div>
                <% if (galleryImageList[i] != null && galleryImageList[i].imageName) { %>
                <div class="form-group col-12 col-md-6 col-lg-4 mb-3">
                    <a class="p-0" href="/institute/showInstituteGalleryImage?instituteId=${institute.id}&fileName=${galleryImageList[i] ? galleryImageList[i].imageName : ""}">View Gallery Image</a>
                </div>
                <% } %>
            </div>

            <% if (galleryImageList[i] != null) { %>
            <div class="d-flex">
                <button class="btn btn-success btn-success-modifier mb-2 mr-2 px-4" type="submit">Update</button>
                <button class="btn btn-outline-secondary btn-outline-secondary-modifier mb-2 px-3" type="button"
                        onclick="deleteInstituteGalleryImageById(${galleryImageList[i].id});">Delete</button>
            </div>
            <% } else { %>
            <button type="submit" class="btn btn-primary btn-primary-modifier mb-2 px-5">Add</button>
            <% } %>
            </form>
            <% } %>
        </div>
        <input type="text" class="form-control hidden" id="urls" value="${instituteUrls.join('~')}"/>
    </div>
    <form name="updateAboutUs" id="updateAboutUs" action="/institute/updateAboutUs" method="post">
        <input type="hidden" name="instituteId" id="instituteId">
        <input type="hidden" name="aboutUs" >
        <input type="hidden" name="contactDetails" >
        <input type="hidden" name="section3" >
        <input type="hidden" name="section4" >
    </form>
</section>
<script>
    <%if(institute!=null&&institute.aboutUs!=null&&!"".equals(institute.aboutUs)){%>
    document.getElementById("aboutUs").innerHTML="${institute.aboutUs.replace("\n", "").replace("\r", "")}";
    <%}%>
    <%if(institute!=null&&institute.contactDetails!=null&&!"".equals(institute.contactDetails)){%>
    document.getElementById("contactDetails").innerHTML="${institute.contactDetails.replace("\n", "").replace("\r", "")}";
    <%}%>
    <%if(institute!=null&&institute.section3!=null&&!"".equals(institute.section3)){%>
    document.getElementById("section3").innerHTML="${institute.section3.replace("\n", "").replace("\r", "")}";
    <%}%>
    <%if(institute!=null&&institute.section4!=null&&!"".equals(institute.section4)){%>
    document.getElementById("section4").innerHTML="${institute.section4.replace("\n", "").replace("\r", "")}";
    <%}%>

    var urls = document.getElementById('urls').value.split('~')
    var index = urls.indexOf('${institute.urlname}')
    var instituteId = '${institute.id}'
    if(index>-1){
        urls.splice(index, 1);
    }
    if (!document.getElementById('urlName').value) {

        document.getElementById("urlName").value =
            document
                .getElementById("iname").value
                .toString()
                .toLowerCase()
                .replace(/ /g, "-")
                .replace(/\./g, "-")
    }
</script>


<script>

    function updateInstitute (field) {
        var seoDescription=document.getElementById('seoDescription').value;
        var tagline = document.getElementById('tagline').value;
        var name = document.getElementById('iname').value;
        var cname = document.getElementById('cname').value;
        var cnumber = document.getElementById('cnumber').value;
        var cemail = document.getElementById('cemail').value;
        var addressLine1 = document.getElementById('aline1').value;
        var addressLine2 = document.getElementById('aline2').value;
        var town = document.getElementById('town').value;
        var zip = document.getElementById('zipcode').value;
        var state = document.getElementById('state').value;
        var country = document.getElementById('country').value;
        var urlname = document.getElementById('urlName').value;
        var website = document.getElementById('website').value;
        var fb = document.getElementById('fb').value;
        var fax = document.getElementById('fax').value;
        var li = document.getElementById('li').value;
        var yt = document.getElementById('yt').value;
        var twt = document.getElementById('twt').value;
        tagline=tagline.replace(/&/g,"~");
        addressLine1=addressLine1.replace(/&/g,"~");
        addressLine2=addressLine2.replace(/&/g,"~");
        li=li.replace(/&/g,"~");
        yt=yt.replace(/&/g,"~");
        twt=twt.replace(/&/g,"~");
        fb=fb.replace(/&/g,"~");
        seoDescription=seoDescription.replace(/&/g,"~");
        website=website.replace(/&/g,"~");
        var checkOutDays = ('1'=="${session["siteId"]}" || '25'=="${session["siteId"]}")?document.getElementById("checkOutDays").value:null;
        if(name.trim().length===0){
            alert('Please enter a name.');
            return
        }
        if(cname.trim().length===0){
            alert('Please enter a contact name.');
            return
        }
        if(zip.length > 0 && zip.length !== 6){
            alert('Please enter a valid ZIP code.');
            return
        }
        if(!validateEmail(cemail)){
            alert('Please enter a valid email.');
            return
        }
        if (urlname == "" || urlname.search(/[ <>{}#/\\?:!&$^*%`|+"]/g) > -1) {
            alert("Institute Url must be all small characters and must not contain any space and special characters \n < > { } # / \\ ? : ! & $ ^ * % ` | + ");
            return
        }
        var webreg = /[(http(s)?):\/\/(www\.)?a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)/ig
        if (website.length > 0 && !webreg.test(website)) {
            alert('Enter Valid Website')
            document.getElementById("website").value = "";
            return
        }
        $('.loading-icon').removeClass('hidden');
        document.updateAboutUs.instituteId.value=instituteId;
        if(field.name=="aboutUs") {
            $('.loading-icon').removeClass('hidden');
            document.updateAboutUs.aboutUs.value=CKEDITOR.instances.aboutUs.getData();
            document.updateAboutUs.submit();
        } else if(field.name=="contactDetails") {
            $('.loading-icon').removeClass('hidden');
            document.updateAboutUs.contactDetails.value=CKEDITOR.instances.contactDetails.getData();
            document.updateAboutUs.submit();
        }else if(field.name=="section3") {
            $('.loading-icon').removeClass('hidden');
            document.updateAboutUs.section3.value=CKEDITOR.instances.section3.getData();
            document.updateAboutUs.submit();
        }else if(field.name=="section4") {
            $('.loading-icon').removeClass('hidden');
            document.updateAboutUs.section4.value=CKEDITOR.instances.section4.getData();
            document.updateAboutUs.submit();
        }else {
            <g:remoteFunction controller="institute" action="updateInstitute"
            params="'tagline='+tagline+'&seoDescription='+seoDescription+'&institutionEdit=true'+'&instituteId='+instituteId
            +'&name='+name+'&cname='+cname+'&cnumber='+cnumber+'&cemail='+cemail+'&addressLine1='+addressLine1+'&addressLine2='+addressLine2+'&town='+town
            +'&zip='+zip+'&state='+state+'&country='+country+'&urlname='+urlname+'&website='+website+'&fb='+fb+'&fax='+fax+'&li='+li+'&yt='+yt+'&twt='+twt+'&checkOutDays='+checkOutDays" onSuccess = "instituteAdded(data);"/>
        }
    }
    function instituteAdded(data){
        if(data.status!='success'){
            alert('Failed to update. Please try later.')
            return
        }
        alert("updated successfully.");
        location.reload()
    }

    function updateInstituteImage() {
        var oFile = document.getElementById("selectFile").files[0]; // <input type="file" id="fileUpload" accept=".jpg,.png,.gif,.jpeg"/>

        if (oFile.name.search(/[ <>{}#/\\?:!&$^*%`|+"]/g) > -1) {
            alert("File Name must not contain any space and special characters \n < > { } # / \\ ? : ! & $ ^ * % ` | + ")
            document.getElementById("selectFile").value = "";
            return
        }

        if (oFile.size > 2097152) // 2 mb for bytes.
        {
            $('#file-error').css('color', 'red');
            document.getElementById("selectFile").value = "";
            return;
        }
        document.getElementById("logoFileName").innerText = oFile.name;
        document.getElementById('instututeLogoForm').submit();
    }
    function validateEmail(field) {
        var regex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,5}$/;
        return (regex.test(field)) ? true : false;
    }
    function onInstituteNameChanged(){
        if (true) {
            document.getElementById("urlName").value =
                document
                    .getElementById("iname").value
                    .toString()
                    .toLowerCase()
                    .replace(/ /g, "-")
                    .replace(/\./g, "-")
            onUrlNameChanged();
        }
    }
    function onUrlNameChanged () {
        document.getElementById('urlName').value = document.getElementById('urlName').value.toLowerCase().replace(/ /g, "-").replace(/\./g, "-");
         var instituteUrl= document.getElementById('urlName').value;
        <g:remoteFunction controller="institute" action="validateInstituteUrl" params="'instituteUrl='+instituteUrl+'&instituteId='+instituteId" onSuccess = "urlUpdated(data);"/>

    }

    function urlUpdated(data) {
        if (data.status == "OK") {
            alert("updated successfully!");
            location.reload();
        }else if(data.status == "exist"){
            alert("url name already exist! please enter different url");
            document.getElementById('urlName').value="";

        }
    }
    function numberOnly(id) {
        var element = document.getElementById(id);
        var regex = /[^0-9]/gi;
        element.value = element.value.replace(regex, "");
    }
</script>

<script>
    CKEDITOR.config.width = '100%'
    CKEDITOR.config.height = '30em'
    CKEDITOR.replace('aboutUs', {
        customConfig: '/assets/ckeditor/customConfig.js',
        uploadUrl: '/institute/uploadInstituteContent?command=QuickUpload&type=Files&responseType=json&instituteId='+instituteId,
        filebrowserUploadUrl: '/institute/uploadInstituteContent?command=QuickUpload&type=Files&responseType=json&mode='+instituteId,
        filebrowserImageUploadUrl: '/institute/uploadInstituteContent?command=QuickUpload&type=Images&responseType=json&instituteId='+instituteId,
    });

    CKEDITOR.replace('contactDetails', {
        customConfig: '/assets/ckeditor/customConfig.js',
        uploadUrl: '/institute/uploadInstituteContent?command=QuickUpload&type=Files&responseType=json&instituteId='+instituteId,
        filebrowserUploadUrl: '/institute/uploadInstituteContent?command=QuickUpload&type=Files&responseType=json&mode='+instituteId,
        filebrowserImageUploadUrl: '/institute/uploadInstituteContent?command=QuickUpload&type=Images&responseType=json&instituteId='+instituteId,

    });
    CKEDITOR.replace('section3', {
        customConfig: '/assets/ckeditor/customConfig.js',
        uploadUrl: '/institute/uploadInstituteContent?command=QuickUpload&type=Files&responseType=json&instituteId='+instituteId,
        filebrowserUploadUrl: '/institute/uploadInstituteContent?command=QuickUpload&type=Files&responseType=json&mode='+instituteId,
        filebrowserImageUploadUrl: '/institute/uploadInstituteContent?command=QuickUpload&type=Images&responseType=json&instituteId='+instituteId,
    })
    CKEDITOR.replace('section4', {
        customConfig: '/assets/ckeditor/customConfig.js',
        uploadUrl: '/institute/uploadInstituteContent?command=QuickUpload&type=Files&responseType=json&instituteId='+instituteId,
        filebrowserUploadUrl: '/institute/uploadInstituteContent?command=QuickUpload&type=Files&responseType=json&mode='+instituteId,
        filebrowserImageUploadUrl: '/institute/uploadInstituteContent?command=QuickUpload&type=Images&responseType=json&instituteId='+instituteId,
    })

    CKEDITOR.instances.aboutUs.on('blur', function() {
        updateInstitute(document.getElementById("aboutUs"));
    });
    CKEDITOR.instances.contactDetails.on('blur', function() {
        updateInstitute(document.getElementById("contactDetails"));
    });
    CKEDITOR.instances.section3.on('blur', function() {
        updateInstitute(document.getElementById("section3"));
    });
    CKEDITOR.instances.section4.on('blur', function() {
        updateInstitute(document.getElementById("section4"));
    });

    function uploadGalleryImages(id) {
        var oFile = document.getElementById("galleryImage"+id).files[0]; // <input type="file" id="fileUpload" accept=".jpg,.png,.gif,.jpeg"/>

        if (oFile.name.search(/[ <>{}#/\\?:!&$^*%`|+"]/g) > -1) {
            alert("File Name must not contain any space and special characters \n < > { } # / \\ ? : ! & $ ^ * % ` | + ")
            document.getElementById("galleryImage"+id).value = "";
        } else if (oFile.size > 2097152) // 2 mb for bytes.
        {
            $('#file-error').css('color', 'red');
            document.getElementById("galleryImage"+id).value = "";
        }else {
            $('.loading-icon').removeClass('hidden');
            alert("Saved successfully!");
        }

    }

    function addBanner(index) {
        var selectedDescFile = document.getElementById("bannerImage"+index).files[0];
        if (document.getElementById("name" + index).value == "") {
            alert("Please enter the name.");
            return false;
        } else if(document.getElementById("bannerImage"+index).value=="") {
            alert("Please choose image.");
            return false;
        } else if(document.getElementById("bannerImage"+index).value=="" && document.getElementById("bannerImageName"+index).value=="") {
            alert("Please choose image.");
            return false;
        } else if(selectedDescFile !=undefined && selectedDescFile.name.search(/[ <>{}#/\\?:!&$^*%`|+"]/g)>-1){
            alert("File name must not contain any space and special characters.");
            return false;
        } if(selectedDescFile !=undefined && selectedDescFile.size>=153600){
            alert("File size should be below 150kb.");
            return false;
        }else {
            $('.loading-icon').removeClass('hidden');
            alert("Saved successfully!");
        }
    }

    function deleteInstituteBanner(id) {
        var confirmAction = confirm("Are you sure to delete this banner?");
        if(confirmAction) {
            $('.loading-icon').removeClass('hidden');
            <g:remoteFunction controller="institute" action="deleteInstituteBannerById" params="'id='+id+'&instituteId='+instituteId" onSuccess = "bannerDeleted(data);"/>
        }
    }

    function deleteInstituteGalleryImageById(id){
        var confirmAction = confirm("Are you sure to delete this image?");
        if(confirmAction) {
            $('.loading-icon').removeClass('hidden');
            <g:remoteFunction controller="institute" action="deleteInstituteGalleryImageById" params="'id='+id+'&instituteId='+instituteId" onSuccess = "bannerDeleted(data);"/>
        }

    }

    function bannerDeleted(data) {
        if (data.status == "OK") {
            alert("Deleted successfully!");
            location.reload();
        }
    }
</script>
<g:render template="/${session['entryController']}/footer_new"></g:render>
