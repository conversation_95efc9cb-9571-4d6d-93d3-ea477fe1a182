<%@ page import="com.wonderslate.data.ChaptersMst" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<% if(("sage".equals(session["entryController"])||"evidya".equals(session["entryController"])||"wolterskluwer".equals(session["entryController"]))){%>
<g:render template="/${session['entryController']}/navheader"></g:render>
<%}
else {%>

<g:render template="/${session['entryController']}/navheader_new"></g:render>
<%}%>
<g:render template="/funlearn/topicinclude"></g:render>
<asset:javascript src="soundmanager2.js"/>
<asset:javascript src="bar-ui.js"/>
<asset:stylesheet src="bar-ui.css"/>
<script src="https://apis.google.com/js/api.js"></script>


<div class="notes-wrapper-overlay">
    <div class="close-notes-wrapper">
        <a href="javascript:showHideNotesSection();"><i class="material-icons">close</i></a>
    </div>
</div>
<div class="container-fluid read-book-container">

    <div class="row book-read-tabs" style="margin-right: 0;">

            <div class="col-md-3 col-sm-3 col-xs-4 read-book-name hidden-xs">
                <p class="read-book-title"><%=("9".equals(""+session.getAttribute("siteId")))?"CHAPTERS":title%></p>

            </div>
            <div class="col-md-9 col-xs-12 col-sm-9 r-tab tabs-section">
                <div class="col-md-12 col-xs-12 col-sm-12 r-tab-child">
                    <ul class="nav nav-tabs chapter-tabs" id="chapter-details-tabs" role="tablist">
                        <li role="presentation" class="active" style="display: none" onclick="javascript:showTextFormatter(this);">
                            <a href="#read" role="tab" data-toggle="tab" id="tabread">Read</a>
                        </li>

                        <li role="presentation"  style="display: none;">
                            <a href="#long-qa" role="tab" data-toggle="tab" id="tablongqa">Long Answers</a>
                        </li>
                        <li role="presentation" style="display: none;">
                            <a href="#qa" role="tab" data-toggle="tab" id="tab-short-qa">Short Answers</a>
                        </li>
                        <li role="presentation" style="display: none" onclick="javascript:hideTextFormatter(this);">
                            <a href="#learn" class="to-hide" role="tab" data-toggle="tab"  id="tabquiz"><%=("9".equals(""+session.getAttribute("siteId")))?"MCQs":"Objective Types"%></a>
                        </li>

                        <li role="presentation" style="display: none;" >
                            <a href="#create-test" class="to-hide" role="tab" data-toggle="tab" id="tabcreatetest">Create Test</a>
                        </li>

                        <li role="presentation" style="display: none;" onclick="javascript:hideTextFormatter(this);">
                            <a href="#flash-cards" role="tab" data-toggle="tab" id="tab-flash-cards">Flash Cards</a>
                        </li>
                        <li role="presentation" style="display: none" onclick="javascript:hideTextFormatter();">
                            <a href="#discuss" class="to-hide" role="tab" data-toggle="tab">Discussion Form</a>
                        </li>
                        <li role="presentation" style="<%=("9".equals(""+session.getAttribute("siteId")))?"display:none":""%>"  onclick="javascript:hideTextFormatter(this);">
                            <a href="#videos" class="to-hide" role="tab" id="tabvideo" data-toggle="tab">Videos</a>
                        </li>
                        <li role="presentation" style="<%=("9".equals(""+session.getAttribute("siteId")))?"display:none":""%>" onclick="javascript:hideTextFormatter(this);">
                            <a href="#additional" class="to-hide" role="tab" data-toggle="tab" id="tabweblinks">Extra Readings</a>
                        </li>



                    </ul>



                </div>
            </div>
        </div>
        <div class="row chapter-read-material-wrapper">
            <div class="col-md-3 col-xs-6 col-sm-3 read-book-chapters">

                <ol class="read-book-chapters-wrapper" style="padding-left: 30px;">

                    <g:each in="${topicMst}" var="chapter" status="i">
                        <%l

                        %>
                        <li class="chapter-name" id="chapterName${chapter.id}">
                            <a href="javascript:getChapterDetails('${chapter.id}','${chapter.name.replace("'","\\'")}')" id="chapter${chapter.id}" class="<%=(""+topicId).equals(""+chapter.id)%>">${chapter.name}</a>


                            <ul class="chapter-sections" id="sections-dropdown-${chapter.id}"></ul>
                        </li>

                    </g:each>

                </ol>

                <ul class="description-list-wrapper">
                    <li class="quick-links-label">Quick Links</li>
                    <li class="description-list-item" id="bookDescription"></li>
                    <li class="description-list-item" id="detailedContents"></li>
                    <li class="description-list-item" id="aboutTheAuthors"></li>
                    <li class="description-list-item" id="buyNow"></li>
                </ul>



            </div>


            <a class="hidden-sm hidden-md hidden-lg" href="javascript:showHideChaptersMobile();" id="hideShowDivMobile">Chapters</a>
            <a class="hidden-sm hidden-xs hidden-md hidden-lg" href="javascript:showHideChaptersTab();" id="hideShowDivTab">Chapters</a>
            <div class="chapter-overlay hidden"></div>
            <div id="book-read-material" class="col-md-9 col-sm-12 col-xs-12 book-read-material">
                <div class="loading-icon">
                    <div class="loader-wrapper">
                        <div class="loader">Loading</div>
                    </div>
                </div>

                <style type="text/css">

                .shine {
                    background: #d4d7d9;
                    background-image: linear-gradient(to right, #d4d7d9 0%, #e4e9ed 20%, #d4d7d9 40%, #d4d7d9 100%);
                    background-repeat: no-repeat;
                    background-size: 800px 104px;
                    display: inline-block;
                    position: relative;

                    -webkit-animation-duration: 1s;
                    -webkit-animation-fill-mode: forwards;
                    -webkit-animation-iteration-count: infinite;
                    -webkit-animation-name: placeholderShimmer;
                    -webkit-animation-timing-function: linear;
                }

                box {
                    height: 104px;
                    width: 100%;
                }

                div.line-wrapper {
                    display: inline-flex;
                    flex-direction: column;
                    margin-left: 25px;
                    margin-top: 15px;
                    vertical-align: top;
                }

                lines {
                    height: 10px;
                    margin-top: 10px;
                    width: 100%;
                }

                photo {
                    display: block!important;
                    width: 325px;
                    height: 30px;
                    margin-top: 15px;
                }
                .mt-20{
                    margin-top: 10px;
                }

                @-webkit-keyframes placeholderShimmer {
                    0% {
                        background-position: -468px 0;
                    }

                    100% {
                        background-position: 468px 0;
                    }
                }
                .flexAlign{
                    display: flex;
                    justify-content: center;
                }

                </style>

                <div class="tab-content">
                     <div role="tabpanel" class="tab-pane fade in show active" id="all">
                        <div class="container">


                            <div id="content-data-all" class="col-md-12 col-xs-12 col-sm-12"></div>
                        </div>

                    </div>



                    <div role="tabpanel" class="tab-pane fade" id="long-qa">
                        <div id="content-data-QA" class="col-md-12 col-xs-12 col-sm-12"></div>
                    </div>
                    <div role="tabpanel" class="tab-pane fade" id="qa">
                        <div id="content-data-shortQAndA"></div>
                    </div>
                    <div role="tabpanel" class="tab-pane fade" id="learn">
                        <div id="content-data-quiz" class="row quiz-section"></div>
                    </div>

                    <div role="tabpanel" class="tab-pane fade" id="flash-cards">

                        <div id="flash-card-carousel" class="carousel slide flash-card-slider" data-interval="false">
                            <ol class="carousel-indicators">
                                <li data-target="#flash-card-carousel" data-slide-to="0" class="active"></li>
                            </ol>
                            <div class="carousel-inner row quiz-section" id="content-data-flashCards" role="listbox"></div>

                            <div class="carousel-control-wrapper col-md-12">
                                <a class="left carousel-control" id="carousel-btn-left" href="#flash-card-carousel" role="button" data-slide="prev">
                                    <i class="material-icons">chevron_left</i>
                                </a>
                                <p class="card-number" id="card-number"></p>
                                <a class="right carousel-control" id="carousel-btn-right" href="#flash-card-carousel" role="button" data-slide="next">
                                    <i class="material-icons">chevron_right</i>
                                </a>
                            </div>
                        </div>
                    </div>
                    <div role="tabpanel" class="tab-pane fade" id="create-test">
                        <div class="quiz-section row">
                            <div class='quiz-item-wrapper'>
                                <div class='quiz-item'>
                                    <p class='quiz-name'>This Chapter</p>
                                </div>
                                <div class='quiz-buttons'>
                                    <a href="javascript:startTestGenerator('singlechapter');" class='btn btn-block quiz-learn-btn waves-effect waves-ripple'>Start</a>
                                </div>
                            </div>
                            <div class='quiz-item-wrapper'>
                                <div class='quiz-item'>
                                    <p class='quiz-name'>Multiple Chapters</p>
                                </div>
                                <div class='quiz-buttons'>
                                    <a href="javascript:startTestGenerator('singlebook');" class='btn btn-block quiz-learn-btn waves-effect waves-ripple'>Start</a>
                                </div>
                            </div>
                            <div class='quiz-item-wrapper'>
                                <div class='quiz-item'>
                                    <p class='quiz-name'>Multiple Books</p>
                                </div>
                                <div class='quiz-buttons'>
                                    <a href="javascript:startTestGenerator('multiplebooks');" class='btn btn-block quiz-learn-btn waves-effect waves-ripple'>Start</a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div role="tabpanel" class="tab-pane fade" id="videos">
                        <div id="withnovideos" style="display: none">
                            <div class="container">
                                <sec:ifNotLoggedIn>
                                    <div class="row justify-content-center">
                                        <div class="text-center">
                                            <a href='javascript:loginOpen()' class='btn btn-block btn-theme'>Add Related Videos</a>
                                            <a href='javascript:loginOpen()' class='btn btn-block btn-secondaries'>See Related Videos</a>
                                        </div>
                                    </div>
                                </sec:ifNotLoggedIn>
                                <sec:ifLoggedIn>
                                    <div class="row justify-content-center" id="videoInAppOnly" style="display: none">
                                        <div class=""><b>Videos for this chapter is available in app only</b></div>
                                    </div>
                                    <div class="row justify-content-center">
                                        <div class="text-center">
                                            <a href='javascript:createNewVideo();' class='btn btn-block btn-theme' data-toggle="modal" data-target="#addVideo">
                                                Add Related Videos
                                            </a>
                                            <a href='javascript:searchRelatedVideos();' class='btn btn-block btn-secondaries'>
                                                See Related Videos
                                            </a>
                                        </div>
                                    </div>
                                </sec:ifLoggedIn>
                                <br>
                                <div class="row justify-content-center">
                                    <div class="refrenceText text-center">Add your videos here or See related videos here </div>
                                </div>
                            </div>
                        </div>
                        <div id="videoAddButton" class="container"  style="display: none">

                            <div class="d-flex justify-content-end">

                                <div class="dropdown">
                                    <button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown">
                                        <i class="material-icons">
                                            add
                                        </i>  <span>Add a Video</span>
                                    </button>
                                    <sec:ifLoggedIn>
                                        <div class="dropdown-menu">
                                            <a class="dropdown-item" href="javascript:openVideos();"><span>Add new videos</span><i class="material-icons">
                                                search
                                            </i></a>
                                            <a class="dropdown-item" href="javascript:searchRelatedVideos();"><span>Add related videos</span><i class="material-icons">
                                                search
                                            </i></a>
                                            <a class="dropdown-item" href="javascript:searchRelatedVideos();"><span>Search Related videos</span><i class="material-icons">
                                                search
                                            </i></a>

                                        </div>
                                    </sec:ifLoggedIn>
                                    <sec:ifNotLoggedIn>
                                        <div class="dropdown-menu">
                                            <a class="dropdown-item" href="javascript:loginOpen();"><span>Add new videos</span><i class="material-icons">
                                                search
                                            </i></a>
                                            <a class="dropdown-item" href="javascript:loginOpen();"><span>Add related videos</span><i class="material-icons">
                                                search
                                            </i></a>
                                            <a class="dropdown-item" href="javascript:loginOpen();"><span>Search Related videos</span><i class="material-icons">
                                                search
                                            </i></a>

                                        </div>
                                    </sec:ifNotLoggedIn>
                                </div>
                            </div>
                        </div>

                        <div id="searchVideos" style="display: none"></div>
                        <g:render template="/wonderpublish/videoPlay"></g:render>
                    </div>
                    <div role="tabpanel" class="tab-pane fade" id="additional">

                        <div id="withnoweblinks"  style="display: none">
                            <div class="container">
                                <sec:ifNotLoggedIn>
                                    <div class="row justify-content-center">
                                        <div class="text-center">

                                            <a href='#' class='btn btn-block btn-theme' data-toggle="modal" data-target="#addWeburl">
                                                Add solutions link
                                            </a>

                                            <a href="javascript:googleSearch('solutions')" class='btn btn-block btn-secondaries'>
                                                Search solutions for this chapter
                                            </a>

                                            <a href='javascript:loginOpen()' class='btn btn-block btn-secondaries'>Search worksheets for this chapter</a>

                                            <a href='javascript:loginOpen()' class='btn btn-block btn-secondaries'>Search previous year question papers</a>
                                        </div>
                                    </div>
                                </sec:ifNotLoggedIn>
                                <sec:ifLoggedIn>
                                    <div class="row justify-content-center">
                                        <div class="text-center">

                                            <a href='#' class='btn btn-block btn-theme' data-toggle="modal" data-target="#addWeburl">
                                                Add solutions link
                                            </a>


                                            <a href="javascript:googleSearch('solutions')" class='btn btn-block btn-secondaries'>
                                                Search solutions for this chapter
                                            </a>




                                            <a href="javascript:googleSearch('worksheets')" class='btn btn-block btn-secondaries'>
                                                Search worksheets for this chapter
                                            </a>

                                            <a href="javascript:googleSearch('previous year question papers')" class='btn btn-block btn-secondaries'>
                                                Search previous year question papers
                                            </a>
                                        </div>
                                    </div>
                                </sec:ifLoggedIn>
                                <div class="row justify-content-center">
                                    <p class="refrenceText">Add your Reference here or Search related Reference here </p>
                                </div>
                            </div>
                        </div>
                        <div id="addRefButton" class="d-flex justify-content-end" style="display: none">
                            <div class="dropdown">
                                <button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown">
                                    <i class="material-icons">
                                        add
                                    </i>
                                    %{--          <span>Solutions link</span>--}%

                                    <span>Solutions link</span>

                                </button>
                                <sec:ifLoggedIn>
                                    <div class="dropdown-menu">
                                        <a class="dropdown-item" href="javascript:openSolution();"><span>Add Ref. link</span><i class="material-icons">
                                            search
                                        </i></a>
                                        <a class="dropdown-item" href="javascript:googleSearch('solutions')"><span>Search Related refs.</span><i class="material-icons">
                                            search
                                        </i></a>
                                        <a class="dropdown-item" href="javascript:googleSearch('worksheets')"><span>Search Related worksheets.</span><i class="material-icons">
                                            search
                                        </i></a>
                                        <a class="dropdown-item" href="javascript:googleSearch('previous year question papers')"><span>Search Previous year questions.</span><i class="material-icons">
                                            search
                                        </i></a>
                                    </div>
                                </sec:ifLoggedIn>

                                <sec:ifNotLoggedIn>
                                    <div class="dropdown-menu">
                                        <a class="dropdown-item" href="javascript:loginOpen();"><span>Add Ref. link</span><i class="material-icons">
                                            search
                                        </i></a>
                                        <a class="dropdown-item" href="javascript:loginOpen();"><span>Search Related refs.</span><i class="material-icons">
                                            search
                                        </i></a>
                                        <a class="dropdown-item" href="javascript:loginOpen();"><span>Search Related worksheets.</span><i class="material-icons">
                                            search
                                        </i></a>
                                        <a class="dropdown-item" href="javascript:loginOpen();"><span>Search Previous year questions.</span><i class="material-icons">
                                            search
                                        </i></a>
                                    </div>
                                </sec:ifNotLoggedIn>

                            </div>
                        </div>
                        <g:render template="/wonderpublish/additionalReferences"></g:render>

                    </div>


                </div>




                <p class="related-books-label" style="display: none;">Related Books</p>
                <div class="" id="related-books-sage" style="display: none;"></div>


            </div>

        </div>

    </div>
</div>
<div id="overlay"></div>




<%
    if("sage".equals(session["entryController"])&&"true".equals(session.getAttribute("sageUserCreated"))){
%>
<g:render template="/sage/additionalStudentInfo"></g:render>
<%}%>

<script type="text/javascript" async
        src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS_HTML-full">
</script>



<g:render template="/sage/footer"></g:render>



<script>
    var bookLang = "${book.language}" ;
    var bookContentCreatedBy = "${book.createdBy}";
    var createdByWsEditor = "${createdByWsEditor}"
    var pageType='book';
    var bookTags="";
    var notesCreationMode=false;
    var vendor="${book.vendor}";
    var previousChapterId=${topicId};
    <%if(book!=null&&book.tags!=null){%>
    bookTags="${book.tags}";
    <%}%>
    var loggedInUser = false;
    var allTabMode=true;
    var instituteLibrary=false;

    function changeActiveTab(tabName){
        if(tabName=="all") allTabMode=true;
        else allTabMode=false;

        console.log("tabName="+tabName+" allTabMode="+allTabMode);
    }
</script>

<sec:ifLoggedIn>
    <script>
        loggedInUser = true;
    </script>
</sec:ifLoggedIn>

<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js" async></script>
<asset:javascript src="searchContents.js"/>
<asset:javascript src="addcontents.js"/>
<asset:javascript src="clock.js"/>
<asset:javascript src="moment.min.js"/>
<asset:javascript src="landingpage/bootstrap-3-typeahead.js"/>
<script type="text/javascript" async
        src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS_HTML-full">
</script>

<script type="text/x-mathjax-config">
MathJax.Hub.Config({tex2jax: {inlineMath: [['$','$'], ['\\(','\\)']]}});
</script>

<g:render template="/funlearn/topicscripts"></g:render>
<g:render template="/testgenerator/testgen"></g:render>
<g:render template="/wonderpublish/allSection"></g:render>
<g:render template="/wonderpublish/readingSection"></g:render>
<g:render template="/wonderpublish/notesHighlightSection"></g:render>
<g:render template="/wonderpublish/qaSection"></g:render>
<g:render template="/wonderpublish/quizSection"></g:render>
<g:render template="/wonderpublish/videoSection"></g:render>
<g:render template="/wonderpublish/flashCardSection"></g:render>
<g:render template="/wonderpublish/additionalReferenceSection"></g:render>
<g:render template="/wonderpublish/wonderGoaStudySet"></g:render>
<script>


    var sageOnly="${sageOnly}";
    var receivedResId="${resId}";

    <%if(instructor){%>
    instructor = true;
    <%}%>
    <%if(instructorControlledBook){%>
    instructorControlledBook=true;
    <%}%>
    var authorTag="Author";
    <%if(book.authors!=null&&book.authors.indexOf(',')!=-1){%>
    authorTag="Authors";
    <%}%>

    lastReadTopicId = ${lastReadTopicId};
    $('#zoom-section').hide();
    <%if(notesCreationMode){%>
    notesCreationMode=true;
    <%}%>
    getTopicDetails('${topicId}','chapter');

    var sageSite=false;
    <% if("sage".equals(session["entryController"])){%>
    sageSite=true;
    <%}else{%>
    $("#chapterName"+previousChapterId).addClass('orangeText');
    <%}%>

    function getChapterDetails(chapterId,chapterName){

        $("#htmlreadingcontent").hide();
        $('#displayNotes').hide();
        $("#chapterName"+previousChapterId).removeClass('orangeText');
        $('.loading-icon').removeClass('hidden');

        // if(($("#tabread").parents('li').css('display') == 'none')&&($("#tabread").hasClass('active'))){
        //
        //
        //
        // }
        if(sageSite) {$("#tabcreatetest").parents('li').hide();
            $('.loading-icon').addClass('hidden');
        }
        if(elementExists('sections-dropdown-'+previousChapterId)){
            document.getElementById('sections-dropdown-'+previousChapterId).innerHTML="";
        }
        if(elementExists('content-data-studyset')){
            document.getElementById('content-data-studyset').innerHTML="";
            $("#content-data-studyset-nosets").show();
            $("#study-set-wrapper-container").hide();
        }
        if(elementExists('content-data-userNotes')){
            document.getElementById('content-data-userNotes').innerHTML="";
        }
        if(elementExists('content-data-no-notes')){
            $("#content-data-no-notes").show();
        }


        if(elementExists('addNotes')){
            $('#addNotes').hide();

        }
        //   $('#chapter-details-tabs a[href="#read"]').tab('show');
        previousChapterId=chapterId;
        $("#chapterName"+previousChapterId).addClass('orangeText');
        getTopicDetails(chapterId,'chapter');
        //
        // if($(window).width() < 768){
        //     showHideChaptersMobile();
        // }
        if($(window).width() == 768){
            showHideChaptersTab();
        }

        var mobChapname=chapterName;
        if(elementExists('mobChapname')) {
            document.getElementById('mobChapname').innerHTML = mobChapname;
        }
        if($(window).width() < 767){
            $('#chapters-toggle').toggleClass('left');
            $("#book-sidebar").toggleClass('d-none');
            if ($('.read-content').hasClass('offset-md-4')) {
                $('.read-content').removeClass('offset-md-4').addClass('col-md-12');
                $('.read-content').removeClass('col-md-8');
            } else {
                $('.read-content').removeClass('col-md-12').addClass('offset-md-4');
                $('.read-content').addClass('col-md-8');
            }
        }
        scrollTo(0,64);
        if($("#tabread").parents('li').css('display') == 'none'){

            if($("#tabread").hasClass('active')){

                if( $('#content-data-readingMaterial').hide()){
                    $("#taball").tab('show');
                }

            }
        }
    }



</script>
<asset:javascript src="zoom.js"/>

<script>
    function showHideChapters() {
        $('.read-book-chapters').toggleClass('read-book-chapters-slide-left');

        if($('.read-book-chapters').hasClass('read-book-chapters-slide-left')) {
            $('.read-book-chapters').hide();
        } else {
            $('.read-book-chapters').show();
        }

        $('#hideShowDiv').toggleClass('hideShowDiv-slide-left rotated');
        $('.book-read-material').toggleClass('book-read-material-full-width');
    }

    function showHideChaptersMobile() {
        if($('.read-book-chapters').css("margin-left") == "-600px") {
            $('.read-book-chapters').animate({"margin-left": '0'}, 'fast');
            $('.section-btns').animate({"width": '50%'});
            $('#hideShowDivMobile').addClass('rotated');
            $('#hideShowDivMobile').animate({'left' : '60%'}, 'fast');
            $('.chapter-overlay').removeClass('hidden');
            $('body').css({
                'overflow' : 'hidden'
            });
        } else {
            $('.chapter-overlay').addClass('hidden');
            $('.read-book-chapters').animate({"margin-left": '-=600'});
            $('#hideShowDivMobile').removeClass('rotated');
            $('#hideShowDivMobile').animate({'left' : '-27px'}, 'fast');
            $('.section-btns').animate({"width": '100%'});
            $('body').css({
                'overflow' : 'auto'
            });
        }
    }

    function showHideChaptersTab() {
        if($('.read-book-chapters').css("margin-left") == "-500px") {
            $('.read-book-chapters').animate({"margin-left": '0'});
            $('.book-read-material').animate({"width": '100%'});
            $('.section-btns').animate({"width": '45%'});
            $('#hideShowDivTab').addClass('rotated');
            $('#hideShowDivTab').animate({'left' : '45%'});
            $('body').addClass('no-scroll');
        } else {
            $('.read-book-chapters').animate({"margin-left": '-=500'});
            $('.book-read-material').animate({"width": '100%'});
            $('.section-btns').animate({"width": '100%'});
            $('#hideShowDivTab').removeClass('rotated');
            $('#hideShowDivTab').animate({'left' : '0'}, 'fast');
            $('body').removeClass('no-scroll');
        }
    }

    function showHideNotesSection() {
        if($('.notes-creation-wrapper').css("margin-right") == "-600px") {
            $('.notes-creation-wrapper').animate({"margin-right": '0'});
            $('.book-read-material, body').css({
                'overflow-y' : 'hidden'
            });
            $('.notes-list-item-indicator').show();
            $('.individual-note-selection').hide();
            $('.comment-by-user').show();
            $('.export-btn-wrapper').hide();
            $('.export-study-set').html('Export to Revision').removeClass('cancel-export-study-set');
            $('.notes-wrapper-overlay').show();
            setTimeout(function() {
                $('.close-notes-wrapper').show();
            }, 400);
        } else {
            $('.close-notes-wrapper').hide();
            $('.notes-creation-wrapper').animate({"margin-right": '-600px'});
            $('.book-read-material, body').css({
                'overflow-y' : 'auto'
            });
            $('.notes-wrapper-overlay').hide();
        }
    }

</script>


<% if("sage".equals(session["entryController"])){%>
<script>
    <g:remoteFunction controller="wonderpublish" action="getBooksListForUserSage" params="'tags=${book.tags}'" onSuccess='displayRelatedBooks(data);'/>
    $('.read-book-chapters-wrapper li:first').addClass('chapter-name-active');
    $('.read-book-chapters-wrapper').on('click', 'a', function() {
        $('.read-book-chapters-wrapper li').removeClass('chapter-name-active');
        $(this).parents('.chapter-name').addClass('chapter-name-active');
    });

    function displayRelatedBooks(data){
        var books = data.books;
        noOfBooks = books.length;

        if(noOfBooks<=0) return;

        var imgSrc = "";
        var displayStr="";

        for(i=0;i<noOfBooks;i++){
            if(books[i].id==${book.id}) {
                continue;
            }
            if (books[i].coverImage === null || books[i].coverImage == "null" || books[i].coverImage == "") {
                imgSrc = "/assets/booksmojo/img_cover_placeholder.png";
            } else {
                imgSrc = "/funlearn/showProfileImage?id="+books[i].id+"&fileName="+books[i].coverImage+"&type=books&imgType=passport";
            }

            displayStr += "<div class='item related-books-carousel-item'>"+
                "<a href='/sage/doris?siteName=sage&bookId="+books[i].id+"'>" +
                "<div class='book-item'>" +
                "<div class='book-img-wrapper'>"+
                "<img class='book-image' src='"+imgSrc+"' alt=''/>" +
                "</div>" +
                "</div>" +
                "</a>" +
                "</div>";
        }

        document.getElementById('related-books-sage').innerHTML = displayStr;
        $('#related-books-sage').show();
        $( ".related-books-label" ).show();
    }



    var formattedIRData = {};

    function getInstructorResources(){
        <g:remoteFunction controller="wonderpublish" action="getInstructorResources" params="'bookId=${bookId}'" onSuccess = "initializeIRData(data);"/>
    }

    function initializeIRData(data){
        console.log(data);
        formattedIRData.bookDescription = [];
        formattedIRData.detailedContents = [];
        formattedIRData.aboutTheAuthors = [];
        formattedIRData.buyNow = [];

        var length = data.instructorResources.length;
        if(length == 0) {
            //   addpopover();
        }
        for(var i =0; i < length; ++i) {
            var item = data.instructorResources[i];
            var temp = {};
            temp.link = item.link;
            temp.linkName = item.linkName;
            switch(item.subTab){
                case 'bookDescription':
                    formattedIRData.bookDescription.push(temp);
                    break;
                case 'detailedContents':
                    formattedIRData.detailedContents.push(temp);
                    break;
                case 'aboutTheAuthors':
                    formattedIRData.aboutTheAuthors.push(temp);
                    break;
                case 'buyNow':
                    formattedIRData.buyNow.push(temp);
                    break;
            }
        }

        if(formattedIRData.bookDescription.length>0) {
            document.getElementById("bookDescription").innerHTML = " <a href='"+formattedIRData.bookDescription[0].link+"' class=\"description-list-item-link\" target='_blank'>book description <i class=\"material-icons\">chevron_right</i></a>";
        }
        if(formattedIRData.detailedContents.length>0) {
            document.getElementById("detailedContents").innerHTML = " <a href='"+formattedIRData.detailedContents[0].link+"' class=\"description-list-item-link\" target='_blank'>Detailed Contents <i class=\"material-icons\">chevron_right</i></a>";
        }
        if(formattedIRData.aboutTheAuthors.length>0) {
            document.getElementById("aboutTheAuthors").innerHTML = " <a href='"+formattedIRData.aboutTheAuthors[0].link+"' class=\"description-list-item-link\" target='_blank'>About The "+authorTag+" <i class=\"material-icons\">chevron_right</i></a>";
        }
        if(formattedIRData.buyNow.length>0) {
            document.getElementById("buyNow").innerHTML = " <a href='"+formattedIRData.buyNow[0].link+"' class=\"description-list-item-btn\" target='_blank'>Buy now</a>";
        }
    }
    getInstructorResources();
</script>
<%}%>

<script>
    function clickIE() {if (document.all) {return false;}}
    function clickNS(e) {if
    (document.layers||(document.getElementById&&!document.all)) {
        if (e.which==2||e.which==3) {return false;}}}
    if (document.layers)
    {document.captureEvents(Event.MOUSEDOWN);document.onmousedown=clickNS;}
    else{document.onmouseup=clickNS;document.oncontextmenu=clickIE;}

    document.oncontextmenu=new Function("return false")
    // disables ctrl + p....
    $(function(){
        $(document).keydown(function(objEvent) {
            if (objEvent.ctrlKey) {
                if (objEvent.keyCode == 80) {
                    objEvent.preventDefault();
                    objEvent.stopPropagation();
                    return false;
                }
            }
        });
    });
    $('.loading-icon').hide();
    setTimeout(function() {
        $('#studentInformation').modal('show');
    }, 500);
    $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
        formCancelNotes();

    });
    $('#read,#userNotes,#long-qa,#qa,#learn,#flash-cards,#create-test,#discuss').bind('copy paste', function(e) {
        e.preventDefault();
    });
</script>


<g:render template='/wonderpublish/testgenModal'></g:render>

<g:render template="/wonderpublish/testgeneratorscripts"></g:render>


<g:render template="/wonderpublish/addNotesScripts"></g:render>
<script>
    function closeNotes() {
        $('#overlay').addClass('d-none');
        $('.export-notes').addClass('d-none');
        $('#notesMenu').removeClass('active');
    }
    $('#tabusernotes,#tabquiz,#tabweblinks,#tabStudySet,#tabvideo').on('click',function () {
        $('#book-read-material').removeClass('white-bg black-bg sepia-bg grey-bg').addClass('book-read-material'+' ');
        $('.color-mode-list-item').removeClass('active');
        $('#formatMenu,#notesMenu,#prev-section,#next-section').hide();
    });

    <% if("sage".equals(session["entryController"])){%>
    $('#chapter-details-tabs a').click(function (e) {
        var button=$(e.target).attr('id');
        if(button=="tablongqa") {
            for (var i = 0; i < formattedTopicData.qAndA.length; ++i) {
                var data = formattedTopicData.qAndA[i];
                var qaresId = data.id;
                <g:remoteFunction controller="funlearn" action="updatedorisResourceView"  params="'resId='+qaresId" />
                break;
            }
        }
        else if(button=="tab-short-qa") {
            for (var i = 0; i < formattedTopicData.shortQAndA.length; ++i) {
                var data = formattedTopicData.shortQAndA[i];
                var shresId = data.id;
                <g:remoteFunction controller="funlearn" action="updatedorisResourceView"  params="'resId='+shresId" />
                break;
            }
        }
        else if(button=="tabquiz"){
            for (var i = 0; i < formattedTopicData.quizs.length; ++i) {
                var data = formattedTopicData.quizs[i];
                var qresId = data.id;
                <g:remoteFunction controller="funlearn" action="updatedorisResourceView"  params="'resId='+qresId" />
                break;
            }
        }
        else if(button=="tab-flash-cards"){
            for (var i = 0; i < formattedTopicData.keyValues.length; ++i) {
                var data = formattedTopicData.keyValues[i];
                var fresId = data.id;
                <g:remoteFunction controller="funlearn" action="updatedorisResourceView"  params="'resId='+fresId" />
                break;
            }
        }
        else if(button=="tabweblinks"){
            for (var i = 0; i < formattedTopicData.weblinks.length; ++i) {
                var data = formattedTopicData.weblinks[i];
                var wbresId = data.id;
                <g:remoteFunction controller="funlearn" action="updatedorisResourceView"  params="'resId='+wbresId" />
                break;
            }
        }
        else if(button=="tabvideo"){
            for (var i = 0; i < formattedTopicData.relVidoes.length; ++i) {
                var data = formattedTopicData.relVidoes[i];
                var rvresId = data.id;
                <g:remoteFunction controller="funlearn" action="updatedorisResourceView"  params="'resId='+rvresId" />
                break;
            }
        }
        e.preventDefault();
        $(this).tab('show');
    });
    <%}%>
</script>
<script>
    document.onkeydown = function(e) {
        if(event.keyCode == 123) {
            return false;
        }
        if(e.ctrlKey && e.shiftKey && e.keyCode == 'I'.charCodeAt(0)){
            return false;
        }
        if(e.ctrlKey && e.shiftKey && e.keyCode == 'J'.charCodeAt(0)){
            return false;
        }
        if(e.ctrlKey && e.keyCode == 'U'.charCodeAt(0)){
            return false;
        }
    }

    var $document = $(document),
        $element = $('body'),
        className = 'hasScrolled';

    $document.scroll(function() {
        if ($document.scrollTop() >= 64) {
            // user scrolled 50 pixels or more;
            // do stuff
            $element.addClass(className);
        } else {
            $element.removeClass(className);
        }
    });
</script>

<script>
    var defaultSiteName="${grailsApplication.config.grails.appServer.default}";
</script>




</body>
</html>

