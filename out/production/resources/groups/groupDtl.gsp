<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/books/navheader_new"></g:render>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta2/css/all.min.css"/>
<link href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css" rel="stylesheet" type="text/css">
<link href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css" rel="stylesheet" type="text/css">
<link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.css"/>
<asset:stylesheet href="ckeditor5/styles.css"/>
<asset:stylesheet href="groups/groupsStyle.css" />

<style>
div#post-slider .dropdown-menu.show li img{
    display:inline-block !important;
}
.slick-track{
    display: flex !important;
}

.slick-slide {
    height: inherit !important;
}
img.user-pinnedPost-img.mr-3 {
    width:20px;
    position:absolute;
    right:7%;
}
.slick-slide {
    margin-right:20px;
    margin-left:2px;
}
.slick-track {
    padding:10px 0px;
}
.slick-slide:not(.slick-current) {
    /* Make slides other than the current one translucent */
    opacity: 0.4;
}
.slick-list{padding:0 20% 0 0 !important;}

div#slider-parent {
    width:38%;
    margin:auto;
    margin-bottom:2%;
}
button.slick-prev.slick-arrow {
    background: #ffffff;
    width: 30px;
    height: 30px;
    box-shadow: 0px 0px 8px rgba(0, 0, 0 ,0.4);
    border-radius: 50%;
    left: -20px;
    z-index:10;
}
button.slick-prev.slick-arrow:before,.slick-next:before {
    width:7px;
    height:7px !important;
    font-size:5px !important;
}

button.slick-next.slick-arrow {
    background: #ffffff;
    width: 30px;
    height: 30px;
    box-shadow: 0px 0px 8px rgb(0 0 0 / 25%);
    border-radius: 50%;
    z-index:10;
}
a.pinPostBtn img,a.unpinPostBtn img{
    width:15px;
    margin-right:2% !important;
}
.modal {
    background: rgba(0, 0, 0, 0.8) !important;
}
@media only screen and (max-width: 767px) {
    .textarea-container{
        top: 0;
    }
    div#leaveGroupAdmin .modal-dialog.modal-dialog-centered {
        align-items: flex-end;
        padding-bottom: 0px;
        max-width: 100%;
        margin: 0px;
        height: 100%;
    }
    label#showCoverImage {
        position: absolute;
        right: 12% !important;
        top: 33%;}
}
body{display:none;}

.modal{
    z-index:10000;}
div#leaveGroupAdmin button.close span{
    color:white;
    font-weight:100;}
div#leaveGroupAdmin button.close {
    position:absolute;
    top:-20%;
    right:2%;
    opacity:1;}
div#leaveGroupAdmin h3 {
    font-family: Poppins;
    font-size: 20px;
    font-style: normal;
    font-weight: 400;
    line-height: 30px;
    letter-spacing: -0.015em;
    text-align: left;}
@media (max-width:575px) {
    div#post-slider .time{
        font-size:9px;
    }  div#post-slider p.mr-2.text-capitalize {
        font-size:12px;
    }
    #postList {
        position: relative;
        top: 0px !important;
    }
    .header-Message.mt-4 p{
        width:92% !important;
    }
    .Titlewall.m-3{
        width:93% !important;
    }
    div#slider-parent {
        width:92%;
        margin:auto;
        margin-bottom:2%;
    }
    button.slick-prev.slick-arrow {
        left: -11px !important;
    }
}
.posts-card.p-4.mt-3 {
    z-index:0 !important;
}
.Titlewall.m-3 {
    width:38% ;
    margin:auto !important;
}
p.adminclass.ml-2 {
    font-size: 12px;
    font-style: italic;
    text-align: right;
    color: #ae3691;
    display: inline !important;
    padding: 3px;
    background: rgba(174,054,145,0.2);
    border-radius: 5px;
}
div#leaveGroupAdmin p{
    font-family: Poppins;
    font-size: 11px;
    font-style: normal;
    font-weight: 400;
    line-height: 17px;
    letter-spacing: -0.015em;
    text-align: left;}
div#leaveGroupAdmin a {
    background: rgba(39, 174, 96, 1);
    width:100% !important;
    border-radius: 5px;
    display:flex;
    justify-content:center;
    color:white;
    padding:2%;
    margin-top:5%;}

button.btn.btn-outline-primary.mt-0.makeAd:focus {
    color: #fff;
    background-color: #007bff;}
label#showCoverImage {
    position:absolute;
    right:5%;
    top:23%;
    background: rgba(255, 255, 255, 0.35);
    box-shadow: 0px 0px 10px rgba(0 ,0, 0,0.8);
    border-radius: 50%;
    padding: 5px;
    display: flex;}
@media (min-width:575px) {
    .modal-dialog.modal-dialog-centered.ErrorDialogue {
        max-width: 22% !important;}  }
.modal-dialog.modal-dialog-centered.ErrorDialogue span{
    float:right;
    margin-right:2%;}

.modal-dialog.modal-dialog-centered.ErrorDialogue h3 {
    text-align:center;
    color:#dc3545;
    font-size: 15px;
    font-weight: 500;}
.modal-dialog.modal-dialog-centered.ErrorDialogue .modal-footer {
    padding:3px !important;}

.modal-dialog.modal-dialog-centered.ErrorDialogue button.btn.btn-secondary {
    background:#dc3545;
    outline:none;
    border:none;}
.optionList li  {
    color: rgba(68, 68, 68, 0.85);
    font-size: 12px;cursor: pointer;}
.comments-info:not(.comments-info:first-child) {
    display:none;}

</style>

<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>

<div class="page-main-wrapper mdl-js pt-5 wonderslate_wall">

<div class="groupBanner" id="groupBackground">
    <div class="container">
        <div class="groupBannerContent d-flex align-items-center justify-content-between">
            <div class="header d-flex align-items-center">
                <a href="/groups/index" class="back-btn d-flex align-items-center mr-2">
                    <img src="${assetPath(src: 'groups/back-white.svg')}" class="mr-2">
                    Back
                </a>
                <label for="upload-photo" style='margin-bottom:0px; display:none' id="showCoverImage">
                    %{--                <img style="width:20px" src="${assetPath(src: '/groups/edit.svg')}">--}%
                    <i class="material-icons" style="color:white;font-size:21px">edit</i>
                    <input type="file" name="file" id="upload-photo" class="d-none" accept="image/*"/>
                    %{--                <a href="javascript:" class="notification d-flex">--}%
                    %{--                    <span id="notificationCount" class="notificationCount">0</span>--}%
                    %{--                    <img src="${assetPath(src: 'groups/Notifications.svg')}">--}%
                    %{--                </a>--}%
            </div>
            <div class="reportbtn d-flex justify-content-between align-items-center" id="reportAndSettings">

            </div>
        </div>


        <div class="grpBannerName">
            <h3 class="text-white mr-3" id="groupTitle"></h3>
            <p id="groupDescription"></p>
        </div>
    </div>
</div>

<div class="adminoptions mt-4" id="adminoptions" style="display: none;">
    <div class="container">
        <div class="header-wrapper admin-panel-header d-flex align-items-center justify-content-between">
            <a onclick="javascript:showAdminSettings();" class="text-white settings-btn settings-icon ml-2" style="display: none;">
                <img src="${assetPath(src: 'groups/settings.svg')}" class="" >
            </a>
        </div>
        <div class="header-title mt-3">
            <h4>admin Settings</h4>
        </div>
        <div class="adoptionslist">
            <ul class="optionList pl-0">
                <li>
                    <a href="/groups/reported?groupId=${params.groupId}" class="d-flex align-items-center">
                        <img src="${assetPath(src: 'groups/reportedicon.svg')}" class="mr-3">
                        Reported
                    </a>
                </li>
                <li>
                    <a href="/groups/memberRequests?groupId=${params.groupId}" class="d-flex align-items-center">
                        <img src="${assetPath(src: 'groups/memberreq.svg')}" class="mr-3"> Members
                    Request
                    </a>
                </li>
                <li>
                    <a href="/groups/members?groupId=${params.groupId}" class="d-flex align-items-center">
                        <img src="${assetPath(src: 'groups/members.svg')}" class="mr-3"> Members
                    </a>
                </li>
                <li>
                    <a href="/groups/groupCreate?mode=edit&groupId=${params.groupId}" class="d-flex align-items-center">
                        <img src="${assetPath(src: 'groups/edit.svg')}" class="mr-3"> Edit group
                    details
                    </a>
                </li>

                <%if(adminCount>1){%>
                <li>
                    <a href="javascript:showExitGroupModal();" class="d-flex align-items-center direct-leave">
                        <img src="${assetPath(src: 'groups/LeaveGrp.svg')}" class="mr-3"> Exit Group
                    </a>
                </li>
                <%}else if(membersCount>0 && adminCount==1) {%>
                <li>
                    <a  class="d-flex align-items-center indirect-leave">
                        <img src="${assetPath(src: 'groups/LeaveGrp.svg')}" class="mr-3"> Exit Group
                    </a>
                </li>
                <%}%>
            </ul>
        </div>
    </div>
</div>

<div class="grp-btns container w-50 d-flex justify-content-center">
    <a href="javascript:showShareModal()"  class="invite-btn  align-items-center justify-content-center" id="invite-btn">
        <img src="${assetPath(src: 'groups/addIcon.svg')}" class="mr-2" > Invite Friend
    </a>
    <a id="joinGroup" href="javascript:joinGroup();" class="join-btn align-items-center justify-content-center" style="display: none">
        <img src="${assetPath(src: 'groups/addIcon.svg')}" alt="" class="mr-2" /> Join Group
    </a>
    <a id="requestJoinGroup" href="javascript:showRequestModal();" class="request-btn align-items-center justify-content-center" style="display: none">
        <img src="${assetPath(src: 'groups/addIcon.svg')}" alt="" class="mr-2" /> Request to Join
    </a>
    <a id="admin" href="javascript:void(0);" class="text-white align-items-center justify-content-center">
        You're admin
    </a>
    <div class="dropdown joined-btn" style="display: none">
        <button class="btn w-100 h-100 joined-dropdownBtn dropdown-toggle" type="button" id="dropdownMenuButton" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
            Joined
        </button>
        <div class="dropdown-menu drpDwnMenu" aria-labelledby="dropdownMenuButton">
            <a class="dropdown-item" href="javascript:showReportGroupModal();" id="repGroup">
                <i class="fas fa-flag mr-1"></i> Report Group
            </a>
            <a class="dropdown-item" href="javascript:showExitGroupModal();" id="exGroup">
                <i class="fas fa-sign-out-alt"></i> Exit Group
            </a>
        </div>
    </div>
</div>

<div class="emptyState text-center mt-2 d-flex text-center align-items-center justify-content-center">
    <i class="fas fa-lock-open mr-2" id="lock"></i>
    <p class="statusText">This is a public group</p>
</div>
<div class="d-flex align-items-center flex-column container mt-5" id="unjoinedPrivate">
    <img src="${assetPath(src: 'groups/lockgif.gif')}" style="width: 75px"/>
    <p style="font-size: 18px" class="text-danger text-center" id="unjoinedText">Please join the group to explore!</p>
</div>

<div class="textarea-container container mt-4 width-size" id="privilage">
    <textarea id="postEditor" class="p-3"  rows="4"  placeholder="Post"></textarea>
    <label for="upload-file">
        <img src="${assetPath(src: 'groups/attach-1.svg')}">
    </label>
    <input type="file" name="file" id="upload-file" accept="image/*,.pdf"/>
    <button type="button" class="btn postBtn" onclick="javascript:createPost();">Post</button>
</div>

<p id="err" class="container width-size text-danger" style="display: none">Please select file less than 8Mb</p>

<p id="errorPostEditor" class="form-text error-text text-danger hidden container my-3 width-size"></p>
<p id="errorUploadFile" class="form-text error-text text-danger hidden container my-3 width-size"></p>
<div style="display: none" class="container mt-4 width-size" id="image-preview">
    <div class="col-12 col-md-6 p-0 position-relative img-preview">
        <img id="post-image-preview" style="width: 100%; height:50px!important;" src="" alt="your image"/>
        <p id="imgFileName"></p>
        <button type="button" class="btn btn-sm close-img p-0" onclick="javascript:removeSelectedPostImg();" > <i class="material-icons">cancel</i> </button>
    </div>
</div>

<div style="display: none" class="container mt-3 width-size" id="file-preview">
    <div class="col-12 p-0 position-relative" id="fileNamePreview">

    </div>
</div>
<div class="slider-parent mt-3" id="slider-parent" style="display: none">
    <div class="tootltip " style="width:30%">
        <p data-toggle="tooltip" title="Your admin has pinned these items for the group to see." style="text-align: left;font-size: 14px;font-family: poppins, sans-serif;font-weight: bold;">Pinned Post</p>
    </div>
</div>
<section class="postList container width-size  p-0">

    <div id="postList" class="postList container">

    </div>

    <div id="showMore" class="text-center mt-4" style="display: none;">
        <a id="showMorePosts" href="javascript:showMorePosts();" class="btn btn-success">Show More</a>
    </div>

</section>

<div id="successMsg" class="alert-message hide"></div>
<div id="errorMsg" class="alert-message hide"></div>

<div id="groupReportModal" class="modal fade" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header border-bottom-0">
                <h5 class="modal-title">Report Group</h5>
            </div>
            <div class="modal-body">
                <textarea class="form-control" id="reportReason" type='text'  maxLength='230' placeholder="Enter reason here.. (Optional)"></textarea>
                <div id="reportGroupReasonMsg" class="mt-2"></div>
            </div>
            <div class="modal-footer border-top-0">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-danger" onclick="javascript:reportGroup();" id="groupReportBtn">Report</button>
            </div>
        </div>
    </div>
</div>

<div id="joinRequestModal" class="modal fade" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header border-bottom-0">
                <h5 class="modal-title">Request to Join</h5>
            </div>
            <div class="modal-body">
                <textarea class="form-control" id="joinReason" type="text"  maxLength="230" placeholder="Enter reason here"></textarea>
                <div id="joinReasonMsg" class="mt-2"></div>
            </div>
            <div class="modal-footer border-top-0">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-success reqBtn" onclick="javascript:requestJoinGroup();">Submit</button>
            </div>
        </div>
    </div>
</div>

<div id="groupPostReportModal" class="modal fade" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header border-bottom-0">
                <h5 class="modal-title">Report Post</h5>
            </div>
            <div class="modal-body">
                <div id="reportReasonDiv">

                </div>
                <div id="reportPostReasonMsg" class="mt-2"></div>
            </div>
            <div class="modal-footer border-top-0">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-danger" id="reportPostBtn" data-dismiss="modal">Report Post</button>
            </div>
        </div>
    </div>
</div>

<div id="groupPostDeleteModal" class="modal fade" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header border-bottom-0">
                <h5 class="modal-title">Delete Post</h5>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this post?</p>
            </div>
            <div class="modal-footer border-top-0">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-danger" id="deletePostBtn">Delete Post</button>
            </div>
        </div>
    </div>
</div>

<div id="groupUserReportModal" class="modal fade" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header border-bottom-0">
                <h5 class="modal-title">Report User</h5>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to Report this user?</p>
            </div>
            <div class="modal-footer border-top-0">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-danger" id="reportUserBtn" onclick="javascript:reportUser()">Report User</button>
            </div>
        </div>
    </div>
</div>

<div id="groupPostEditModal" class="modal fade" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header border-bottom-0">
                <h4 class="modal-title" style="color:#9A309B">Edit Post</h4>
            </div>
            <div class="modal-body">
                <div class="textarea-container mt-3">
                    <textarea id="editPostEditor" class="editor" rows="5" placeholder="Edit post"></textarea>

                    <button type="button" class="btn" id="editPostBtn" disabled>Update</button>
                </div>
                <p id="errEd" class="text-danger" style="display: none">Please select file less than 8Mb</p>
                <p id="errorEditPostEditor" class="form-text error-text text-danger hidden my-3"></p>
                <p id="errorEditUploadFile" class="form-text error-text text-danger hidden my-3"></p>

                <div class="mt-4" id="editPostImagePreview"  style="display: none;">
                    <div class="col-12 col-md-6 p-0 position-relative" >
                        <button type="button" class="edit-content" style="float: right;border: none;background: transparent;cursor:pointer;" onclick="javascript:removecontent()" aria-label="Close">
                            <span aria-hidden="true" style="font-size:25px">&times;</span>
                        </button>
                        <img id="editPostImage"  src="" class="editPostImg rounded">
                    </div>
                </div>
                <div style="display: none" class="mt-3 col-12 col-md-6 p-0 "   id="editPostFilePreview">
                    <button type="button" class="edit-content-1"  style="cursor:pointer;border: none;background: transparent;" onclick="javascript:removecontent()" aria-label="Close">
                        <span aria-hidden="true" style="font-size:25px">&times;</span>
                    </button>
                    <div class="col-12 p-0 position-relative"   id="editPostFileName">

                    </div>
                </div>
            </div>
            <div class="modal-footer border-top-0">
                <label for="upload-file-2"  style="cursor:pointer;">
                    <img src="${assetPath(src: 'groups/attach-1.svg')}">
                </label>
                <input type="file" class="d-none" name="file" id="upload-file-2" accept="image/*,.pdf"/>

                <button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>
            </div>
        </div>
    </div>
</div>

<div id="groupUserExitModal" class="modal fade" tabindex="-1" role="dialog">
    <div class="modal-dialog  modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header border-bottom-0">
                <h5 class="modal-title">Exit Group</h5>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to exit the group?</p>
            </div>
            <div class="modal-footer border-top-0">
                <button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="exitFromGroup">Exit</button>
            </div>
        </div>
    </div>
</div>

<div class="modal " id="leaveGroupAdmin" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
            <div class="modal-body">
                <h3>Select  admin</h3>
                <p>You were the admin of this group. Before you leave, you’ll have to assign a new admin from the members.</p>
                <div class="buttonmodal"> <a href="/groups/members?groupId=${params.groupId}&exitFlow=true">Okay, Next!</a></div>

            </div>

        </div>
    </div>
</div>
<div class="modal " id="Error1" class="ErrorDialogue" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered ErrorDialogue" role="document">
        <div class="modal-content">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
            <div class="modal-body">
                <h3>File size exceeds 2mb.</h3>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<div class="modal " id="Error2" class="ErrorDialogue" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered ErrorDialogue" role="document">
        <div class="modal-content">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
            <div class="modal-body">
                <h3>Please select images only.</h3>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
<div class="modal " id="Error3" class="ErrorDialogue" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered ErrorDialogue" role="document">
        <div class="modal-content">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
            <div class="modal-body">
                <h3>File Name must not contain any space and special characters.</h3>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

</div>

<g:render template="/books/footer_new"></g:render>
<g:render template="/resources/shareContent"></g:render>
<script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js"></script>
<script type="text/javascript" src="//cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.min.js"></script>



<script>

    <%if(session["userdetails"]==null){%>
    $(".loading-icon").addClass("hidden")
    checkLoginAndProceed();
    <%} else { %>

    // $(document).ready(function(){

    $("#postEditor").keyup(function () {
        $("#errorPostEditor, #errorUploadFile").addClass('hidden');
    });
    $("#editPostEditor").keyup(function () {
        $("#errorEditPostEditor, #errorEditUploadFile").addClass('hidden');
    });
    $("#joinReason").keyup(function () {
        $("#joinReasonMsg").hide();
    });
    $('.modal').on('hidden.bs.modal', function (e) {
        $("#reportPostReasonMsg, #reportGroupReasonMsg").hide();
    });
    // });

    $("#report-option").hide();
    $("#comment-section").hide();
    $("#reply-section").hide();
    $("#more-option").click(function () {
        $("#report-option").toggle();
    });

    $(".comment-btn").click(function () {
        $("#comment-section").toggle();
    });

    $(".reply-btn").click(function () {
        $("#reply-section").toggle();
    });


    var dataObj;
    var groupId;
    var reason;
    var username;
    var currentUsername = "${session['userdetails'].username}";
    var validFileSize = true;
    var validFileFormat = true;
    var fileType = "";
    var imageSrc;
    var postId;
    var description;
    var commentedDate;
    var createCKEditor, updateCKEditor;
    var postImageSrc;
    var postFileSrc;
    var userExist;
    var admin = false;
    var userType
    var pageNo = 0;
    var tempPageNo;
    var icon;
    var commentUserName;
    var fileEmpty = false;
    var postLikedState;
    var postCount
    var postedDate;
    var joined = false;
    var groupType = 'group';
    var currentDate;
    var checkPostedDate = false;
    var checkCommentedDate = false;
    var pinned = false;
    var dict = {};
    var commentdict = {};
    var batchId = null;
    var instituteId =  null;


    groupId = '${groupId}';



    function groupDetails(){
        $(".loading-icon").removeClass("hidden");
        var mode=location.search.split('mode=')[1]?location.search.split('mode=')[1]:'';
        dataObj = {
            groupId: groupId,
            mode:mode,
        };
        $.ajax({
            url: "/groups/getGroupDetailsById",
            type: "POST",
            contentType: "application/json",
            dataType:"json",
            data: JSON.stringify(dataObj),
            success: function (data) {
                showGroupBasicDetails(data);
                showPosts();
            },
        });
    }

    groupDetails();

    function showGroupBasicDetails(data) {
        var privilage = data.allPost;
        userType = data.userType;
        userExist = data.userExist;
        batchId = data.batchId;
        instituteId = data.instituteId;
        var privacyType = data.privacyType.toLowerCase();
        setTimeout(function () {
            $(".loading-icon").addClass("hidden")
        }, 1200)
        document.getElementById("groupTitle").innerText = data.groupName;
        document.getElementById("groupDescription").innerText = data.description;
        username = data.createdBy;
        if (data.colorCode != null) {
            imageSrc = "/assets/groups/" + data.colorCode;
            $("#groupBackground").attr("style", "background-image:url(" + imageSrc + ")");
        } else if (data.image != null) {
            imageSrc = "/groups/showGroupImage?id=" + data.id + "&fileName=" + data.image;
            $("#groupBackground").attr("style", "background-image:url(" + imageSrc + ")");
        }
        if (privilage == "false" && userType != 'admin') {
            $("#postList").css({top: 0, position: 'relative'});
            $('.grp-btns.container.w-50.d-flex.justify-content-center').after($("<div class='header-Message mt-4'><p style='width:38%;margin:auto;text-align:center;background: rgba(218, 218, 218, 0.5);border-radius:5px; border: 1px solid rgba(146, 146, 146, 1) ;color:rgba(68, 68, 68, 0.6);'>Only admins will be able to post on this group.</p></div> "));
        }
        if (userType == 'admin' || userType == 'super_admin' ) {
            admin = true;
            $("#requestJoinGroup, #joinGroup, .joined-btn").css("display", "none");
            $('#showCoverImage').show();
            document.getElementById("reportAndSettings").innerHTML = "<a onclick='javascript:showAdminSettings();' class='text-white settings-btn ml-2'>" +
                "<img src='${assetPath(src: 'groups/settings.svg')}'></a>"
        } else if (username != currentUsername) {
            $(".joined-btn").css("display", "flex");
            $("#admin").css("display", "none");
        } else if (privacyType == "private") {
            $("#requestJoinGroup").css("display", "flex");
            $("#joinGroup").css("display", "none");
        } else if (privacyType == "public") {
            $("#joinGroup").css("display", "flex");
            $("#requestJoinGroup").css("display", "none !important");
            $("#admin").hide();
        }
        if (privilage == "false" && userType != 'admin') {
            $('#privilage').hide();
        }

        if (userExist == 'yes') {
            joined = true;
            $("#joinGroup").css("display", "none");
            $("#requestJoinGroup").css("display", "none");
            $("#invite-btn").css("display", "flex");
            $(".emptyState").removeClass("d-flex").addClass("d-none")
            $("#unjoinedPrivate").addClass("d-none").removeClass("d-flex")
        }
        if (userExist == 'yes' && currentUsername == username && userType != 'admin' ) {
            joined = true;
            $("#joinGroup").css("display", "none");
            $("#requestJoinGroup").css("display", "none");
            $("#invite-btn").css("display", "flex");
            $(".emptyState").removeClass("d-flex").addClass("d-none")
            $("#unjoinedPrivate").addClass("d-none").removeClass("d-flex")
            $(".joined-btn").css("display", "flex");

        }
        if (userExist == 'yes' && privacyType == "private" && userType != 'admin') {
            $("#admin").hide();
            $(".joined-btn").css("display", "show");
        }
        if (privacyType == "private" && userExist == 'No') {
            $("#joinGroup").css("display", "none");
            $("#requestJoinGroup").css("display", "flex");
            $(".invite-btn").css("display", "none");
            $("#postList").hide();
            document.getElementById("postList").innerHTML = "";
            $("#showMore").hide();
            $(".statusText").text("This is a private group")
            $("#lock").addClass("fa-lock").removeClass("fa-lock-open")
            if(batchId!=null||instituteId!=null){
                window.location.href = '/books';
            }
        }
        if (privacyType == "public" && userExist == 'No') {
            $("#requestJoinGroup").css("display", "none");
            $(".invite-btn").css("display", "flex");
            $("#privilage").hide();
            $("#joinGroup").css("display", "flex");
            $(".joined-btn").css("display", "none");
            $("#unjoinedPrivate").addClass("d-none").removeClass("d-flex")
        }
        else if (userExist == 'No') {
            $(".textarea-container").hide();
            $(".joined-btn").css("display", "none");
            $("#invite-btn").css("display", "none");
            $(".emptyState").show()
            $("#showMorePosts").hide()
            $("#admin").hide();
        }
        if(data.groupType == 'channel')
        {
            groupType = 'channel';
            $("#invite-btn").css("display", "none");
            $('.header-Message.mt-4').hide();
            $(".joined-btn").css("display", "none");
            $(".btn-group.dropleft").css("display", "none");
            $("#admin").hide();
            $(".groupBanner").hide();
            $(".admin-header").hide();
            $("#slider-parent").css("display", "block");
            if(data.batchId==null&&data.instituteId==null) {
                $('#privilage').before($("<div class='Titlewall m-3'><a href='/books/home' class='back-btn d-flex mr-2'> <i class='material-icons-round mr-1'>keyboard_backspace</i> Back </a><h4><strong>Wonderslate Wall</strong></h4></div> "));
                $('#postList').before($('<div class="feeds-header"><h4 class="px-4"><strong>Feed</strong></h4></div>'));
            }else{
                $('#privilage').before($("<div class='Titlewall m-3'><a href='/instituteHome?instituteId=${params.instituteId}' class='back-btn d-flex mr-2'> <i class='material-icons-round mr-1'>keyboard_backspace</i> Back </a><h4><strong>"+data.groupName+"</strong></h4></div> "));
                $('#postList').before($('<div class="feeds-header"><h4 class="px-4"><strong></strong></h4></div>'));
            }
        }
        if(data.groupType != 'channel' && userExist == 'yes'){
            $("#slider-parent").css("display", "block");
            $('#postList').before($('<div class="feeds-header"><h4 class="px-4"><strong>Feed</strong></h4></div>'));
        }

        if(userType == 'super_admin' && data.groupType != 'channel' )
        {
            $('#privilage').hide();
            $(".joined-btn").css("display", "none");
            $("#admin").hide();
            $('.adoptionslist ul li:nth-child(5)').hide();
            $('.adoptionslist ul li:nth-child(4)').hide();
            $('.adoptionslist ul li:nth-child(3)').hide();
            $('.adoptionslist ul li:nth-child(2)').hide();
            $("#invite-btn").css("display", "none");
            $("label#showCoverImage").css("display", "none");
            $("#adminoptions").css("height", "100px !important");

        }
    }

    function forcejoin() {
        if (userExist == 'No') {
            swal({
                title: "",
                text:"Please join the Group.",
                allowOutsideClick: true,
                showConfirmButton: true,
                showCancelButton: true,
                confirmButtonColor: "#27AE60",
                confirmButtonText: "Join Group",
                cancelButtonText: "cancel",
                closeOnConfirm: true,
                closeOnCancel: true,
                customClass: '',
            }, function () {
                joinGroup();
            });
            $('.input-group input[type="text"]').val('');
        }
    }

    function reqStatus(){
        <g:remoteFunction controller="groups" action="getUserRequestStatus" params="'groupId='+groupId" onSuccess="showStatus(data)" />
    }
    reqStatus();
    function showStatus(data){
        if (data.status == "Requested"){
            $("#requestJoinGroup").text("Requested").attr('href','javascript:void(0);');
            $(".statusText").text("You are requested to join the group")
            $("#unjoinedText").text("Please wait till the admin accepts your request")

        }
    }

    function joinGroup() {
        dataObj = {
            groupId: groupId
        };
        $.ajax({
            url: "/groups/joinGroup",
            type: "POST",
            contentType: "application/json",
            dataType:"json",
            data: JSON.stringify(dataObj),
            success: function (data) {
                window.location.href = "/groups/groupDtl?groupId="+groupId;
                hideJoinGroup(data);
            },
        });
    }

    function hideJoinGroup(data){
        $("#joinGroup").css("display", "none");
    }

    function showReportGroupModal() {
        $('#groupReportModal').modal('show').on('shown.bs.modal', function (e) {
            $("#reportReason").focus();
        });
    }

    function reportGroup() {
        $('.loading-icon').removeClass('hidden');
        reason = document.getElementById("reportReason").value;
        reason = reason.replace(/([\u2700-\u27BF]|[\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2011-\u26FF]|\uD83E[\uDD10-\uDDFF])/g, '');
        dataObj = {
            groupId: groupId,
            reason: reason,
        };
        $.ajax({
            url: "/groups/reportGroupAsSpam",
            type: "POST",
            contentType: "application/json",
            dataType:"json",
            data: JSON.stringify(dataObj),
            success: function (data) {
                if(data.status == 'OK') {
                    $('.loading-icon').addClass('hidden');
                    document.getElementById("reportGroupReasonMsg").innerHTML = "<p class='text-success'>Reported!</p>";
                    $("#reportReason").val('');
                    $("#reportReason").hide()
                    $("#groupReportBtn").hide()
                    $("#reportGroupReasonMsg").show();
                }
            },
        });
    }

    function showRequestModal() {
        $('#joinRequestModal').modal('show').on('shown.bs.modal', function (e) {
            $("#joinReason").focus();
        });
    }
    function requestJoinGroup() {
        var reason = document.getElementById("joinReason").value;
        reason = reason.replace(/([\u2700-\u27BF]|[\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2011-\u26FF]|\uD83E[\uDD10-\uDDFF])/g, '');
        dataObj = {
            groupId: groupId,
            reason: reason
        };
        $('.loading-icon').removeClass('hidden');
        $.ajax({
            url: "/groups/joinGroup",
            type: "POST",
            contentType: "application/json",
            dataType: "json",
            data: JSON.stringify(dataObj),
            success: function (data) {
                document.getElementById("joinReasonMsg").innerHTML = "<p class='text-success'>Requested successfully!</p>";
                $("#joinReason").val('');
                $("#joinReasonMsg").show();
                $("#joinReason").hide()
                $(".reqBtn").hide()
                reqStatus();
                $('.loading-icon').addClass('hidden');
            },
        });
    }

    function showPostReportModal(id) {
        $('#groupPostReportModal').modal('show').on('shown.bs.modal', function (e) {
            $("#reportPostReason"+id).focus();
        });
        document.getElementById("reportReasonDiv").innerHTML = "<textarea class=\"form-control\" id=\"reportPostReason"+id+"\" type='text'  maxLength='230' rows=\"5\" placeholder=\"Enter reason here.. (Optional)\"></textarea>";
        $("#reportPostBtn").attr('onclick', 'javascript:reportPost('+id+');');
    }

    function deletePostModal(id){
        $("#groupPostDeleteModal").modal('show');
        $("#deletePostBtn").attr('onclick', 'javascript:removePostFromGroup('+id+');');
    }

    function showReportUserModal(username){
        $('#groupUserReportModal').modal('show');
        $("#reportUserBtn").attr('onclick', "javascript:reportUser('"+username+"');");

    }

    function editPostModal(id){
        $("#groupPostEditModal").modal("show");
        $(".modal-backdrop").hide();
        $("#editPostBtn").attr('onclick', 'javascript:updatePost('+id+');');
        getPostDetailsById(id);
        fileEmpty=false;
    }

    function getPostDetailsById(id) {
        <g:remoteFunction controller="groups" action="getPostDetailsById" params="'postId='+id" onSuccess="showPostDetails(data)"/>
    }

    function showPostDetails(data) {
        if(data.description != null) {
            $("#editPostEditor").val(data.description);
        }
        if(data.image != null) {
            $('#editPostImagePreview').show();
            postImageSrc = "/groups/showGroupPostImage?id="+data.postId+"&fileName="+data.image;
            $('#editPostImage').attr('src', postImageSrc);
            document.getElementById("editPostImgFileName").innerHTML = "<strong>File name: </strong>" +data.image;
        }
        if(data.fileName != null) {
            $('#editPostFilePreview').show();
            document.getElementById("editPostFileName").innerHTML = "<p><strong>File name: </strong> "+data.fileName+"</p>";
        }
    }

    function showExitGroupModal(){
        $("#groupUserExitModal").modal("show");
        $("#exitFromGroup").attr('onclick', 'javascript:exitFromGroup();');
    }

    function exitFromGroup(){
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="groups" action="userExitGroup" params="'groupId='+groupId" onSuccess="userExitedFromGroup(data)" />
    }

    function userExitedFromGroup(data){
        if (data.status =="OK"){
            window.location.href = "/groups/index";
        }
    }

    function showAdminSettings() {
        $("#adminoptions").slideToggle("show");
        $(".settings-btn").toggleClass("rotate");
    }

    function showComments(id){
        $("#postList #comments"+id).slideToggle("show");
        $("#postList #commentText"+id).focus();
    }
    function showPinnedComments(id){
        if($('.posts-card').hasClass('slick-current')) {
            $("#post-slider .slick-current #comments" + id).slideToggle("show");
            $("#post-slider .slick-current #commentText" + id).focus();
        }
    }

    function showReply(id){
        $("#post-slider #replies"+id).slideToggle("show");
        $("#postList #replies"+id).slideToggle("show");
    }

    $('#upload-file').change(function() {
        var selectedFile = this.files[0];


        //here I CHECK if the FILE SIZE is bigger than 8 MB
        if (selectedFile.size > 8388608 || selectedFile.fileSize > 8388608) {
            $("#err").show()
            $(".postBtn").attr("disabled", 'disabled')
        }else {
            $(".postBtn").removeAttr("disabled")
            $("#err").hide()
        }

        if(!selectedFile.type.match(/image\/.*|application\/pdf/)) {
            validFileFormat = false;
            document.getElementById("errorUploadFile").innerHTML = "Please select images/pdf only.";
            $("#errorUploadFile").removeClass('hidden');
            removeSelectedPostImg();
            removeSelectedPostFile();
            $('#errorPostEditor').addClass('hidden');
        } else if(selectedFile.name.search(/[ <>{}#/\\?:!&$^*%`|+"]/g)>-1) {
            validFileFormat = false;
            document.getElementById("errorUploadFile").innerHTML = "File name must not contain any space and special characters.";
            $('#errorUploadFile').css('display','block');
            $("#errorUploadFile").removeClass('hidden');
            removeSelectedPostImg();
            removeSelectedPostFile();
            $('#errorPostEditor').addClass('hidden');
        } else {
            validFileFormat = true;
        }
        if(selectedFile.type.match(/image\/.*/)  && validFileFormat) {
            $("#errorUploadFile, #errorPostEditor").addClass('hidden');
            fileType = "image";
            $('#image-preview').show();
            var reader = new FileReader();
            reader.onload = function(e) {
                $('#post-image-preview').attr('src', e.target.result);
            }
            reader.readAsDataURL(selectedFile); // convert to base64 string
            document.getElementById("imgFileName").innerHTML = "<strong>File name: </strong>" +selectedFile.name;

            $('#file-preview').hide();
            $('#fileNamePreview').text('');
        } else if (selectedFile.type.match(/application\/pdf/)  && validFileFormat) {
            $("#errorUploadFile, #errorPostEditor").addClass('hidden');
            fileType = "file";
            $('#file-preview').show();
            document.getElementById("fileNamePreview").innerHTML = "<p id='sad'><strong>File name: </strong> "+selectedFile.name+" <a href='javascript:removeSelectedPostFile();'>Remove</a></p>";

            $('#post-image-preview').attr('src', '');
            $('#image-preview').hide();
            $('#imgFileName').text('');
        }
    });

    function removeSelectedPostImg(){
        $('#post-image-preview').attr('src', '');
        $('#image-preview').hide();
        $('#imgFileName').text('');
        $('#upload-file').val('');
        $("#err").hide();
        $(".postBtn").removeAttr("disabled");
    }

    function removeSelectedPostFile() {
        $('#file-preview').hide();
        $('#fileNamePreview').text('');
        $('#upload-file').val('');
        $("#err").hide();
        $(".postBtn").removeAttr("disabled");
    }

    $('#upload-file-1').change(function() {
        var selectedFile = this.files[0];

        //here I CHECK if the FILE SIZE is bigger than 8 MB (numbers below are in bytes)
        if (selectedFile.size > 8388608 || selectedFile.fileSize > 8388608) {
            $("#errEd").show()
            // validFileSize = false
            $("#editPostBtn").attr("disabled", 'disabled')
        }else {
            $("#editPostBtn").removeAttr("disabled")
            $("#errEd").hide()
        }

        if(!selectedFile.type.match(/image\/.*|application\/pdf/)) {
            validFileFormat = false;
            document.getElementById("errorEditUploadFile").innerHTML = "Please select images/pdf only.";
            $("#errorEditUploadFile").removeClass('hidden');
            removeSelectedPostImg();
            removeEditPostFile();
            $('#errorEditPostEditor').addClass('hidden');
        } else if(selectedFile.name.search(/[ <>{}#/\\?:!&$^*%`|+"]/g)>-1) {
            validFileFormat = false;
            document.getElementById("errorEditUploadFile").innerHTML = "File name must not contain any space and special characters.";
            $("#errorEditUploadFile").removeClass('hidden');
            removeSelectedPostImg();
            removeEditPostFile();
            $('#errorEditPostEditor').addClass('hidden');
        } else {
            validFileFormat = true;
        }
        if(selectedFile.type.match(/image\/.*/) && validFileFormat) {
            $("#errorEditUploadFile, #errorEditPostEditor").addClass('hidden');
            fileType = "image";
            $('#editPostImagePreview').show();
            var reader = new FileReader();
            reader.onload = function(e) {
                $('#editPostImage').attr('src', e.target.result);
            }
            reader.readAsDataURL(selectedFile); // convert to base64 string
            document.getElementById("editPostImgFileName").innerHTML = "<strong>File name: </strong>" +selectedFile.name;


            $('#editPostFilePreview').hide();
            $('#editPostFileName').text('');
        } else if (selectedFile.type.match(/application\/pdf/) && validFileFormat) {
            $("#errorEditUploadFile, #errorEditPostEditor").addClass('hidden');
            fileType = "file";
            $('#editPostFilePreview').show();
            document.getElementById("editPostFileName").innerHTML = "<p><strong>File name: </strong> "+selectedFile.name+"</p>";

            $('#editPostImage').attr('src', '');
            $('#editPostImagePreview').hide();
            $('#editPostImgFileName').text('');
        }
    });
    $('#upload-file-2').change(function() {
        var selectedFile = this.files[0];

        //here I CHECK if the FILE SIZE is bigger than 8 MB (numbers below are in bytes)
        if (selectedFile.size > 8388608 || selectedFile.fileSize > 8388608) {
            $("#errEd").show()
            // validFileSize = false
            $("#editPostBtn").attr("disabled", 'disabled')
        }else {
            $("#editPostBtn").removeAttr("disabled")
            $("#errEd").hide()
        }

        if(!selectedFile.type.match(/image\/.*|application\/pdf/)) {
            validFileFormat = false;
            document.getElementById("errorEditUploadFile").innerHTML = "Please select images/pdf only.";
            $("#errorEditUploadFile").removeClass('hidden');
            removeSelectedPostImg();
            removeEditPostFile();
            $('#errorEditPostEditor').addClass('hidden');
        } else if(selectedFile.name.search(/[ <>{}#/\\?:!&$^*%`|+"]/g)>-1) {
            validFileFormat = false;
            document.getElementById("errorEditUploadFile").innerHTML = "File name must not contain any space and special characters.";
            $("#errorEditUploadFile").removeClass('hidden');
            removeSelectedPostImg();
            removeEditPostFile();
            $('#errorEditPostEditor').addClass('hidden');
        } else {
            validFileFormat = true;
        }
        if(selectedFile.type.match(/image\/.*/) && validFileFormat) {
            $("#errorEditUploadFile, #errorEditPostEditor").addClass('hidden');
            fileType = "image";
            $('#editPostImagePreview').show();
            var reader = new FileReader();
            reader.onload = function(e) {
                $('#editPostImage').attr('src', e.target.result);
            }
            reader.readAsDataURL(selectedFile); // convert to base64 string
            document.getElementById("editPostFileName").innerHTML = "<strong>File name: </strong>" +selectedFile.name;

            $('#editPostFilePreview').hide();
            $('#editPostFileName').text('');
        } else if (selectedFile.type.match(/application\/pdf/) && validFileFormat) {
            $("#errorEditUploadFile, #errorEditPostEditor").addClass('hidden');
            fileType = "file";
            $('#editPostFilePreview').show();
            document.getElementById("editPostFileName").innerHTML = "<p><strong>File name: </strong> "+selectedFile.name+"</p>";

            $('#editPostImage').attr('src', '');
            $('#editPostImagePreview').hide();
            $('#editPostImgFileName').text('');
        }
    });

    function removeEditPostImg() {
        $('#editPostImage').attr('src', '');
        $('#editPostImagePreview').hide();
        $('#editPostImgFileName').text('');
        $('#upload-file-1').val('');
        $('#upload-file-2').val('');

    }

    function removeEditPostFile() {
        $('#editPostFilePreview').hide();
        $('#editPostFileName').text('');
        $('#upload-file-1').val('');
        $('#upload-file-2').val('');

    }

    function removePostImg(){
        $('#post-image-preview').attr('src','')
        $('#image-preview').hide()
    }

    function removePostFile(){
        document.getElementById("upload-file").value = "";
        $('#file-preview').hide();

        // $('#fileNamePreview').hide()
        // document.getElementById('sad').innerHTML = '';
    }

    function createPost() {

        var postContent = $("#postEditor").val().trim();
        postContent= postContent.replace(/([\u2700-\u27BF]|[\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2011-\u26FF]|\uD83E[\uDD10-\uDDFF])/g, '');


        dataObj = {
            groupId: groupId,
            description: postContent,
            siteId: "${session.siteId}"
        };

        if(postContent == "" || postContent == "null" || postContent == null || removingBlankSpaces(postContent)) {
            document.getElementById("errorPostEditor").innerHTML = "Please enter post content.";
            $("#errorPostEditor").show();
            postAttachmentWOText();
        }else if (postContent.length > 500){
            document.getElementById("errorPostEditor").innerHTML = "Please limit the post within 500 characters";
            $("#errorPostEditor").show();
        }
        else {
            $("#errorUploadFile, #errorPostEditor").hide();
            $('.loading-icon').removeClass('hidden');
            $.ajax({
                url: "/groups/createPostDescriptionForGroup",
                type: 'POST',
                contentType: "application/json",
                dataType:"json",
                data: JSON.stringify(dataObj),
                success: function (data) {
                    if (data.status == "success") {
                        // createCKEditor.setData('');
                        postAttachment(data.postId);
                        setTimeout(function (){
                            $('.loading-icon').addClass('hidden');
                        },5000)
                        localStorage.clear();
                        $("#postEditor").val('');
                        navigator.clipboard.writeText("");
                    }
                }
            });
        }
    }

    function postAttachment(id) {
        postId = id;
        var formData = new FormData();
        var files = $("#upload-file")[0].files;

        if(files.length > 0) {
            formData.append('file',files[0]);
        }
        formData.append('groupId', groupId);
        formData.append('type', fileType);
        formData.append('postId',postId);
        formData.append('siteId',"${session.siteId}")
        if(files.length > 0) {
            if(validFileFormat) {
                $("#errorUploadFile, #errorPostEditor").addClass('hidden');
                $('.loading-icon').removeClass('hidden');
                $.ajax({
                    url: "/groups/createPostImgFileForGroup",
                    type: 'POST',
                    cache: false,
                    contentType: false,
                    processData: false,
                    data: formData,
                    success: function (data) {
                        if (data.status == "success") {
                            removePostImg();
                            removePostFile();
                            showPosts();
                        }
                    }
                });
            }
        }
        else  showPosts();
    }

    function postAttachmentWOtextwithnofiles(postId){
        var formData = new FormData();
        var files = $("#upload-file-2")[0].files;
        if(files.length > 0) {
            formData.append('file',files[0]);
        }
        formData.append('groupId', groupId);
        formData.append('type', fileType);
        formData.append('postId',postId)
        formData.append('fileEmpty',fileEmpty)
        if(validFileFormat ) {
            $("#errorUploadFile, #errorPostEditor").addClass('hidden');
            $('.loading-icon').removeClass('hidden');
            $.ajax({
                url: "/groups/createPostImgFileForGroup",
                type: 'POST',
                cache: false,
                contentType: false,
                processData: false,
                data: formData,
                success: function (data) {
                    if (data.status == "success") {
                        $('.loading-icon').addClass('hidden');
                        location.reload()
                    }
                }
            });
        }

    }

    function postAttachmentWOText(){
        var formData = new FormData();
        var files = $("#upload-file")[0].files;
        if(files.length > 0) {
            formData.append('file',files[0]);
        }
        formData.append('groupId', groupId);
        formData.append('type', fileType);
        if(files.length > 0) {
            if(validFileFormat ) {
                $("#errorUploadFile, #errorPostEditor").addClass('hidden');
                $('.loading-icon').removeClass('hidden');
                $.ajax({
                    url: "/groups/createPostImgFileForGroup",
                    type: 'POST',
                    cache: false,
                    contentType: false,
                    processData: false,
                    data: formData,
                    success: function (data) {
                        if (data.status == "success") {
                            $('.loading-icon').addClass('hidden');
                            location.reload()
                        }
                    }
                });
            }
        }
    }

    function updatePost(postId) {

        var postContent = $("#editPostEditor").val();
        postContent= postContent.replace(/([\u2700-\u27BF]|[\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2011-\u26FF]|\uD83E[\uDD10-\uDDFF])/g, '');

        dataObj = {
            groupId: groupId,
            description: postContent,
            postId: postId,
        };

        if((postContent == "" || postContent ==null || postContent =="null" || removingBlankSpaces(postContent)) && ($("#upload-file-2")[0].files.length==0) && !($('#editPostFileName p').css('display')=='none') && document.getElementById('editPostImage').getAttribute('src') == "")  {
            document.getElementById("errorEditPostEditor").innerHTML = "Please change post content.";
            $("#errorEditPostEditor").removeClass('hidden');
        } else {
            var files = $("#upload-file-2")[0].files;
            $("#errorEditUploadFile, #errorEditPostEditor").addClass('hidden');
            $('.loading-icon').removeClass('hidden');
            $.ajax({
                url: "/groups/createPostDescriptionForGroup",
                type: 'POST',
                contentType: "application/json",
                dataType:"json",
                data: JSON.stringify(dataObj),
                success: function (data) {
                    if (data.status == "success") {
                        if(files.length > 0 || fileEmpty==true) postAttachmentWOtextwithnofiles(postId)
                        setTimeout(function (){
                            $('.loading-icon').addClass('hidden');
                            location.reload();
                        },4000)
                        fileEmpty=false;

                    }
                }
            });
        }

    }

    function reportUser(user) {
        $(".loading-icon").removeClass("hidden");
        description = "test";
        username = user;
        dataObj = {
            groupId: groupId,
            description: description,
            username: username
        };
        $.ajax({
            url: "/groups/reportGroupUser",
            type: "POST",
            contentType: "application/json",
            dataType:"json",
            data: JSON.stringify(dataObj),
            success: function (data) {
                $('#groupUserReportModal').modal('hide');
                $(".loading-icon").addClass("hidden");
            },
        });
    }

    function reportPost(id) {
        $(".loading-icon").removeClass("hidden");
        description = $("#reportPostReason"+id).val();
        description= description.replace(/([\u2700-\u27BF]|[\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2011-\u26FF]|\uD83E[\uDD10-\uDDFF])/g, '');
        dataObj = {
            groupId: groupId,
            postId: id,
            description: description,
        };
        $.ajax({
            url: "/groups/reportPostAsSpam",
            type: "POST",
            contentType: "application/json",
            dataType:"json",
            data: JSON.stringify(dataObj),
            success: function (data) {
                document.getElementById("reportPostReasonMsg").innerHTML = "<p class='text-success'>Post reported!</p>";
                $("#reportPostReason"+data.postId).val('');
                $(".loading-icon").addClass("hidden");
                $("#reportPostReasonMsg").show();
            },
        });
    }

    function removePostFromGroup(id) {
        var siteId="${session.siteId}"
        $(".loading-icon").removeClass("hidden");
        <g:remoteFunction controller="groups" action="deletePostFromGroup" params="'groupId='+groupId+'&postId='+id+'&siteId='+siteId" onSuccess="postDeleted(data)"/>
    }

    function postDeleted(data) {
        $(".loading-icon").addClass("hidden");
        if(data.status == "OK") {
            $("#successMsg").addClass("show").removeClass("hide");
            document.getElementById("successMsg").innerHTML = '<p>Post deleted!</p>';
            setTimeout(function(){ $("#successMsg").removeClass("show").addClass("hide"); }, 5000);
            $("#groupPostDeleteModal").modal('hide');
            showPosts();
        }
    }

    function likePost(id,likedPost) {
        $(".loading-icon").addClass("hidden");
        postId = id;
        if(!likedPost) {
            <g:remoteFunction controller="groups" action="likePostOfGroup" params="'groupId='+groupId+'&postId='+postId" onSuccess="postLikedOrDisliked(data)"/>
        } else if(likedPost) {
            <g:remoteFunction controller="groups" action="dislikePostOfGroup" params="'groupId='+groupId+'&postId='+postId" onSuccess="postLikedOrDisliked(data)" />

        }
    }

    function postLikedOrDisliked(data){
        $(".loading-icon").addClass("hidden");
        showPosts();
    }

    var commentPostId;

    function sendComment(id) {
        dict[id]=0;

        description = $("#commentText"+id).val().trim();
        description = description.replace(/([\u2700-\u27BF]|[\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2011-\u26FF]|\uD83E[\uDD10-\uDDFF])/g, '');
        if(description == ""){
            description = $("#postList #commentText"+id).val().trim();
            description = description.replace(/([\u2700-\u27BF]|[\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2011-\u26FF]|\uD83E[\uDD10-\uDDFF])/g, '');
             var fromPostList = true;
        }
        dataObj = {
            groupId: groupId,
            postId: id,
            description: description,
        };
        if(description == "" || description == "null" || description == null) {
            $("#errorComment"+id).show();
        } else {
            $.ajax({
                url: "/groups/commentPostOfGroup",
                type: "POST",
                contentType: "application/json",
                dataType:"json",
                data: JSON.stringify(dataObj),
                success: function (data) {
                    if(data.status == "OK") {
                        $("#commentText"+id).val("");
                        getLatestComments(id);
                        commentPostId=id;
                        if (fromPostList == true){
                            $("#postList #inside-comments"+id).scrollTop($("#postList #inside-comments"+id)[0].scrollHeight);
                        }
                        else $("#post-slider #inside-comments"+id).scrollTop($("#post-slider #inside-comments"+id)[0].scrollHeight);

                    }
                },
            });
        }
    }

    function replyComment(commentId,id) {

        description = $("#commentText"+id).val().trim();
        description = description.replace(/([\u2700-\u27BF]|[\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2011-\u26FF]|\uD83E[\uDD10-\uDDFF])/g, '');
        if(description == ""){
            description = $("#postList #commentText"+id).val().trim();
            description = description.replace(/([\u2700-\u27BF]|[\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2011-\u26FF]|\uD83E[\uDD10-\uDDFF])/g, '');
            var fromPostList = true;
        }
        dataObj = {
            groupId: groupId,
            postId: id,
            commentId: commentId,
            description: description,
        };
        if(description == "" || description == "null" || description == null) {
            document.getElementById("errorMsg").innerHTML = '<p>Please enter comment.</p>';
            $("#errorMsg").addClass("show").removeClass("hide");
            setTimeout(function(){ $("#errorMsg").removeClass("show").addClass("hide"); }, 5000);
        } else {
            $.ajax({
                url: "/groups/replyForCommentOfGroup",
                type: "POST",
                contentType: "application/json",
                dataType:"json",
                data: JSON.stringify(dataObj),
                success: function (data) {
                    if(data.status == "OK") {
                        getLatestComments(id);
                        commentPostId=id;
                        $("#commentText"+id).val("");
                        cancelReply(commentId,id);

                        if (fromPostList == true){
                            $("#postList #inside-comments"+id).scrollTop($("#postList #inside-comments"+id)[0].scrollHeight);
                        }
                        else $("#post-slider #inside-comments"+id).scrollTop($("#post-slider #inside-comments"+id)[0].scrollHeight);

                    }
                },
            });
        }
    }

    function getLatestComments(id) {
       var commentsPageNo= dict[id];
        <g:remoteFunction controller="groups" action="getCommentListForPost" params="'postId='+id+'&pageNo='+commentsPageNo" onSuccess="showLatestComments(data,id)" />
    }

    function showLatestComments(data,id) {
        var commentsList = data.commentsList;

        $("#totalComments"+commentPostId).text(commentsList.length);
        $(' #comments'+commentPostId).html(commentsUI(commentPostId,commentsList));
    }

    function showPosts(showMore){

        $('.slider-inner').slick('unslick'); /* ONLY remove the classes and handlers added on initialize */
        $('.slider-inner').remove(); /* Remove current slides elements, in case that you want to show new slides. */
        <g:remoteFunction controller="groups" action="getGroupPostsDetails" params="'groupId='+groupId+'&pageNo='+pageNo" onSuccess="showPostLists(data,showMore)" />
    }

    var pinnedcomments;
    function showPostLists(data,showMore){

        pinnedcomments = data;
        showPinnedPosts(pinnedcomments);

        currentUsername = currentUsername.replace("&#64;","@")

        var postDetails = data.groupPostDetails;

        var htmlStr ='';
        if(showMore=='no')
            htmlStr = document.getElementById("postList").innerHTML;
        if(postDetails.length==0&&pageNo>0){
            $("#showMore").hide();
        } else if(postDetails.length==0&&pageNo==0){
            document.getElementById("postList").innerHTML = "<div class='searchGif'><img src='${assetPath(src:'groups/search.gif')}' /></div> <p class='p-4 text-center'>No posts added yet!</p>";
            $("#showMore").hide();
        } else {
            for (var i=0;i<postDetails.length;i++){
                dict[postDetails[i].id] = 0;
                var comments = postDetails[i].comments;
                var pinnedpost = postDetails[i].pinned;
                var commentsCount = jQuery.parseJSON(postDetails[i].commentsCount);
                username = postDetails[i].createdBy;
                postCount = numberFormatter(postDetails[i].postLikesCount)

                postedDate  = postDetails[i].dateCreated;
                currentDate = moment(postedDate).utc().format('YYYY-MM-DD');
                checkPostedDate = moment().isSame(currentDate, 'day');
                if(checkPostedDate) {
                    postedDate = moment(postedDate).fromNow();
                } else {
                    postedDate = moment(postedDate).format('lll');
                }

                htmlStr = htmlStr + "<div class=\"posts-card p-4 mt-3\">\n"
                if(postDetails[i].userType == 'admin' )
                {
                    htmlStr +="<div class='admin-header' style='text-align: right;'><p class=\"adminclass ml-2  \">"+postDetails[i].userType+"</p></div>"
                }
                htmlStr += "<div class=\"d-flex align-items-center justify-content-between\">\n" +
                    "<div class=\"user-info d-flex align-items-center\">\n";

                if(postDetails[i].profilepic != null) {
                    // imageSrc = "/funlearn/showProfileImage?id="+postDetails[i].userId+"&fileName="+postDetails[i].profilepic+"&type=user&imgType=passport";
                    imageSrc = "/funlearn/showProfileImage?id="+postDetails[i].userId+"&fileName="+postDetails[i].profilepic+"&type=user&imgType=passport";
                } else {
                    imageSrc = "${assetPath(src: 'landingpageImages/img_avatar3.png')}";
                }
                htmlStr += "<img src='"+imageSrc+"' class=\"user-info-img mr-3\">";

                if (postDetails[i].name == null){
                    htmlStr +="<p class=\"mr-2 text-capitalize\">Unknown user</p>";
                }
                else if(postDetails[i].userType == 'admin' && groupType=="channel" && batchId==null && instituteId==null ){
                    htmlStr +="<p class=\"mr-2 text-capitalize\">Admin</p>";
                }
                else {
                    htmlStr +="<p class=\"mr-2 text-capitalize\">"+postDetails[i].name+"</p>";
                }

                htmlStr +=   "<p class='time'>"+postedDate+"</p>";
                if(pinnedpost == 'true')
                {
                    htmlStr +=  "<img src=\"${assetPath(src: 'groups/pinpost.svg')}\" class=\"user-pinnedPost-img mr-3\">";
                }

                htmlStr +="</div>";
                if(userType != 'super_admin') {
                    htmlStr += " <div class=\"btn-group dropleft\">\n"
                    if (groupType != "channel") {
                        htmlStr += "<button type=\"button\" class=\"btn dropdown-toggle p-0\" data-toggle=\"dropdown\">\n" +
                            "<img src=\"${assetPath(src: 'groups/mdi_more_vert.svg')}\">\n" +
                            "</button>";
                    }
                    if (groupType == "channel" && userType == "admin") {
                        htmlStr += "<button type=\"button\" class=\"btn dropdown-toggle p-0\" data-toggle=\"dropdown\">\n" +
                            "<img src=\"${assetPath(src: 'groups/mdi_more_vert.svg')}\">\n" +
                            "</button>";
                    }
                    htmlStr += "<div class=\"dropdown-menu\">";


                    if (currentUsername != username && !admin) {
                        htmlStr += "<ul class=\"px-3 mb-0 d-flex flex-column justify-content-center\">\n" +
                            "<li>\n" +
                            "<a href=\"javascript:showPostReportModal(" + postDetails[i].id + ")\">\n" +
                            " <img src=\"${assetPath(src: 'groups/flag.svg')}\" class=\"mr-2\">Report post\n" +
                            "</a>\n" +
                            "</li>";
                    }
                    if (admin || currentUsername == username) {
                        htmlStr += "<li>\n" +
                            "<a href=\"javascript:deletePostModal(" + postDetails[i].id + ")\" class=\"deletePostBtn\">\n" +
                            "<img src=\"${assetPath(src: 'groups/trash.svg')}\" class=\"mr-2\">Delete post\n" +
                            "</a>\n" +
                            "</li>";
                    }
                    if(admin ) {
                        if(pinnedpost == "true"){
                            htmlStr += "<li>\n" +
                                "<a href=\"javascript:pinPost(" + postDetails[i].id + ","+ postDetails[i].pinned+")\" class=\"unpinPostBtn\">\n" +
                                "<img src=\"${assetPath(src: 'groups/pin.svg')}\" class=\"mr-2\">Unpin post\n" +
                                "</a>\n" +
                                "</li>";
                        }
                        else {
                            htmlStr += "<li>\n" +
                                "<a href=\"javascript:pinPost(" + postDetails[i].id + ","+ postDetails[i].pinned+")\" class=\"pinPostBtn\">\n" +
                                "<img src=\"${assetPath(src: 'groups/pin.svg')}\" class=\"mr-2\">Pin post\n" +
                                "</a>\n" +
                                "</li>";
                        }

                    }
                    if ((currentUsername == username || (currentUsername == username && admin)) && userType != "super_admin") {
                        htmlStr += "<li>\n" +
                            "<a href=\"javascript:editPostModal(" + postDetails[i].id + ")\">\n" +
                            "<img src=\"${assetPath(src: 'groups/edit-post.svg')}\" class=\"mr-2\">Edit post\n" +
                            "</a>\n" +
                            "</li>";
                    }
                    if (currentUsername != username && !admin && postDetails[i].userType != 'admin') {
                        var userName = postDetails[i].createdBy;
                        htmlStr += "<li>\n" +
                            "<a href=\"javascript:showReportUserModal('" + userName + "');\">\n" +
                            "<img src=\"${assetPath(src: 'groups/report-user.svg')}\" class=\"mr-2\">Report user\n" +
                            "</a>\n" +
                            "</li>";
                    }
                    htmlStr += "</ul></div></div>" ;
                }
                htmlStr += "</div>";
                htmlStr += "<div class='mb-3 mt-3' style='word-break: break-word'>\n";
                if(postDetails[i].description != null) {

                    var description = createTextLinks_(postDetails[i].description);
                    htmlStr += "<p class=\"post-description\" >" + description + "</p>\n";


                }

                if(postDetails[i].postImage != null) {
                    postImageSrc = "/groups/showGroupPostImage?id="+postDetails[i].id+"&fileName="+postDetails[i].postImage;
                    htmlStr += "<img  src='"+postImageSrc+"' class=\"post-img mb-3 mt-3\">\n";
                }

                if(postDetails[i].fileName != null) {
                    postFileSrc = "/groups/downloadPostFile?id="+postDetails[i].id;
                    htmlStr += "<a href='"+postFileSrc+"'>"+postDetails[i].fileName+" (Click to download file)</a>\n";
                }

                htmlStr += "</div>";


                htmlStr += "<div class=\"d-flex justify-content-between\">\n";
                htmlStr +="<div class=\"postOptions d-flex align-items-center\">\n";

                if(postCount>0){
                    htmlStr +="<span style=\"font-size: 10px;color:#2F80ED;font-weight: 600;\">"+postCount+"</span>\n";
                }
               if(groupType=="channel" && (batchId!=null || instituteId==null)){
                   //don't show like
               }else {
                   htmlStr += "<button class='d-flex align-items-center' onclick=\"javascript:likePost(" + postDetails[i].id + "," + postDetails[i].likedPost + ");\" id=\"likingPost\">\n";
                   if (postDetails[i].likedPost == "true") {
                       htmlStr += "<img src=\"${assetPath(src: 'groups/like.svg')}\" id=\"likeImg\">" +
                           "<span style='font-size: 11px;color:#2F80ED;margin-left: 5px;'>Liked!</span>";
                   } else {
                       htmlStr += "<img src=\"${assetPath(src: 'groups/unlike.svg')}\" id=\"likeImg\">" +
                           "<span style='font-size: 11px;color:rgba(68, 68, 68, 0.85);margin-left: 5px;'>Like</span>";
                   }
                   htmlStr += "</button>";
               }
                htmlStr += "</div>";
                htmlStr += "<div class=\"comment-bttn\">";
                htmlStr += "<button onclick=\"javascript:showComments(" + postDetails[i].id + ")\">\n";

                htmlStr +="<img src=\"${assetPath(src: 'groups/comment.svg')}\">\n"+
                    " </button>";
                htmlStr += "</div></div>";

                htmlStr += "<div id='comments"+postDetails[i].id+"'>"+commentsUI(postDetails[i].id,comments)+"</div></div>";

            }

            document.getElementById("postList").innerHTML = htmlStr;


            if(postDetails.length>=10){
                $("#showMore").show();
            }
            var anchors = document.querySelectorAll('.post-description a');
            for (var i=0; i<anchors.length; i++){
                anchors[i].setAttribute('target', '_blank');
            }
        }
        hideAdmin();
        geturls();
    }

    function showPinnedPosts(data,showMore) {

        currentUsername = currentUsername.replace("&#64;", "@")

        var postDetails = data.groupPinnedPosts;
        var countPinned=0;
        var htmlStr = '';
        for (var i = 0; i < postDetails.length; i++) {
            var comments = postDetails[i].comments;
            var pinnedpost = postDetails[i].pinned;
            var postId = postDetails[i].id;
            if (pinnedpost == "true") {

                pinned = true;
                countPinned+=1;
            } else {
                pinned = false;
            }
            if (pinned == true) {
                var commentsCount = jQuery.parseJSON(postDetails[i].commentsCount);
                username = postDetails[i].createdBy;
                postCount = numberFormatter(postDetails[i].postLikesCount)

                postedDate = postDetails[i].dateCreated;
                currentDate = moment(postedDate).utc().format('YYYY-MM-DD');
                checkPostedDate = moment().isSame(currentDate, 'day');
                if (checkPostedDate) {
                    postedDate = moment(postedDate).fromNow();
                } else {
                    postedDate = moment(postedDate).format('lll');
                }

                htmlStr = htmlStr + "<div class=\"posts-card p-4 mt-3\" >\n"
                if (postDetails[i].userType == 'admin' ) {
                    htmlStr += "<div class='admin-header' onclick='postDetailsNewPage("+postId+")' style='text-align: right;'><p class=\"adminclass ml-2  \">" + postDetails[i].userType + "</p></div>"
                }
                htmlStr += "<div class=\"d-flex align-items-center justify-content-between\">\n" +
                    "<div class=\"user-info d-flex align-items-center\" onclick='postDetailsNewPage("+postId+")'>\n";

                if (postDetails[i].profilepic != null) {
                    // imageSrc = "/funlearn/showProfileImage?id="+postDetails[i].userId+"&fileName="+postDetails[i].profilepic+"&type=user&imgType=passport";
                    imageSrc = "/funlearn/showProfileImage?id=" + postDetails[i].userId + "&fileName=" + postDetails[i].profilepic + "&type=user&imgType=passport";
                } else {
                    imageSrc = "${assetPath(src: 'landingpageImages/img_avatar3.png')}";
                }
                htmlStr += "<img src='" + imageSrc + "' class=\"user-info-img mr-3\">";

                if (postDetails[i].name == null) {
                    htmlStr += "<p class=\"mr-2 text-capitalize\">Unknown user</p>";
                }
                else if(postDetails[i].userType == 'admin' && groupType=="channel" ){
                    htmlStr +="<p class=\"mr-2 text-capitalize\">Admin</p>";
                }else {
                    htmlStr += "<p class=\"mr-2 text-capitalize\">" + postDetails[i].name + "</p>";
                }

                htmlStr += "<p class='time'>" + postedDate + "</p>";

                htmlStr += "</div>";
                if(userType != 'super_admin') {
                htmlStr += " <div class=\"btn-group dropleft\">\n"
                if (groupType != "channel") {
                    htmlStr += "<button type=\"button\" class=\"btn dropdown-toggle p-0\" data-toggle=\"dropdown\">\n" +
                        "<img src=\"${assetPath(src: 'groups/mdi_more_vert.svg')}\">\n" +
                        "</button>";
                }
                if (groupType == "channel" && userType == "admin") {
                    htmlStr += "<button type=\"button\" class=\"btn dropdown-toggle p-0\" data-toggle=\"dropdown\">\n" +
                        "<img src=\"${assetPath(src: 'groups/mdi_more_vert.svg')}\">\n" +
                        "</button>";
                }
                htmlStr += "<div class=\"dropdown-menu\">";


                if (currentUsername != username && !admin) {
                    htmlStr += "<ul class=\"px-3 mb-0 d-flex flex-column justify-content-center\">\n" +
                        "<li>\n" +
                        "<a href=\"javascript:showPostReportModal(" + postDetails[i].id + ")\">\n" +
                        " <img src=\"${assetPath(src: 'groups/flag.svg')}\" class=\"mr-2\">Report post\n" +
                        "</a>\n" +
                        "</li>";
                }
                if (admin || currentUsername == username) {
                    htmlStr += "<li>\n" +
                        "<a href=\"javascript:deletePostModal(" + postDetails[i].id + ")\" class=\"deletePostBtn\">\n" +
                        "<img src=\"${assetPath(src: 'groups/trash.svg')}\" class=\"mr-2\">Delete post\n" +
                        "</a>\n" +
                        "</li>";
                }
                if(admin ) {
                    if(pinnedpost == "true"){
                        htmlStr += "<li>\n" +
                            "<a href=\"javascript:pinPost(" + postDetails[i].id + ","+ postDetails[i].pinned+")\" class=\"unpinPostBtn\">\n" +
                            "<img src=\"${assetPath(src: 'groups/pin.svg')}\" class=\"mr-2\">Unpin post\n" +
                            "</a>\n" +
                            "</li>";
                    }
                    else {
                        htmlStr += "<li>\n" +
                            "<a href=\"javascript:pinPost(" + postDetails[i].id + ","+ postDetails[i].pinned+")\" class=\"pinPostBtn\">\n" +
                            "<img src=\"${assetPath(src: 'groups/pin.svg')}\" class=\"mr-2\">Pin post\n" +
                            "</a>\n" +
                            "</li>";
                    }

                }
                if ((currentUsername == username || (currentUsername == username && admin)) && userType != "super_admin") {
                    htmlStr += "<li>\n" +
                        "<a href=\"javascript:editPostModal(" + postDetails[i].id + ")\">\n" +
                        "<img src=\"${assetPath(src: 'groups/edit-post.svg')}\" class=\"mr-2\">Edit post\n" +
                        "</a>\n" +
                        "</li>";
                }
                if (currentUsername != username && !admin && postDetails[i].userType != 'admin') {
                    var userName = postDetails[i].createdBy;
                    htmlStr += "<li>\n" +
                        "<a href=\"javascript:showReportUserModal('" + userName + "');\">\n" +
                        "<img src=\"${assetPath(src: 'groups/report-user.svg')}\" class=\"mr-2\">Report user\n" +
                        "</a>\n" +
                        "</li>";
                }
                    htmlStr += "</ul></div></div>" ;
                }
                htmlStr += "</div>";
                htmlStr += "<div class='mb-3 mt-3 descreptionBlock' onclick='postDetailsNewPage("+postId+")' style='word-break: break-word; max-height:100px; min-height:100px;'>\n";
                if (postDetails[i].description != null ) {
                     if(postDetails[i].description.length>100) {
                         var description = createTextLinks_(postDetails[i].description).slice(0, 49);
                         htmlStr += "<p class=\"post-description\" onclick='postDetailsNewPage("+postId+")'>" + description + "<a href=\"#\" > Show more..</a></p>\n";
                     }
                     else{
                         var description = createTextLinks_(postDetails[i].description);
                         htmlStr += "<p class=\"post-description\" onclick='postDetailsNewPage("+postId+")'>" + description + "</p>\n";
                     }
                }

                if (postDetails[i].postImage != null) {
                    postImageSrc = "/groups/showGroupPostImage?id=" + postDetails[i].id + "&fileName=" + postDetails[i].postImage;
                    htmlStr += "<img  src='" + postImageSrc + "' class=\"post-img mb-3 mt-3\" style='max-height:50px;width:100%;height:auto;object-fit:cover;' onclick='postDetailsNewPage("+postId+")'>\n";
                }

                if (postDetails[i].fileName != null) {
                    postFileSrc = "/groups/downloadPostFile?id=" + postDetails[i].id;
                    htmlStr += "<a href='" + postFileSrc + "'>" + postDetails[i].fileName + " (Click to download file)</a>\n";
                }

                htmlStr += "</div>";


                htmlStr += "<div class=\"d-flex justify-content-between sliderlike\">\n";
                htmlStr += "<div class=\"postOptions d-flex align-items-center\">\n";

                if (postCount > 0) {
                    htmlStr += "<span style=\"font-size: 10px;color:rgba(68, 68, 68, 0.85);font-weight: 600;\">" + postCount + "</span>\n";
                }

                htmlStr += "<button class='d-flex align-items-center' onclick=\"javascript:likePost(" + postDetails[i].id + "," + postDetails[i].likedPost + ");\" id=\"likingPost\">\n";
                if (postDetails[i].likedPost == "true") {
                    htmlStr += "<img src=\"${assetPath(src: 'groups/like.svg')}\" id=\"likeImg\">" +
                        "<span style='font-size: 11px;color:rgba(68, 68, 68, 0.85);margin-left: 5px;'>Liked</span>";
                } else {
                    htmlStr += "<img src=\"${assetPath(src: 'groups/unlike.svg')}\" id=\"likeImg\">" +
                        "<span style='font-size: 11px;color:rgba(68, 68, 68, 0.85);margin-left: 5px;'>Like</span>";
                }
                htmlStr += "</button>";
                htmlStr += "</div>";
                htmlStr += "<div class=\"comment-bttn\">";

                htmlStr += "<button onclick='postDetailsNewPage("+postId+")'\">\n";


                htmlStr += "<img src=\"${assetPath(src: 'groups/comment.svg')}\">\n" +
                    " </button>";
                htmlStr += "</div></div>";

                htmlStr += "<div style='display: none;'  class='commentsDiv' id='comments" + postDetails[i].id + "'>" + commentsUI(postDetails[i].id, comments) + "</div></div>";

            }
        }

        $('.slider-parent').append($("<div class='slider-inner' id='post-slider'></div>"));
        document.getElementById("post-slider").innerHTML = htmlStr;
        if($('div#slider-parent .posts-card').length==1){
            document.getElementById("post-slider").innerHTML += htmlStr;

        }


        if(postDetails.length>=10){
            $("#showMore").show();
        }
        var anchors = document.querySelectorAll('.post-description a');
        for (var i=0; i<anchors.length; i++){
            anchors[i].setAttribute('target', '_blank');
        }
        hideAdmin();
        slickInitialize();
        showPinnedElement();
        $('.form-text.error-text.text-danger').css('display','none');
    }



    function showMorePosts() {
        pageNo++;
        showPosts('no');
    }

    function commentsUI(id,comments) {
        var commentsTemplate = "";
        var replyText = "";
        commentsTemplate += "<div class=\"comments-sec mt-2 p-3\"><div id='inside-comments"+id+"' class='inside-comments'>";
        if(comments.length>0) {
            for (var i = 0; i < comments.length; i++) {
                commentedDate = comments[i].dateCreated;
                currentDate = moment(commentedDate).utc().format('YYYY-MM-DD');
                checkCommentedDate = moment().isSame(currentDate, 'day');
                if(checkCommentedDate) {
                    commentedDate = moment(commentedDate).fromNow();
                } else {
                    commentedDate = moment(commentedDate).format('lll');
                }
                commentsTemplate += "<div class=\"comments-info\">" +
                    "<div class=\"user-info d-flex align-items-center\">";
                if(comments[i].profilepic != null) {
                    imageSrc = "/funlearn/showProfileImage?id="+comments[i].userId+"&fileName="+comments[i].profilepic+"&type=user&imgType=passport";
                } else {
                    imageSrc = "${assetPath(src: 'landingpageImages/img_avatar3.png')}";
                }
                commentsTemplate += "<img src="+imageSrc+" class=\"user-info-img mr-2\">";
                if(comments[i].name != null){
                    commentUserName = comments[i].name
                }else{
                    commentUserName = "Unknown user";
                }
                commentsTemplate +=  "<p class=\"mr-2 text-capitalize\" style=\"font-size: 12px;color: #444;\">"+commentUserName+"</p>" +
                    "<p style=\"font-size: 11px;color: rgba(68, 68, 68, 0.48);font-style: italic;\">"+commentedDate+"</p>" +
                    "</div>" +
                    "<div class=\"ml-5\" >" +
                    "<p style=\"font-size: 14px;color: #000;\">"+comments[i].description+"</p>" +
                    "</div>";

                commentsTemplate += "<div class=\"d-flex reply-sec ml-5 mb-2\">";

                var replies = comments[i].repliedDetailsByCommentsId;
                var repliesCount = jQuery.parseJSON(comments[i].repliedCount);
                if(repliesCount>0) {
                    if(repliesCount==1) {
                        replyText = "Reply";
                    } else {
                        replyText = "Replies";
                    }
                    commentsTemplate += "<p><a style='font-size: 11px;font-style: normal;' href='javascript:showReply("+comments[i].id+");'>"+numberFormatter(repliesCount)+" "+replyText+"</a></p>";
                }

                commentsTemplate += "<button onclick=\"javascript:replyTo('"+comments[i].name+"',"+comments[i].id+","+id+");\">Reply</button>"+
                    "</div>";

                commentsTemplate += "<div style=\"display: none;border-left: 1px solid #ddd;\" id=\"replies"+comments[i].id+"\" class=\"ml-5 mt-3 pl-3\">";

                for (var j = 0; j < replies.length; j++) {
                    commentedDate = replies[j].dateCreated;
                    currentDate = moment(commentedDate).utc().format('YYYY-MM-DD');
                    checkCommentedDate = moment().isSame(currentDate, 'day');
                    if(checkCommentedDate) {
                        commentedDate = moment(commentedDate).fromNow();
                    } else {
                        commentedDate = moment(commentedDate).format('lll');
                    }
                    commentsTemplate += "<div class='reply-info pb-3'><div class=\"d-flex align-items-center mb-1\">"+
                        "<p style=\"font-size: 12px;color: #444;\" class=\"mr-2 text-capitalize\">"+(replies[j].name?replies[j].name:'Unknown User')+"</p>"+
                        "<p style=\"font-size: 11px;color: rgba(68, 68, 68, 0.48);font-style: italic;\">"+commentedDate+"</p>"+
                        "</div>"+
                        "<p style=\"font-size: 14px;color: #000;\">"+replies[j].description+"</p></div>";
                }
                commentsTemplate += "</div></div>";
            }
            commentsTemplate += "</div><div class=\"mt-3 comment-input-box\">"+
                "<div id='replyToText"+id+"' style='display:none;'></div>"+
                "<div class=\"input-group\" oninput='javascript:forcejoin()'>"+
                "<input type=\"text\" id=\"commentText"+id+"\" class=\"comment-input col\" maxLength='500'   placeholder=\"Write your comment\">"+
                "<div class=\"input-group-append\" oninput='javascript:forcejoin()'>"+
                "<button class=\"btn btn-success btn-sm d-flex align-items-center\" type=\"button\" id='sendCommentBtn"+id+"' onclick='sendComment("+id+")' style=\"border-radius:0 10px 10px 0;border:0;\">"+
                "<i class=\"material-icons\" style=\"font-size: 18px;\">send</i>"+
                "</button>"+
                "</div>"+
                "</div>"+
                "<p id='errorComment"+id+"' class='form-text error-text text-danger' style='display: none;'>Please enter comment.</p>"+
                "</div>";
            if((comments.length>1)) {
                commentsTemplate+=  "<div class='text-center hideShowMore'>" +
                "<a id='firstNine" + id + "' style='cursor:pointer;padding-top:3%;font-size: 14px;margin-bottom: 5px;color: #007bff;font-weight:600;' onclick='showfirstnine(" + id + ")' >Show more</a>" +
                "<a id='showNext' class='showNext" + id + "' style='cursor:pointer;padding-top:3%;font-size: 14px;margin-bottom: 5px;color: #007bff;font-weight:600;display:none;' onclick='shownextten(" + id + ")'  >Show more</a></div>";
            }
        } else {
            commentsTemplate += "<img src='${assetPath(src: 'groups/speech.gif')}' class='noComt'/>\n"+
                "<p style='font-size: 13px;text-align: center;'>No comments found!</p>";
            commentsTemplate += "</div><div class=\"mt-3 comment-input-box\">"+
                "<div id='replyToText"+id+"' style='display:none;'></div>"+
                "<div class=\"input-group\" oninput='javascript:forcejoin()'>"+
                "<input type=\"text\" id=\"commentText"+id+"\" class=\"comment-input col\" maxLength='500'   placeholder=\"Write your comment\">"+
                "<div class=\"input-group-append\" oninput='javascript:forcejoin()'>"+
                "<button class=\"btn btn-success btn-sm d-flex align-items-center\" type=\"button\" id='sendCommentBtn"+id+"' onclick='sendComment("+id+")' style=\"border-radius:0 10px 10px 0;border:0;\">"+
                "<i class=\"material-icons\" style=\"font-size: 18px;\">send</i>"+
                "</button>"+
                "</div>"+
                "</div>"+
                "<p id='errorComment"+id+"' class='form-text error-text text-danger' style='display: none;'>Please enter comment.</p>"+
                "</div>";
        }


        commentsTemplate += "</div>";
        return commentsTemplate;
    }

    function replyTo(name,commentId,id) {
        $("#commentText"+id).focus();
        $('#sendCommentBtn'+id).attr('onclick','javascript:replyComment('+commentId+','+id+');');
        document.getElementById("replyToText"+id).innerHTML = "<p style='font-size: 12px;font-style: italic;margin-bottom: 5px;color: #777;'>Replying to "+name +
            "<a style='font-size: 13px;padding-left: 5px;font-weight: 600;' href='javascript:cancelReply("+commentId+","+id+");'>Cancel</a></p>";
        $("#replyToText"+id).show();
        $("#postList #commentText"+id).focus();
        $('#postList #sendCommentBtn'+id).attr('onclick','javascript:replyComment('+commentId+','+id+');');
       document.querySelector("#postList #replyToText"+id).innerHTML = "<p style='font-size: 12px;font-style: italic;margin-bottom: 5px;color: #777;'>Replying to "+name +
            "<a style='font-size: 13px;padding-left: 5px;font-weight: 600;' href='javascript:cancelReply("+commentId+","+id+");'>Cancel</a></p>";
        $("#postList #replyToText"+id).show();
    }

    function cancelReply(commentId,id) {
        $('#sendCommentBtn'+id).attr('onclick','javascript:sendComment('+id+');');
        document.getElementById("replyToText"+id).innerHTML = "";
        $("#replyToText"+id).hide();
    }

    function replaceAll(str, find, replace) {
        return str.replace(new RegExp(escapeRegExp(find), 'g'), replace);
    }

    function escapeRegExp(str) {
        return str.replace(/([.*+?^=!:$\{\}()|\[\]\/\\])/g, "\\$1");
    }

    function numberFormatter(count) {
        if (count < 1e3) return count;
        if (count >= 1e3 && count < 1e6) return +(count / 1e3).toFixed(1) + "k";
        if (count >= 1e6 && count < 1e9) return +(count / 1e6).toFixed(1) + "M";
        if (count >= 1e9 && count < 1e12) return +(count / 1e9).toFixed(1) + "B";
        if (count >= 1e12) return +(count / 1e12).toFixed(1) + "T";
        return count;
    }

    var text;
    var mcqLink;
    async function paste() {
        text = await navigator.clipboard.readText();
        localStorage.setItem('mcqlink',text);
        mcqLink  = localStorage.getItem('mcqlink');
        if (isUrls(mcqLink)){
            $("#postEditor").val(mcqLink);
        }

    }
    paste();
    function createTextLinks_(text) {
        return (text || "").replace(
            /([^\S]|^)(((https?\:\/\/)|(www\.))(\S+))/gi,
            function (match, space, url) {
                var hyperlink = url;
                if (!hyperlink.match("^https?://")) {
                    hyperlink = "http://" + hyperlink;
                }
                return space + '<a href="' + hyperlink + '">' + url + "</a>";
            }
        );
    }
    function isUrls(s) {
        var regexp = /(^|\W)wonderslate.page.link($|\W)/
        return regexp.test(s);
    }

    function showShareModal(){
        var link = window.location.host+'/groups/groupDtl?groupId='+groupId;
        openShareContentModalGeneric("Interesting groups found. Please click on the link to see the Groups!",link)
    }
    <%}%>
    $(window).load(function() {
        // When the page has loaded
        $("body").css('display','block');
    });

    function removecontent(){
        $('#editPostFileName p').empty();
        $('#editPostImage').attr('src', '');
        $('#editPostImagePreview').hide();
        $('#editPostFilePreview').hide();
        $('.edit-content').hide();
        $('.edit-content-1').hide();
        fileEmpty = true;
        $('#editPostBtn').prop("disabled", false);
    }
    function showCancel(){
        $('.edit-content').show();
    }
    $('.modal-dialog.modal-lg.modal-dialog-centered .textarea-container textarea').click(function () {

        $('#editPostBtn').prop("disabled", false);

    })
    $('.modal-dialog.modal-lg.modal-dialog-centered').change(function () {
        if($(".modal-dialog.modal-lg.modal-dialog-centered #editPostImagePreview").css("display") == "block"){
            showCancel();
        }else if($(".modal-dialog.modal-lg.modal-dialog-centered #editPostFilePreview").css("display") == "block"){
            $('.edit-content-1').show();
        }
    })

    function geturls(){

        var post = $('.post-description ');
        for (var i=0 ; i<post.length-1 ; i++)
        {
            var url= $('.post-description a');

            var splitUrl = url.attr("href").split('&')[3];
            if (splitUrl == 'shared=true')
            {
                localStorage.setItem('myLocal', 'hasTrue');
            }
        }

    }
    $('.indirect-leave').click(function () {
        $("#leaveGroupAdmin").modal('show');
    })


    $('#upload-photo').change(function() {
        $("#errorCoverImage").addClass('hidden');
        var selectedFile = this.files[0];
        var _size = selectedFile.size;
        if(_size >= 2000000){
            validFileSize = false;
            $('#Error1').modal('show');
            removeGroupCoverImage();
        } else {
            validFileSize = true;
        }
        if(!selectedFile.type.match('image.*')) {
            validFileFormat = false;
            $('#Error2').modal('show');
            removeGroupCoverImage();
        } else if (selectedFile.name.search(/[ <>{}#/\\?:!&$^*%`|+"]/g)>-1) {
            validFileFormat = false;
            $('#Error3').modal('show');
            removeGroupCoverImage();
        } else if(validFileSize){
            validFileFormat = true;
            $('#coverImagePreview').show();
            var reader = new FileReader();
            reader.onload = function(e) {
                $("#groupBackground").attr("style", "background-image:url("+e.target.result+")").addClass("show-profile-image");
            }
            reader.readAsDataURL(selectedFile); // convert to base64 string
            var formData = new FormData();
            var files = $("#upload-photo")[0].files;
            if(validFileSize && validFileFormat) {
                $("#errorCoverImage,#errorDPForm").addClass('hidden');
                if(files.length > 0) {
                    formData.append('file',files[0]);
                    formData.append('groupId', groupId);
                    $.ajax({
                        url: "/groups/addGroupCoverImage",
                        type: 'POST',
                        cache: false,
                        contentType: false,
                        processData: false,
                        data: formData,
                        success: function (data) {
                            if (data.status == "success") {
                            }
                        }
                    });
                }
            }
        }});

    function removeGroupCoverImage() {
        $('#coverImagePreview').hide();
        $('#coverImageFileName').text('');
        $('#upload-photo').val('');
    }

    function removingBlankSpaces(str){
        if (!str.replace(/\s/g, '').length) {
            return true;
        }
        else return false;
    }
    $("#groupPostEditModal").on('hidden.bs.modal', function () {
        $(this).data('bs.modal', null);
        $("#errorEditPostEditor").addClass('hidden');
    });
    function hideAdmin() {
        if (groupType == 'channel') {
            $(".admin-header").hide();
        }
    }
    function slickInitialize() {
        if ($('.slider-inner').hasClass('slick-initialized')) {
            $('.slider-inner').slick('unslick');
           // $('.slider-parent').append($("<div class='slider-inner' id='post-slider'></div>"));

            }
        $(".slider-inner").not('.slick-initialized').slick({
        // $('.slider-inner').slick({
            centerMode: true,
            centerPadding: '60px',
            slidesToShow: 1,
            slidesToScroll: 1,
            responsive: [
                {
                    breakpoint: 768,
                    settings: {
                        arrows: true,
                        centerMode: true,
                        centerPadding: '60px',
                        slidesToShow: 1,
                        cssEase: 'linear',
                    }
                },
                {
                    breakpoint: 480,
                    settings: {
                        arrows: true,
                        centerMode: true,
                        centerPadding: '60px',
                        slidesToShow: 1,
                        cssEase: 'linear',
                    }
                }
            ]
        });

    };


    $(document).ready(function(){
        $('[data-toggle="tooltip"]').tooltip({
            tooltipClass: "passwordTooltip"
        });
    });
   function showPinnedElement(){
       if($('div#slider-parent .posts-card').length<1){
           $('#slider-parent p').css("display", "none");
           $('.slider-inner').css("display", "none");
       }
       else
       {
           $('#slider-parent p').css("display", "block");
           $('.slider-inner').css("display", "block");
       }
   }

    function pinPost(postId,currentPin){
        var pinned='';
        if(currentPin==true)pinned="false";
        else pinned="true";
        var dataObj={
            groupId:groupId,
            postId:postId,
            pinned:pinned,
        }
        $.ajax({
            url: "/groups/pinPost",
            type: "POST",
            contentType: "application/json",
            dataType:"json",
            data: JSON.stringify(dataObj),
            success: function (data) {
                if(data.status == "OK") {

                    $('.slider-inner').slick('unslick'); /* ONLY remove the classes and handlers added on initialize */
                    $('.slider-inner').remove(); /* Remove current slides elements, in case that you want to show new slides. */
                    showPosts();
            }
        }
    })
    }


   function showfirstnine(id){

       $('#inside-comments'+id+' .comments-info').css('display','block');
       $('.showNext'+id).css('display','block ');
       $('#firstNine'+id).css('display','none');

   }
   function shownextten(id){
       dict[id]+=1;
       var commentsPageNo= dict[id];
       commentPostId=id;
       <g:remoteFunction controller="groups" action="getCommentListForPost" params="'postId='+id+'&pageNo='+commentsPageNo" onSuccess="showPaginatedComments(data,id)" />
   }
    function showPaginatedComments(data,id) {
        var commentsList = data.commentsList;


        if(commentsList.length>0 && Array.isArray(commentsList)) {
            $("#totalComments" + commentPostId).text(commentsList.length);
            $("#comments" + commentPostId + " #inside-comments" + commentPostId).append(commentsNewUI(commentPostId, commentsList));
            $('#inside-comments'+id+' .comments-info').css('display','block');
        }
        else  $('.showNext'+id).css('display','none ');
    }
    function commentsNewUI(id,comments) {
        var commentsTemplate = "";
        var replyText = "";
        if(comments.length>0) {
            for (var i = 0; i < comments.length; i++) {
                commentedDate = comments[i].dateCreated;
                currentDate = moment(commentedDate).utc().format('YYYY-MM-DD');
                checkCommentedDate = moment().isSame(currentDate, 'day');
                if(checkCommentedDate) {
                    commentedDate = moment(commentedDate).fromNow();
                } else {
                    commentedDate = moment(commentedDate).format('lll');
                }
                commentsTemplate += "<div class=\"comments-info\">" +
                    "<div class=\"user-info d-flex align-items-center\">";
                if(comments[i].profilepic != null) {
                    imageSrc = "/funlearn/showProfileImage?id="+comments[i].userId+"&fileName="+comments[i].profilepic+"&type=user&imgType=passport";
                } else {
                    imageSrc = "${assetPath(src: 'landingpageImages/img_avatar3.png')}";
                }
                commentsTemplate += "<img src="+imageSrc+" class=\"user-info-img mr-2\">";
                if(comments[i].name != null){
                    commentUserName = comments[i].name
                }else{
                    commentUserName = "Unknown user";
                }
                commentsTemplate +=  "<p class=\"mr-2 text-capitalize\" style=\"font-size: 12px;color: #444;\">"+commentUserName+"</p>" +
                    "<p style=\"font-size: 11px;color: rgba(68, 68, 68, 0.48);font-style: italic;\">"+commentedDate+"</p>" +
                    "</div>" +
                    "<div class=\"ml-5\" >" +
                    "<p style=\"font-size: 14px;color: #000;\">"+comments[i].description+"</p>" +
                    "</div>";

                commentsTemplate += "<div class=\"d-flex reply-sec ml-5 mb-2\">";

                var replies = comments[i].repliedDetailsByCommentsId;
                var repliesCount = jQuery.parseJSON(comments[i].repliedCount);
                if(repliesCount>0) {
                    if(repliesCount==1) {
                        replyText = "Reply";
                    } else {
                        replyText = "Replies";
                    }
                    commentsTemplate += "<p><a style='font-size: 11px;font-style: normal;' href='javascript:showReply("+comments[i].id+");'>"+numberFormatter(repliesCount)+" "+replyText+"</a></p>";
                }

                commentsTemplate += "<button onclick=\"javascript:replyTo('"+comments[i].name+"',"+comments[i].id+","+id+");\">Reply</button>"+
                    "</div>";

                commentsTemplate += "<div style=\"display: none;border-left: 1px solid #ddd;\" id=\"replies"+comments[i].id+"\" class=\"ml-5 mt-3 pl-3\">";

                for (var j = 0; j < replies.length; j++) {
                    commentedDate = replies[j].dateCreated;
                    currentDate = moment(commentedDate).utc().format('YYYY-MM-DD');
                    checkCommentedDate = moment().isSame(currentDate, 'day');
                    if(checkCommentedDate) {
                        commentedDate = moment(commentedDate).fromNow();
                    } else {
                        commentedDate = moment(commentedDate).format('lll');
                    }
                    commentsTemplate += "<div class='reply-info pb-3'><div class=\"d-flex align-items-center mb-1\">"+
                        "<p style=\"font-size: 12px;color: #444;\" class=\"mr-2 text-capitalize\">"+(replies[j].name?replies[j].name:'Unknown User')+"</p>"+
                        "<p style=\"font-size: 11px;color: rgba(68, 68, 68, 0.48);font-style: italic;\">"+commentedDate+"</p>"+
                        "</div>"+
                        "<p style=\"font-size: 14px;color: #000;\">"+replies[j].description+"</p></div>";
                }
                commentsTemplate += "</div></div>";
            }
        } else {
            commentsTemplate += "<img src='${assetPath(src: 'groups/speech.gif')}' class='noComt'/>\n"+
                "<p style='font-size: 13px;text-align: center;'>No comments found!</p>";
            hideCommentShowMore();
        }

        commentsTemplate += "</div><div class=\"mt-3 comment-input-box\">"+
            "<div id='replyToText"+id+"' style='display:none;'></div>"+
            "<div class=\"input-group\" oninput='javascript:forcejoin()'>"+
            "</div>"+
            "</div>"+
            "<p id='errorComment"+id+"' class='form-text error-text text-danger' style='display: none;'>Please enter comment.</p>"+
            "</div>";

        commentsTemplate += "</div>";
        return commentsTemplate;
    }

function postDetailsNewPage(postId) {
    window.location.href = '/groups/postDetail?groupId='+groupId+'&postId='+postId+'/';
}

</script>

<script src="https://cdn.ckeditor.com/ckeditor5/28.0.0/balloon/ckeditor.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js"></script>


</body>
</html>

