
<g:render template="/etexts/navheader_new"></g:render>

<section class="contactus">
    <div class="container card shadow p-4 p-md-5 my-md-5">
        <h1>Feedback</h1>
        <hr class="mb-4">
        <p>We would love to hear your feedback on the features, functionality and content of the platform!</p>
        <p>Please take a moment to let us know what you think.</p>
        <div class="contact_form feedback_form col-12 px-0 mt-4">
            <form class="feedback-form" method="post"  action="/log/addFeedbackForm" novalidate>
                <label>How useful are the following platform features?<span class="text-danger">*</span></label>
                <div class="form-row align-items-center">
                    <div class="platform col-sm-5">
                        <p><small>Platform Features</small></p>
                    </div>
                    <div class="poor col-sm-1 text-center">
                        <p><small>Unacceptable</small></p>
                    </div>
                    <div class="average col-sm-1 text-center">
                        <p><small>Average</small></p>
                    </div>
                    <div class="neutral col-sm-1 text-center">
                        <p><small>Neutral</small></p>
                    </div>
                    <div class="happy col-sm-1 text-center">
                        <p><small>Happy</small></p>
                    </div>
                    <div class="very-happy col-sm-1 text-center">
                        <p><small>Very Happy</small></p>
                    </div>
                </div>
                <div class="form-row align-items-center">
                    <div class="platform col-sm-5">
                        <p>Search</p>
                    </div>
                    <div class="poor col-sm-1 text-center">
                        <div class="custom-control custom-radio">
                            <input type="radio" id="search1" name="search" class="custom-control-input" value="Unacceptable" required>
                            <label class="custom-control-label" for="search1"></label>
                        </div>
                    </div>
                    <div class="average col-sm-1 text-center">
                        <div class="custom-control custom-radio">
                            <input type="radio" id="search2" name="search" class="custom-control-input" value="Average" required>
                            <label class="custom-control-label" for="search2"></label>
                        </div>
                    </div>
                    <div class="neutral col-sm-1 text-center">
                        <div class="custom-control custom-radio">
                            <input type="radio" id="search3" name="search" class="custom-control-input" value="Neutral" required>
                            <label class="custom-control-label" for="search3"></label>
                        </div>
                    </div>
                    <div class="happy col-sm-1 text-center">
                        <div class="custom-control custom-radio">
                            <input type="radio" id="search4" name="search" class="custom-control-input" value="Happy" required>
                            <label class="custom-control-label" for="search4"></label>
                        </div>
                    </div>
                    <div class="very-happy col-sm-1 text-center">
                        <div class="custom-control custom-radio">
                            <input type="radio" id="search5" name="search" class="custom-control-input" value="Very Happy" required>
                            <label class="custom-control-label" for="search5"></label>
                        </div>
                    </div>
                </div>
                <hr class="mt-0">
                <div class="form-row align-items-center">
                    <div class="platform col-sm-5">
                        <p>Reading experience</p>
                    </div>
                    <div class="poor col-sm-1 text-center">
                        <div class="custom-control custom-radio">
                            <input type="radio" id="reading1" name="reading" class="custom-control-input" value="Unacceptable" required>
                            <label class="custom-control-label" for="reading1"></label>
                        </div>
                    </div>
                    <div class="average col-sm-1 text-center">
                        <div class="custom-control custom-radio">
                            <input type="radio" id="reading2" name="reading" class="custom-control-input" value="Average" required>
                            <label class="custom-control-label" for="reading2"></label>
                        </div>
                    </div>
                    <div class="neutral col-sm-1 text-center">
                        <div class="custom-control custom-radio">
                            <input type="radio" id="reading3" name="reading" class="custom-control-input" value="Neutral" required>
                            <label class="custom-control-label" for="reading3"></label>
                        </div>
                    </div>
                    <div class="happy col-sm-1 text-center">
                        <div class="custom-control custom-radio">
                            <input type="radio" id="reading4" name="reading" class="custom-control-input" value="Happy" required>
                            <label class="custom-control-label" for="reading4"></label>
                        </div>
                    </div>
                    <div class="very-happy col-sm-1 text-center">
                        <div class="custom-control custom-radio">
                            <input type="radio" id="reading5" name="reading" class="custom-control-input" value="Very Happy" required>
                            <label class="custom-control-label" for="reading5"></label>
                        </div>
                    </div>
                </div>
                <hr class="mt-0">
                <div class="form-row align-items-center">
                    <div class="platform col-sm-5">
                        <p>My Library</p>
                    </div>
                    <div class="poor col-sm-1 text-center">
                        <div class="custom-control custom-radio">
                            <input type="radio" id="library1" name="library" class="custom-control-input" value="Unacceptable" required>
                            <label class="custom-control-label" for="library1"></label>
                        </div>
                    </div>
                    <div class="average col-sm-1 text-center">
                        <div class="custom-control custom-radio">
                            <input type="radio" id="library2" name="library" class="custom-control-input" value="Average" required>
                            <label class="custom-control-label" for="library2"></label>
                        </div>
                    </div>
                    <div class="neutral col-sm-1 text-center">
                        <div class="custom-control custom-radio">
                            <input type="radio" id="library3" name="library" class="custom-control-input" value="Neutral" required>
                            <label class="custom-control-label" for="library3"></label>
                        </div>
                    </div>
                    <div class="happy col-sm-1 text-center">
                        <div class="custom-control custom-radio">
                            <input type="radio" id="library4" name="library" class="custom-control-input" value="Happy" required>
                            <label class="custom-control-label" for="library4"></label>
                        </div>
                    </div>
                    <div class="very-happy col-sm-1 text-center">
                        <div class="custom-control custom-radio">
                            <input type="radio" id="library5" name="library" class="custom-control-input" value="Very Happy" required>
                            <label class="custom-control-label" for="library5"></label>
                        </div>
                    </div>
                </div>
                <hr class="mt-0">
                <div class="form-row align-items-center">
                    <div class="platform col-sm-5">
                        <p>Notes</p>
                    </div>
                    <div class="poor col-sm-1 text-center">
                        <div class="custom-control custom-radio">
                            <input type="radio" id="notes1" name="notes" class="custom-control-input" value="Unacceptable" required>
                            <label class="custom-control-label" for="notes1"></label>
                        </div>
                    </div>
                    <div class="average col-sm-1 text-center">
                        <div class="custom-control custom-radio">
                            <input type="radio" id="notes2" name="notes" class="custom-control-input" value="Average" required>
                            <label class="custom-control-label" for="notes2"></label>
                        </div>
                    </div>
                    <div class="neutral col-sm-1 text-center">
                        <div class="custom-control custom-radio">
                            <input type="radio" id="notes3" name="notes" class="custom-control-input" value="Neutral" required>
                            <label class="custom-control-label" for="notes3"></label>
                        </div>
                    </div>
                    <div class="happy col-sm-1 text-center">
                        <div class="custom-control custom-radio">
                            <input type="radio" id="notes4" name="notes" class="custom-control-input" value="Happy" required>
                            <label class="custom-control-label" for="notes4"></label>
                        </div>
                    </div>
                    <div class="very-happy col-sm-1 text-center">
                        <div class="custom-control custom-radio">
                            <input type="radio" id="notes5" name="notes" class="custom-control-input" value="Very Happy" required>
                            <label class="custom-control-label" for="notes5"></label>
                        </div>
                    </div>
                </div>
                <hr class="mt-0">
                <div class="form-row align-items-center">
                    <div class="platform col-sm-5">
                        <p>Products for your Library</p>
                    </div>
                    <div class="poor col-sm-1 text-center">
                        <div class="custom-control custom-radio">
                            <input type="radio" id="products1" name="products" class="custom-control-input" value="Unacceptable" required>
                            <label class="custom-control-label" for="products1"></label>
                        </div>
                    </div>
                    <div class="average col-sm-1 text-center">
                        <div class="custom-control custom-radio">
                            <input type="radio" id="products2" name="products" class="custom-control-input" value="Average" required>
                            <label class="custom-control-label" for="products2"></label>
                        </div>
                    </div>
                    <div class="neutral col-sm-1 text-center">
                        <div class="custom-control custom-radio">
                            <input type="radio" id="products3" name="products" class="custom-control-input" value="Neutral" required>
                            <label class="custom-control-label" for="products3"></label>
                        </div>
                    </div>
                    <div class="happy col-sm-1 text-center">
                        <div class="custom-control custom-radio">
                            <input type="radio" id="products4" name="products" class="custom-control-input" value="Happy" required>
                            <label class="custom-control-label" for="products4"></label>
                        </div>
                    </div>
                    <div class="very-happy col-sm-1 text-center">
                        <div class="custom-control custom-radio">
                            <input type="radio" id="products5" name="products" class="custom-control-input" value="Very Happy" required>
                            <label class="custom-control-label" for="products5"></label>
                        </div>
                    </div>
                </div>
                <hr class="mt-0">
                <div class="form-row align-items-center mb-4">
                    <div class="platform col-sm-5">
                        <p>User Dashboard</p>
                    </div>
                    <div class="poor col-sm-1 text-center">
                        <div class="custom-control custom-radio">
                            <input type="radio" id="dashboard1" name="dashboard" class="custom-control-input" value="Unacceptable" required>
                            <label class="custom-control-label" for="dashboard1"></label>
                        </div>
                    </div>
                    <div class="average col-sm-1 text-center">
                        <div class="custom-control custom-radio">
                            <input type="radio" id="dashboard2" name="dashboard" class="custom-control-input" value="Average" required>
                            <label class="custom-control-label" for="dashboard2"></label>
                        </div>
                    </div>
                    <div class="neutral col-sm-1 text-center">
                        <div class="custom-control custom-radio">
                            <input type="radio" id="dashboard3" name="dashboard" class="custom-control-input" value="Neutral" required>
                            <label class="custom-control-label" for="dashboard3"></label>
                        </div>
                    </div>
                    <div class="happy col-sm-1 text-center">
                        <div class="custom-control custom-radio">
                            <input type="radio" id="dashboard4" name="dashboard" class="custom-control-input" value="Happy" required>
                            <label class="custom-control-label" for="dashboard4"></label>
                        </div>
                    </div>
                    <div class="very-happy col-sm-1 text-center">
                        <div class="custom-control custom-radio">
                            <input type="radio" id="dashboard5" name="dashboard" class="custom-control-input" value="Very Happy" required>
                            <label class="custom-control-label" for="dashboard5"></label>
                        </div>
                    </div>
                </div>

                <label>How was the browsing experience?<span class="text-danger">*</span></label>
                <div class="form-row align-items-center">
                    <div class="col-sm-6">
                        <div class="custom-control custom-radio">
                            <input type="radio" id="browsing1" name="browsing" class="custom-control-input" value="Yes, I am happy" required>
                            <label class="custom-control-label" for="browsing1">Yes, I am happy</label>
                        </div>
                    </div>
                    <div class="col-sm-6">
                        <div class="custom-control custom-radio">
                            <input type="radio" id="browsing2" name="browsing" class="custom-control-input" value="No, it needs improvement" required>
                            <label class="custom-control-label" for="browsing2">No, it needs improvement</label>
                        </div>
                    </div>
                </div>

                <div class="form-group mt-4 mb-5">
                    <label>Write your review here:</label>
                    <textarea class="form-control" name="review"></textarea>
                </div>

                <label>Did you find what you were looking for?<span class="text-danger">*</span></label>
                <div class="form-row align-items-center">
                    <div class="col-sm-6">
                        <div class="custom-control custom-radio">
                            <input type="radio" id="looking1" name="looking" class="custom-control-input" value="Yes, it was easy" required>
                            <label class="custom-control-label" for="looking1">Yes, it was easy</label>
                        </div>
                    </div>
                    <div class="col-sm-6">
                        <div class="custom-control custom-radio">
                            <input type="radio" id="looking2" name="looking" class="custom-control-input" value="No, help me with it" required>
                            <label class="custom-control-label" for="looking2">No, help me with it</label>
                        </div>
                    </div>
                </div>

                <div class="form-group mt-4 mb-5">
                    <label>Let us know of your requirements:</label>
                    <textarea class="form-control" name="requirements"></textarea>
                </div>

                <div class="form-group mt-4 mb-5">
                    <label>What features, if any, would significantly improve your experience with SAGE e-texts?</label>
                    <textarea class="form-control" name="feature_improvements"></textarea>
                </div>

                <div class="form-group">
                    <label>First Name <span class="text-danger">*</span> </label>
                    <input type="text" class="form-control" name="first_name" required>
                    <div class="invalid-feedback">
                        Please enter your name.
                    </div>
                </div>
                <div class="form-group">
                    <label>Email <span class="text-danger">*</span> </label>
                    <input type="email" class="form-control" name="email_id" required>
                    <div class="invalid-feedback">
                        Please enter valid email address.
                    </div>
                </div>
                <div class="form-group">
                    <label>Institution Name</label>
                    <input type="text" class="form-control" name="institute_name">
                </div>
                <button type="submit" class="btn btn-primary col-md-3 mt-3">FINISH</button>
            </form>
        </div>
    </div>
</section>

<script>
    (function() {
        'use strict';
        window.addEventListener('load', function() {
            var forms = document.getElementsByClassName('feedback-form');
            var validation = Array.prototype.filter.call(forms, function(form) {
                form.addEventListener('submit', function(event) {
                    if (form.checkValidity() === false) {
                        event.preventDefault();
                        event.stopPropagation();
                    } else {
                        setTimeout(function(){
                            alert('Submitted successfully!');
                        },100);
                    }
                    form.classList.add('was-validated');
                }, false);
            });
        }, false);
    })();
</script>

<g:render template="/etexts/footer_new"></g:render>
