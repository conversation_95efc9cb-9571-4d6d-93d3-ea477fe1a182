// locations to search for config files that get merged into the main config;
// config files can be ConfigSlurper scripts, Java properties files, or classes
// in the classpath in ConfigSlurper format

// grails.config.locations = [ "classpath:${appName}-config.properties",
//                             "classpath:${appName}-config.groovy",
//                             "file:${userHome}/.grails/${appName}-config.properties",
//                             "file:${userHome}/.grails/${appName}-config.groovy"]

// if (System.properties["${appName}.config.location"]) {
//    grails.config.locations << "file:" + System.properties["${appName}.config.location"]
// }

grails.project.groupId = appName // change this to alter the default package name and Maven publishing destination
grails.app.context = "/"

// The ACCEPT header will not be used for content negotiation for user agents containing the following strings (defaults to the 4 major rendering engines)
grails.mime.disable.accept.header.userAgents = ['Gecko', 'WebKit', 'Presto', 'Trident']
grails.mime.types = [ // the first one is the default format
                      all:           '*/*', // 'all' maps to '*' or the first available format in withFormat
                      atom:          'application/atom+xml',
                      css:           'text/css',
                      csv:           'text/csv',                   
                      form:          'application/x-www-form-urlencoded',
                      html:          ['text/html','application/xhtml+xml'],
                      js:            'text/javascript',
                      json:          ['application/json', 'text/json'],
                      multipartForm: 'multipart/form-data',
                      rss:           'application/rss+xml',
                      text:          'text/plain',
                      hal:           ['application/hal+json','application/hal+xml'],
                      xml:           ['text/xml', 'application/xml'],
                      docx:       ['application/vnd.openxmlformats-officedocument.wordprocessingml.document']
]

// URL Mapping Cache Max Size, defaults to 5000
//grails.urlmapping.cache.maxsize = 1000

// Legacy setting for codec used to encode data with ${}
grails.views.default.codec = "html"

// The default scope for controllers. May be prototype, session or singleton.
// If unspecified, controllers are prototype scoped.
grails.controllers.defaultScope = 'singleton'

// GSP settings
grails {
    views {
        gsp {
            encoding = 'UTF-8'
            htmlcodec = 'xml' // use xml escaping instead of HTML4 escaping
            codecs {
                expression = 'html' // escapes values inside ${}
                scriptlet = 'html' // escapes output from scriptlets in GSPs
                taglib = 'none' // escapes output from taglibs
                staticparts = 'none' // escapes output from static template parts
            }
        }
        // escapes all not-encoded output at final stage of outputting
        // filteringCodecForContentType.'text/html' = 'html'
    }
}


grails.converters.encoding = "UTF-8"
// scaffolding templates configuration
grails.scaffolding.templates.domainSuffix = 'Instance'

// Set to false to use the new Grails 1.2 JSONBuilder in the render method
grails.json.legacy.builder = false
// enabled native2ascii conversion of i18n properties files
grails.enable.native2ascii = true
// packages to include in Spring bean scanning
grails.spring.bean.packages = []
// whether to disable processing of multi part requests
grails.web.disable.multipart=false

// request parameters to mask when logging exceptions
grails.exceptionresolver.params.exclude = ['password']

// configure auto-caching of queries by default (if false you can cache individual queries with 'cache: true')
grails.hibernate.cache.queries = false

// configure passing transaction's read-only attribute to Hibernate session, queries and criterias
// set "singleSession = false" OSIV mode in hibernate configuration after enabling
grails.hibernate.pass.readonly = false
// configure passing read-only to OSIV session by default, requires "singleSession = false" OSIV mode
grails.hibernate.osiv.readonly = false

environments {
    development {
        grails.logging.jul.usebridge = true
        uploadFolder = "c:/temp/upload/"
    }
    production {
        grails.logging.jul.usebridge = false
        uploadFolder = "c:/temp/upload/"
        // TODO: grails.serverURL = "http://www.changeme.com"
		grails.serverURL = "https://www.wonderslate.com"
    }
}

// log4j configuration
log4j.main = {
    // Example of changing the log pattern for the default console appender:
    //
    //appenders {
    //    console name:'stdout', layout:pattern(conversionPattern: '%c{2} %m%n')
    //}

    error  'org.codehaus.groovy.grails.web.servlet',        // controllers
            'org.codehaus.groovy.grails.web.pages',          // GSP
            'org.codehaus.groovy.grails.web.sitemesh',       // layouts
            'org.codehaus.groovy.grails.web.mapping.filter', // URL mapping
            'org.codehaus.groovy.grails.web.mapping',        // URL mapping
            'org.codehaus.groovy.grails.commons',            // core / classloading
            'org.codehaus.groovy.grails.plugins',            // plugins
            'org.codehaus.groovy.grails.orm.hibernate',      // hibernate integration
            'org.springframework',
            'org.hibernate',
            'net.sf.ehcache.hibernate'
}
//added by Anand

// Added by the Spring Security Core plugin:
grails.plugin.springsecurity.userLookup.userDomainClassName = 'com.wonderslate.usermanagement.User'
grails.plugin.springsecurity.userLookup.authorityJoinClassName = 'com.wonderslate.usermanagement.UserRole'
grails.plugin.springsecurity.authority.className = 'com.wonderslate.usermanagement.Role'
grails.plugin.springsecurity.controllerAnnotations.staticRules = [
        '/':                              ['permitAll'],
        '/index':                         ['permitAll'],
        '/index.gsp':                     ['permitAll'],
        '/assets/**':                     ['permitAll'],
        '/**/js/**':                      ['permitAll'],
        '/**/css/**':                     ['permitAll'],
        '/**/images/**':                  ['permitAll'],
        '/**/favicon.ico':                ['permitAll'],
        '/creation/index':                ['permitAll'],
        '/funlearn/**':                   ['permitAll'],
        '/*':                              ['permitAll'],
        '/**':                            ['permitAll'],
        '/oauth/**':                      ['permitAll']
]

grails.plugin.springsecurity.filterChain.chainMap = [
        '/api/**': 'JOINED_FILTERS,-exceptionTranslationFilter,-authenticationProcessingFilter,-securityContextPersistenceFilter,-rememberMeAuthenticationFilter',  // Stateless chain
        '/**': 'JOINED_FILTERS,-restTokenValidationFilter,-restExceptionTranslationFilter'                                                                          // Traditional chain
]

grails.plugin.springsecurity.rest.login.active=true
grails.plugin.springsecurity.rest.login.endpointUrl='/api/login'
grails.plugin.springsecurity.rest.login.failureStatusCode=401
grails.plugin.springsecurity.rest.login.useJsonCredentials=true
grails.plugin.springsecurity.rest.login.usernamePropertyName='username'
grails.plugin.springsecurity.rest.login.passwordPropertyName='password'
grails.plugin.springsecurity.rest.token.storage.useGorm = true
grails.plugin.springsecurity.rest.token.storage.gorm.tokenDomainClassName = 'com.wonderslate.usermanagement.AuthenticationToken'
grails.plugin.springsecurity.rest.token.storage.gorm.tokenValuePropertyName = 'token'
grails.plugin.springsecurity.rest.token.storage.gorm.usernamePropertyName = 'username'
grails.plugin.springsecurity.rest.token.validation.active=true
grails.plugin.springsecurity.rest.token.validation.headerName='X-Auth-Token'
grails.plugin.springsecurity.rest.token.validation.endpointUrl='/api/validate'
grails.plugin.springsecurity.rest.token.validation.useBearerToken=false

//cors config.
cors.enabled=true
cors.url.pattern = ['/api/*','/wonderslate/funlearn/*','/**/funlearn/**']
cors.headers=[
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Credentials': true,
        'Access-Control-Allow-Headers': 'origin, authorization, accept, content-type, x-requested-with,x-auth-token',
        'Access-Control-Allow-Methods': 'GET, HEAD, POST, PUT, DELETE, TRACE, OPTIONS',
        'Access-Control-Max-Age': 3600
]

grails.plugin.springsecurity.rememberMe.alwaysRemember=true

grails.plugin.springsecurity.useSecurityEventListener = true

grails.plugin.springsecurity.onInteractiveAuthenticationSuccessEvent = { e, appCtx ->
    def userLogService = appCtx.getBean('userLogService')
    userLogService.addUserLog("login")
}

grails.plugin.springsecurity.onAuthenticationSuccessEvent  = { e, appCtx ->
}

grails.plugin.springsecurity.auth.loginFormUrl = '/security/loginform'
grails.plugin.springsecurity.failureHandler.defaultFailureUrl = '/security/loginfailedmanager'
grails.plugin.springsecurity.successHandler.defaultTargetUrl = '/security/loginmanager'
grails.plugin.springsecurity.logout.afterLogoutUrl='/security/logout'

def appName = grails.util.Metadata.current.'app.name'
def baseURL = grails.serverURL ?: "http://localhost:8080"

oauth {
    // ...
    providers {
        // for Google OAuth 2.0
        google {
            api = org.grails.plugin.springsecurity.oauth.GoogleApi20
            key = '1085788834896-rldq23bt8aaihd462iku4220udfnmfa0.apps.googleusercontent.com'
            secret = 'sNk98I-gsdYZamldk1O_27Cd'
            successUri = '/oauth/google/success'
            failureUri = '/oauth/google/error'
            callback = "${baseURL}/oauth/google/callback"
            scope = 'https://www.googleapis.com/auth/userinfo.profile https://www.googleapis.com/auth/userinfo.email'
        }
        google_localhost {
            api = org.grails.plugin.springsecurity.oauth.GoogleApi20
            key = '1085788834896-rldq23bt8aaihd462iku4220udfnmfa0.apps.googleusercontent.com'
            secret = 'sNk98I-gsdYZamldk1O_27Cd'
            successUri = '/oauth/google/success'
            failureUri = '/oauth/google/error'
            callback = "${baseURL}/oauth/google/callback"
            scope = 'https://www.googleapis.com/auth/userinfo.profile https://www.googleapis.com/auth/userinfo.email'
        }

        facebook {
            api = org.scribe.builder.api.FacebookApi
            key = '448101868718363'
            secret = '68a8e61bdf6ba586e6cdab71474daf48'
            scope = 'email,user_about_me'
            successUri = '/oauth/facebook/success'
            failureUri = '/oauth/facebook/error'
            callback = "${baseURL}/oauth/facebook/callback"
        }
        facebook_localhost {
            api = org.scribe.builder.api.FacebookApi
            key = '448101868718363'
            secret = '68a8e61bdf6ba586e6cdab71474daf48'
            scope = 'email,user_about_me'
            successUri = '/oauth/facebook/success'
            failureUri = '/oauth/facebook/error'
            callback = "${baseURL}/oauth/facebook/callback"
        }
        // ...
    }
}


//grails.plugin.springsecurity.rememberMe.persistent = true
//grails.plugin.springsecurity.rememberMe.persistentToken.domainClassName = 'com.wonderslate.usermanagement.PersistentLogin'

grails {
   mail {
     host = "smtp.gmail.com"
     port = 465
     username = "<EMAIL>"
     password = "yourpassword"
     props = ["mail.smtp.auth":"true", 					   
              "mail.smtp.socketFactory.port":"465",
              "mail.smtp.socketFactory.class":"javax.net.ssl.SSLSocketFactory",
              "mail.smtp.socketFactory.fallback":"false"]

   }
}
grails.mail.overrideAddress="<EMAIL>"
grails.mail.disabled=true
grails.mail.default.from="Wonderslate <<EMAIL>>"
wordpress.url='http://*************:8888/wordpress/'
wordpress.blogId=0
wordpress.username='root'
wordpress.password='root'


quartz {
    autoStartup = true
    jdbcStore = false
}
environments {
    development {
        quartz {
            autoStartup = false
        }
    }
	
    test {
        quartz {
            autoStartup = false
        }
    }	
}

grails.razorpay.keyId='rzp_test_Hs6ZcOvyPgPdJK'
grails.razorpay.secretKey='0krPs8xnZhudWnlU6Jiymd9h'
grails.processpdf.url='http://process.wonderslate.com'