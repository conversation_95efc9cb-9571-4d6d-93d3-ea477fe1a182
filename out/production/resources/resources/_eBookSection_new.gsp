<script>$('link[data-role="baseline"]').attr('href', '');</script>

<% Integer priceCount = bookPriceDtls.size() %>
<style>
.bookDetails__description-tab .tabWrapper .tabContent .bookContains{
    align-items: unset !important;
}
.unknownCoverImg{
    height: 350px;
    border-radius: 5px;
    color: #fff;
    padding: 15px;
    position: relative;
    width: 276px;
}
.unknownCoverImg::after{
    content: '';
    position: absolute;
    top: 0;
    left: 5px;
    bottom: 0;
    width: 2px;
    background: #0000001A;
    box-shadow: 1px 0 3px rgba(255, 255, 255, 0.3);
}
.unknownCoverImgTitle{
    text-align: center;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px){
    footer .footer-fluid{
        padding-bottom: 20rem !important;
    }
}
</style>
<style>
<%if(priceCount<=1){%>
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px){
    .bookDetails__info-dtls__price{
        grid-template-columns: repeat(1,1fr);
        padding: 10px;
    }
}
<%}else {%>
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px){
    .bookDetails__info-dtls__price-card .bookType p{
        font-size: 15px;
    }
}
<%}%>
<%if("1".equals(""+session["siteId"]) || "true".equals(session["prepjoySite"])){%>
    .bookDetails__description{
        margin-top:1rem;
    }
<%} else if(("3".equals(""+session["siteId"] )|| "38".equals(""+session['siteId'])) && !inLibrary && priceCount>2){%>
    .bookDetails__description{
        margin-top: 1rem;
    }
<%} else if(priceCount<=1){%>
    .bookDetails__description{
        margin-top:1rem;
    }
<%} else if(priceCount>2){%>
    .bookDetails__description{
        margin-top: 1.2rem;
    }
<%}%>
</style>


<%if("true".equals(session["prepjoySite"])){%>
<style>
.bookDetails__info-dtls__price-card,
.bookDetails__coverImage,
.infoSection,
.bookDetails__paperback,
.bookDetails__description,
.bookDetails__info .infoSection{
    border: 1px solid rgba(255,255,255, 0.25) !important;
}
.bookDetails__features .featureIcons_dtl,
.activeCart{
    background-color: rgba(255,255,255,0.4) !important;
}
.bookDetails__info-dtls__price-card .saving__price{
    color: green !important;
}
.bookDetails__info-dtls__price-card.activeCart .saving__price{
    color: #2cfd2c !important;
}
.bookDetails__info-dtls__price-card .bookType img,
.amazonLogo{
    filter: invert(100);
}
.bookDetails__info-publisherName,
.breadcrumb-item button,
.bookDetails__paperback h3,
.tabContent ol{
    color: #fff !important;
}
.bookDetails__info-dtls__box h3,
.bookDetails__info-dtls__box span{
    color: rgba(255, 255, 255, 0.4) !important;
}
.bookDetails__coverImage--freeChapterBtn a,
.bookDetails__features .bookImp ul li div p,
.reviewCard__content p{
    color: #000 !important;
}
</style>
<%}%>

<%
    String bookCoverImage = ""
    boolean showSampleChapter = true
    String registerBookTitle = booksMst.title.replaceAll(' ', '-')
    Long registerBookId = booksMst.id
    String bookDtlSEOTitle = (publisherNameForTitle!=null?publisherNameForTitle:"")+" "+booksMst.title
    String pulisherPageURL = ""
    String sampleChapterText = ""
    boolean ebook
    boolean printBook
    if (bookPriceDtls.bookType.contains('printbook') || bookPriceDtls.bookType.contains('combo')) {
        printBook = true
    }
    if(bookPriceDtls.bookType.contains('eBook') || bookPriceDtls.bookType.contains('testSeries') || bookPriceDtls.bookType.contains('bookGPT')){
        ebook=true
    }
    if(booksMst.coverImage!=null) {
        if(booksMst.coverImage.startsWith("http")){
            bookCoverImage = booksMst.coverImage
        }else{
            bookCoverImage = "/funlearn/showProfileImage?id="+booksMst.id+"&fileName="+booksMst.coverImage+"&type=books&imgType=webp"
        }
    }

    if((ebook && printBook)  || (ebook && !printBook)){
        if(!"test".equals(booksMst.bookType)){
             sampleChapterText = session['wileySite'] ? 'Look Inside' : 'Try for Free'
            if (session['wileySite'] && showPrice){
                showSampleChapter = false
            }else{
                showSampleChapter = true
            }
        }
    }else{
        showSampleChapter = false;
    }

    if("true".equals(session["commonWhiteLabel"])){
        pulisherPageURL= "/sp/"+session["siteName"]+"/store?linkSource=bookDetail&publisher="+publisherName.replaceAll(" ","-")
    }else if("true".equals(session["prepjoySite"])){
        pulisherPageURL= "/"+session["entryController"]+"/eBooks?linkSource=bookDetail&publisher="+publisherName.replaceAll(" ","-")
    }else {
        pulisherPageURL= "/"+session["entryController"]+"/store?linkSource=bookDetail&publisher="+publisherName.replaceAll(" ","-")
    }
%>

<div class="bookDetails__container">
    <div class="details__breadcrum">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item" style="min-width: 20px;padding: 5px"><button onclick="openBreadcrumbLink('${booksTagDtl.level}','','');">${booksTagDtl.level}</button></li>
                <li class="breadcrumb-item" style="min-width: 20px;padding: 5px"><button onclick="openBreadcrumbLink('${booksTagDtl.level}','${booksTagDtl.syllabus}','');">${booksTagDtl.syllabus}</button></li>
                <li class="breadcrumb-item" style="min-width: 20px;padding: 5px"><button onclick="openBreadcrumbLink('${booksTagDtl.level}','${booksTagDtl.syllabus}','${booksTagDtl.grade}');">${booksTagDtl.grade}</button></li>
            </ol>
        </nav>
    </div>
</div>

<div class="bookDetails__container">
    <div class="bookDetails">
        <!---- COVER IMAGE SECTION ---->
        <div class="bookDetails__coverImage">
            <div class="imgAndFeature">
                <% if(booksMst.coverImage!=null){%>
                <picture>
                    <source media="(min-width: 768px)" srcset="${bookCoverImage}">
                    <source media="(max-width: 767px)" srcset="${bookCoverImage}">
                    <img src="${bookCoverImage}" alt="${booksMst.title}" width="276" height="375">
                </picture>
                <%}else{%>
                <div class="unknownCoverImg">
                    <div class="unknownCoverImgTitle">
                        <p>${bookDtlSEOTitle}</p>
                    </div>
                </div>
                <%}%>
                <%if("1".equals(""+session["siteId"]) || "true".equals(session["prepjoySite"])){%>
                <div class="featureIcons_dtl-icons mob">
                    <div class="featureIcons_dtl-icon">
                        <img src="${assetPath(src: 'resource/phone.png')}" alt="eBook" width="30px" height="30px">
                        <p>eBook</p>
                    </div>
                    <div class="featureIcons_dtl-icon">
                        <img src="${assetPath(src: 'resource/exam.png')}" alt="Mock Test" width="35px" height="35px">
                        <p>Mock Test</p>
                    </div>
                    <div class="featureIcons_dtl-icon">
                        <img src="${assetPath(src: 'resource/notespad.png')}" alt="Notes" width="35px" height="35px">
                        <p>Notes</p>
                    </div>
                    <div class="featureIcons_dtl-icon">
                        <img src="${assetPath(src: 'resource/flashcards-icon.png')}" alt="Flashcards" width="35px" height="35px">
                        <p>Flashcards</p>
                    </div>
                </div>
                <%}%>
            </div>
            <% if(showSampleChapter){
            if("ebookwithai".equals(booksMst.bookType) || "bookgpt".equals(booksMst.bookType)){%>
            <div class="bookDetails__coverImage--freeChapterBtn">
                <a href="/prompt/bookgpt?siteName=${session["siteName"]}&bookId=${booksMst.id}" class="btn btn-preview" target="_blank" style="background: #FBBC04 !important;border: none !important;font-weight: 400">Try for Free</a>
            </div>
            <%}else{%>
            <div class="bookDetails__coverImage--freeChapterBtn">
            <a href="/${registerBookTitle}/ebook?bookId=${registerBookId}&siteName=${params.siteName}" id="openSampleChapter" title="${booksMst.title}" target="_blank" style="background: #FBBC04 !important;border: none !important;font-weight: 400">${sampleChapterText}</a>
            </div>
            <%}}%>
        </div>
        <!---- BOOK INFO SECTION ---->
        <div class="bookDetails__info">
            <div class="infoSection">
                <h1 class="bookDetails__info-title">${bookDtlSEOTitle}</h1>
                <h2 class="bookDetails__info-publisherName">By <a href="${pulisherPageURL}">${publisherName}</a></h2>
                <div class="bookDetails__info-dtls">
                    <div class="bookDetails__info-dtls__box">
                        <h3>Author(s)</h3>
                        <span>:</span>
                        <h3>${booksMst.authors}</h3>
                    </div>
                    <% if(booksMst.isbn != null && booksMst.isbn != '') { %>
                    <div class="bookDetails__info-dtls__box">
                        <h3>ISBN</h3>
                        <span>:</span>
                        <h3>${booksMst.isbn}</h3>
                    </div>
                    <%}%>
                </div>
                <div class="bookDetails__info-dtls__rating">
                    <div class="starIcons">
                        <i class="fa-solid fa-star"></i>
                        <i class="fa-solid fa-star"></i>
                        <i class="fa-solid fa-star"></i>
                        <i class="fa-solid fa-star"></i>
                    </div>
                    <%if("1".equals(""+session["siteId"]) || "true".equals(session["prepjoySite"])){%>
                        <a href="#reviewsMap">Reviews</a>
                    <%}%>
                </div>
                <div class="bookDetails__info-dtls__price" id="bookVariants"></div>
                <div class="bookDetails__info-dtls__addToCart">
                    <button class="addToCartBtn" style="grid-column-end: 2;display: none">Add to Cart</button>
                </div>
                <%if(("3".equals(""+session["siteId"] )|| "38".equals(""+session['siteId'])) && !inLibrary ){%>
                <a href="/wsLibrary/accessCode?bookLevel=true&bookId=${booksMst.id}&bookTitle=${booksMst.title}" style="margin:10px 0 0 8px;font-size: 13px;display: block">Do you have access code?</a>
                <%}%>
                <a href="https://wa.me/918088443860" id="whatsappLink" style="margin:10px 0 0 8px;font-size: 13px;width: fit-content;display: block">Do you need any help with your purchase?</a>
            </div>
            <%if(subscriptionMst!=null){%>
            <div class="bookDetails__subscription mt-3">
                <form class="bookDetails__subscription-form">
                    <div class="bookDetails__subscription-form__group">
                        <label for="subscriptionId">Subscription</label>
                        <select id="subscriptionId" name="subscriptionId">
                            <option value="">Select </option>
                            <option value="${subscriptionMst.duration1}-${subscriptionMst.price1.intValue()}" <%=params.subsDuration!=null&&params.subsDuration.equals(""+subscriptionMst.duration1)?"selected":""%>>Rs.${subscriptionMst.price1.intValue()} for ${subscriptionMst.duration1} year</option>
                            <%if(subscriptionMst.price2!=null){%>
                            <option value="${subscriptionMst.duration2}-${subscriptionMst.price2.intValue()}" <%=params.subsDuration!=null&&params.subsDuration.equals(""+subscriptionMst.duration2)?"selected":""%>>Rs.${subscriptionMst.price2.intValue()} for ${subscriptionMst.duration2} years</option>
                            <%}%>
                            <%if(subscriptionMst.price3!=null){%>
                            <option value="${subscriptionMst.duration3}-${subscriptionMst.price3.intValue()}" <%=params.subsDuration!=null&&params.subsDuration.equals(""+subscriptionMst.duration3)?"selected":""%>>Rs.${subscriptionMst.price3.intValue()} for ${subscriptionMst.duration3} years</option>
                            <%}%>
                            <%if(subscriptionMst.price4!=null){%>
                            <option value="${subscriptionMst.duration4}-${subscriptionMst.price4.intValue()}" <%=params.subsDuration!=null&&params.subsDuration.equals(""+subscriptionMst.duration4)?"selected":""%>>Rs.${subscriptionMst.price4.intValue()} for ${subscriptionMst.duration4} years</option>
                            <%}%>
                            <%if(subscriptionMst.price5!=null){%>
                            <option value="${subscriptionMst.duration5}-${subscriptionMst.price5.intValue()}" <%=params.subsDuration!=null&&params.subsDuration.equals(""+subscriptionMst.duration5)?"selected":""%>>Rs.${subscriptionMst.price5.intValue()} for ${subscriptionMst.duration5} years</option>
                            <%}%>
                        </select>
                    </div>
                    <div class="bookDetails__subscription-form__group">
                        <label for="subscriptionMonthBookId">Select month</label>
                        <select  id="subscriptionMonthBookId" name="subscriptionMonthBookId">
                            <option value="">Choose starting month</option>
                            <% subsReleasedVersions.each {subs ->%>
                            <option value="${subs.id}" <%=params.subsStartingBookId!=null&&params.subsStartingBookId.equals(""+subs.id)?"selected":""%>>${subs.month_published+" "+subs.year_published}</option>
                            <%}%>
                        </select>
                    </div>
                    <div class="bookDetails__subscription-form__group">
                        <sec:ifLoggedIn>
                            <button class="addSubscription" type="button" onclick="subscribeNow()">Add to cart</button>
                        </sec:ifLoggedIn>
                        <sec:ifNotLoggedIn>
                            <button class="addSubscription" type="button" onclick="signupModal()">Add to cart</button>
                        </sec:ifNotLoggedIn>
                        <p class="text-danger mt-1 d-none" id="subDurErr">Please select subscription duration.</p>
                        <p class="text-danger mt-1 d-none" id="subMonErr">Please select starting month..</p>
                    </div>
                </form>
            </div>
            <%}%>
            <!---- PAPERBACK PRICE SECTION ---->
            <%if("1".equals(""+session["siteId"]) || "true".equals(session["prepjoySite"])){%>
            <div class="bookDetails__paperback">
                <h3>
                    Paperback & Hardcover Prices
                    <span style="color: rgba(0,0,0,0.2);font-size: 18px;cursor: pointer" title="Click to Compare Price">
                        <i class="fa-solid fa-circle-info"></i>
                        <div class="customToolTip">
                            <p>Click to check Amazon prices</p>
                        </div>
                    </span>
                </h3>
                <div class="affiliationCards">
                    <div class='affiliationCards__card' id='amazonPriceCard'>
                        <p id='amazonPrice'><span style='font-size: 14px'>Loading...</span></p>
                        <img src='${assetPath(src: 'resource/amazonAff.svg')}' alt='Amazon' class='amazonLogo' width='50px' height='18px'>
                    </div>
                </div>
            </div>
            <%}%>
        </div>
        <!---- FEATURES SECTION ---->
        <%if("1".equals(""+session["siteId"]) || "true".equals(session["prepjoySite"])){%>
        <div class="bookDetails__features">
            <div class="featureIcons_dtl">
                <h4>Features</h4>
                <div class="featureIcons_dtl-icons">
                    <div class="featureIcons_dtl-icon">
                        <img src="${assetPath(src: 'resource/phone.png')}" alt="eBook" width="25px" height="25px">
                        <p>eBook</p>
                    </div>
                    <div class="featureIcons_dtl-icon">
                        <img src="${assetPath(src: 'resource/exam.png')}" alt="Mock Test" width="25px" height="25px">
                        <p>Mock Test</p>
                    </div>
                    <div class="featureIcons_dtl-icon">
                        <img src="${assetPath(src: 'resource/notespad.png')}" alt="Notes" width="25px" height="25px">
                        <p>Notes</p>
                    </div>
                    <div class="featureIcons_dtl-icon">
                        <img src="${assetPath(src: 'resource/flash-cards.png')}" alt="Flashcards" width="25px" height="25px">
                        <p>Flashcards</p>
                    </div>
                </div>
            </div>
            <div class="bookImp">
                <h4>Why eBooks are better?</h4>
                <div>
                    <ul>
                        <li>
                            <div>
                                <img src="${assetPath(src: 'resource/check.png')}" alt="Easy and anywhere access" width="18px" height="18px">
                                <p>Anytime/Anywhere access</p>
                            </div>
                        </li>
                        <li>
                            <div>
                                <img src="${assetPath(src: 'resource/check.png')}" alt="Less Price" width="18px" height="18px">
                                <p>Cost-effective</p>
                            </div>
                        </li>
                        <li>
                            <div>
                                <img src="${assetPath(src: 'resource/check.png')}" alt="Fast access" width="18px" height="18px">
                                <p>Immediate delivery</p>
                            </div>
                        </li>
                        <li>
                            <div>
                                <img src="${assetPath(src: 'resource/check.png')}" alt="Video classes" width="18px" height="18px">
                                <p>Add Videos</p>
                            </div>
                        </li>
                        <li>
                            <div>
                                <img src="${assetPath(src: 'resource/check.png')}" alt="Save Tree" width="18px" height="18px">
                                <p>Save Trees</p>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <%}%>
        <!---- DESCRIPTION SECTION ---->
        <div class="bookDetails__description">
            <div class="bookDetails__description-tab">
                <div class="tabWrapper">
                    <ul>
                        <% if (session['wileySite'] && showPrice){ %>
                            <li class="tab-item tabActive" id="descriptionTab" data-tabItem="description">Description</li>
                        <%} else if(session['wileySite'] && !showPrice){%>
                            <li class="tab-item" id="descriptionTab" data-tabItem="description">Description</li>
                            <li class="tab-item tabActive" id="tocTab" data-tabItem="toc">Table of Content</li>
                        <%}else{%>
                            <li class="tab-item tabActive" id="descriptionTab" data-tabItem="description">Description</li>
                            <li class="tab-item" id="tocTab" data-tabItem="toc">Table of Content</li>
                        <%}%>
                    </ul>
                    <label for="bookDescHidden"></label>
                    <input value="${booksMst.description}" id="bookDescHidden" class="form-control hidden">
                    <div class="tabContent" id="tabContent">

                    </div>
                </div>
            </div>
        </div>
        <%if("1".equals(""+session["siteId"]) || "true".equals(session["prepjoySite"])){%>
        <div id="reviewsMap" style="visibility: hidden"></div>
        <!---- REVIEWS SECTION ---->
        <div class="bookDetails__reviews" id="reviewsSection">
            <h4 id="reviewTitle">Wonderslate Reviews</h4>
            <div class="reviewCards">

            </div>
        </div>
        <%}%>
    </div>
</div>

<div id="relatedDoubts" class="books-list">
    <g:render template="/resources/relatedBooks"></g:render>
</div>



<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<a href="https://www.wonderslate.com/${booksMst.title.replace(' ','-')}/ebook-details?${request.getQueryString()}" title="internal"> </a>
<%if(session["wileySite"]){%>
<g:render template="/resources/collectionBooks"></g:render>
<%}%>
<g:render template="/${session['entryController']}/footer_new"></g:render>
<script>
    const tabItem = document.querySelectorAll('.tab-item');
    const tabContent = document.getElementById('tabContent');
    const bookVariants  = document.getElementById('bookVariants');
    const addToCartBtn  = document.querySelector('.addToCartBtn');
    const bookID = '${booksMst.id}';
    const siteID = '${session["siteId"]}';
    const isBookPublished = '${booksMst.status}';
    const actualBookType = '${booksMst.bookType}';

    const canSell = '${canSell}';
    let selectedTab = "";
    let  bookVariantHTML = "";
    let selectedBookType = "";
    let wileyCollection = false;
    let isStockAvailable = true;
    let isPriceValid = false;
    let priceListArr = [];
    let openFreeBook = false;
    const encodedBookTitle=replaceAll("${booksMst.title}",' ','-');
    var colors=['#2EBAC6','#0D5FCE','#6FCF97','#F2C94C','#C20232','#FC7753','#E40039','#1abc9c','#FD7272','#55E6C1','#17c0eb'];
    const randomColorNumber = Math.floor(Math.random() * 11);
    <% if(booksMst.coverImage==null){%>
        document.querySelector('.unknownCoverImg').style.background = colors[randomColorNumber]
    <%}%>
    <%
    String bookType
    String finalPerVal
    String bookPrice=""
    String listPrice=""
    boolean isAiBook = false
    bookPriceDtls.each{ bookPriceDtl ->
        isAiBook = false
        if(("printbook".equals(bookPriceDtl.bookType)||"combo".equals(bookPriceDtl.bookType))&&!sellPrintBook||!showPrice){
            if(!showPrice){ %>
                wileyCollection = true;
                bookVariantHTML +="<div class='bookDetails__info-dtls__price-card variant activeCart' data-type='eBook'>"+
                    "<p class='text-danger'>This eBook is part of the collection.</p>"+
                    "</div>";
                document.querySelector('.addToCartBtn').innerHTML  = 'Show Collection';
                bookVariants.innerHTML = bookVariantHTML;
            <% }%>
        <%} else {%>
            <% if(!"upgrade".equals(bookPriceDtl.bookType)) {
                switch (bookPriceDtl.bookType){
                    case "eBook" :
                        bookType = "eBook"
                        break
                    case "testSeries" :
                        bookType = "Online Test Series"
                        break
                    case "upgrade" :
                        bookType = "Test series upgrade"
                        break
                    case "printbook" :
                        bookType = "Paperback"
                        break
                    case "combo" :
                        bookType = "Paperback + eBook"
                        break
                    case "ebookupgrade" :
                        bookType = "Extend validity"
                        break
                    case "bookGPT" :
                        bookType = "AI Book"
                        break
                    default:
                        bookType = "eBook"
                        break
                }

                if("eBook".equals(bookType)&&"ebookwithai".equals(booksMst.bookType)){
                    bookType = "eBook (with AI Doubts Solver)"
                    isAiBook = true
                }
                Float offerPrice = bookPriceDtl.sellPrice
                Float actualPrice = bookPriceDtl.listPrice!=null?bookPriceDtl.listPrice:bookPriceDtl.sellPrice;
                Double calculatedVal = actualPrice - offerPrice
                Double percentageVal = calculatedVal * 100 / actualPrice
                if(offerPrice>100) finalPerVal="Rs."+String.format("%.0f",calculatedVal)
                else finalPerVal=String.format("%.0f",percentageVal) +" %"
                if (offerPrice % 1 != 0) {
                    bookPrice=String.format("%.2f",offerPrice)
                } else {
                    bookPrice=String.format("%.0f",offerPrice)
                }
                if (actualPrice % 1 != 0) {
                    listPrice=String.format("%.2f",actualPrice)
                } else {
                    listPrice=String.format("%.0f",actualPrice)
                }%>

            priceListArr.push({bookType:'${bookPriceDtl.bookType}',price:'${bookPriceDtl.sellPrice}'});

            <% if(("printbook".equals(bookPriceDtl.bookType)||"combo".equals(bookPriceDtl.bookType))&&booksMst.currentStock!=null&&booksMst.currentStock.intValue()<1) {%>
                isStockAvailable = false;
            <%}%>

            bookVariantHTML +="<div class='bookDetails__info-dtls__price-card variant' data-type='${bookPriceDtl.bookType}'>";
            bookVariantHTML +="<div class='bookType'>"+
                "<p>${bookType}</p>";
            if("eBook" === "${bookType}" || "Online test series" === "${bookType}" || "bookGPT" == "${bookType}") {
                bookVariantHTML += "<img src='/assets/wonderslate/ebook.svg' alt='${booksMst.title}' width='20px' height='20px'>";
            }else if("Paperback" === "${bookType}"){
                bookVariantHTML += "<img src='/assets/resource/paperback.svg' alt='${booksMst.title}' width='20px' height='20px'>";
            }else  if ("Paperback + eBook"=== "${bookType}"){
                bookVariantHTML += "<img src='/assets/wonderslate/combo.svg' class='comboIcon' alt='${booksMst.title}' width='20px' height='20px'>";
            }
            bookVariantHTML += "</div>"+
                "<div class='bookPrice'>";
            if(('${bookType}' === 'Paperback' && isStockAvailable) || ('${bookType}' !== 'Paperback')){
                <% if(bookPriceDtl.listPrice != bookPriceDtl.sellPrice) {%>
                bookVariantHTML += "<p class='bookPrice__original'><span class='rupee-symbol'>&#x20b9</span>${listPrice}</p>";
                <%}%>
                <% if(!("0".equals(""+bookPriceDtl.sellPrice)||"0.0".equals(""+bookPriceDtl.sellPrice))) {%>
                bookVariantHTML += "<p class='bookPrice__offer'>" +
                    "<span class='rupee-symbol'>&#x20b9</span>"+
                    "${bookPrice}</p>";
                <%}else{%>
                bookVariantHTML += "<p class='bookPrice__offer text-success'><span class='rupee-symbol'>Free</p>";
                <%}%>

            }else if(('${bookType}' === 'Paperback' && !isStockAvailable) ){
                bookVariantHTML += "<p class='bookPrice__offer text-danger'><span class='rupee-symbol' style='font-size: 16px;font-weight: 400'>Out of Stock</p>";
            }

            bookVariantHTML +="</div>";
            <% if(bookPriceDtl.listPrice != bookPriceDtl.sellPrice) {%>
                <% if("${finalPerVal}" > 0){%>
                    <% if(!("0".equals(""+bookPriceDtl.sellPrice)||"0.0".equals(""+bookPriceDtl.sellPrice))) {%>
                            if(('${bookType}' === 'Paperback' && isStockAvailable) || ('${bookType}' !== 'Paperback')){
                                bookVariantHTML +="<div class='saving'>"+
                                    "<p class='saving__price'>Save ${finalPerVal}</p>";
                            }
                    <%}%>
                <%}%>
            <%}%>
            <% if("365".equals(""+booksMst.validityDays) && (!("printbook".equals(bookPriceDtl.bookType) || "combo".equals(bookPriceDtl.bookType)))) {%>
            bookVariantHTML +="<p class='saving__price'>for 1 year</p>";
            <%}else if(bookExpiry!=null&&!"".equals(bookExpiry)&&(!("printbook".equals(bookPriceDtl.bookType)||"combo".equals(bookPriceDtl.bookType)))){%>
            <% if(session['wileySite']) {%>
            bookVariantHTML +="<p class='saving__price'>Access valid till ${bookExpiry}</b></p>";
            <%}else{%>
            bookVariantHTML +="<p class='saving__price'>Valid till ${bookExpiry}</b></p>";
            <%}%>
            <%}
             if(isAiBook){%>

    bookVariantHTML +="<p><b>** 10 Doubt Tokens free</b></p>";
            <%}%>
            bookVariantHTML +="</div>"+
                "</div>";
            bookVariants.innerHTML = bookVariantHTML;
        <%}%>
    <%}%>
    <%} %>
    const variants = document.querySelectorAll('.variant');
    let  typesArr = []
    for(let v=0;v<variants.length;v++){
        typesArr.unshift(variants[v].getAttribute('data-type'))
    }
    if(variants.length>1){
        let printPresent = typesArr.includes('printbook');
        let testSeriesPresent = typesArr.includes('testSeries')
        if (testSeriesPresent){
            document.querySelector('[data-type="testSeries"]').classList.add('activeCart')
            if(printPresent && !isStockAvailable){
                document.querySelector('[data-type="printbook"]').setAttribute('style','cursor:not-allowed;')
            }
        }else if(printPresent && !testSeriesPresent && isStockAvailable){
            document.querySelector('[data-type="printbook"]').classList.add('activeCart')
        }else if(printPresent && !testSeriesPresent && !isStockAvailable || !printPresent && !testSeriesPresent){
            document.querySelector('[data-type="eBook"]').classList.add('activeCart')
            if(printPresent){
                document.querySelector('[data-type="printbook"]').setAttribute('style','cursor:not-allowed;')
            }
        }
        if (variants.length===3){
            if(isMobileScreen()) {
                variants[2].setAttribute('style','grid-column-start: 1;grid-column-end: 3;')
            }
        }
        addToCartBtn.style.display = 'flex';
    }else if(variants.length<=1){
        let printPresent = typesArr.includes('printbook');
        let ebookPresent = typesArr.includes('eBook');
        let testSeriesPresent = typesArr.includes('testSeries')
        if (printPresent && isStockAvailable){
            document.querySelector('[data-type="printbook"]').classList.add('activeCart')
            addToCartBtn.style.display = 'flex';
        }else if (printPresent && !isStockAvailable){
            if(isMobileScreen()) {
                document.querySelector('.bookDetails__info-dtls__price').style.bottom = '0';
            }
            document.querySelector('[data-type="printbook"]').setAttribute('style','cursor:not-allowed;')
            addToCartBtn.remove();
        }else{
            variants[0].classList.add('activeCart')
            addToCartBtn.style.display = 'flex';
        }
    }
    variants.forEach((variant,index)=>{
        if (variant.classList.contains('activeCart')){
            selectedBookType = variant.getAttribute('data-type');
        }
        variant.addEventListener('click',()=>{
            openFreeBook = false;
            let tempSelectedBookType = selectedBookType
            selectedBookType = variant.getAttribute('data-type');
            if ((selectedBookType!=='printbook' && isStockAvailable) || (selectedBookType==='printbook' && isStockAvailable) || (selectedBookType!=='printbook' && !isStockAvailable)){
                variants.forEach(item=>item.classList.remove('activeCart'));
                variant.classList.add('activeCart');
                updateAddToCartButton(selectedBookType)
            }else{
                selectedBookType=tempSelectedBookType;
            }
        });
    });
    function updateAddToCartButton(bookType){
        console.log("this is not called normally="+actualBookType);
        if (isBookPublished === 'published'){
            if (actualBookType!=='print'){
                 if (canSell=='true'){
                    priceListArr.forEach(price=>{
                        if (price.bookType === bookType){
                            if (price.price === '0'|| price.price === '0.0'){
                                if (userLoggedIn){
                                    openFreeBook = true;
                                    addToCartBtn.textContent = 'Open';
                                }else{
                                    openFreeBook = true;
                                    addToCartBtn.textContent = 'Get this Book';
                                }
                            }else{
                                addToCartBtn.textContent = 'Add to Cart';
                            }
                        }
                    })
                }
            }else{
                openAffliatePage('${booksMst.buylink1}','${booksMst.id}');
            }
        }else{
            addToCartBtn.setAttribute('disabled','disabled');
            addToCartBtn.textContent = 'Book Unavailable'
        }
    }
    addToCartBtn.addEventListener('click',()=>{
        if (!wileyCollection && !openFreeBook){
            hideAndShowLoader('show');
            addToCartFromDtl(bookID,selectedBookType);
        }else if(!wileyCollection && openFreeBook){
            if(userLoggedIn){
                encodeBookTitle();
            }else{
                openRegister('free','${booksMst.title}','${booksMst.id}');
            }
        }else{
            const title = "${wileyCollectionBookTitle}".replaceAll(' ','-').replaceAll('&','-');
            const colBookURL = '/'+title+'/ebook-details?siteName=${session["siteName"]}&bookId=${wileyCollectionBookId}'
            setTimeout(()=>{
                window.open(colBookURL,'_blank')
            },200)
        }
    })

    tabItem.forEach((tab)=>{
        tab.addEventListener('click',()=>{
            tabItem.forEach(all=>all.classList.remove('tabActive'));
            tab.classList.add('tabActive');
            selectedTab = tab.getAttribute('data-tabItem');
            if (selectedTab === 'toc'){
                updateTableOfContent();
            }else if(selectedTab === 'description'){
                updateBookDescription();
            }else{
                updateBookDescription();
            }
        })
    })

    function openBreadcrumbLink(level,syllabus,grade) {
        var paramLevel = replaceAll(level.replace('&', '~'),' ','-');
        var paramSyllabus = replaceAll(syllabus.replace('&', '~'),' ','-');
        var paramGrade = replaceAll(grade.replace('&', '~'),' ','-');

        <%if("true".equals(""+session["commonWhiteLabel"])){%>
        if(grade!="" && grade!=null) {
            window.location.href = "/sp/${session["siteName"]}/store?level="+paramLevel+"&syllabus="+paramSyllabus+"&grade="+paramGrade;
        } else if(syllabus!="" && syllabus!=null) {
            window.location.href = "/sp/${session["siteName"]}/store?level="+paramLevel+"&syllabus="+paramSyllabus;
        } else {
            window.location.href = "/sp/${session["siteName"]}/store?level="+paramLevel;
        }
        <%}else if (!"true".equals(session['prepjoySite'])){%>
        if(grade!="" && grade!=null) {
            window.location.href = "/${session["entryController"]}/store?level="+paramLevel+"&syllabus="+paramSyllabus+"&grade="+paramGrade;
        } else if(syllabus!="" && syllabus!=null) {
            window.location.href = "/${session["entryController"]}/store?level="+paramLevel+"&syllabus="+paramSyllabus;
        } else {
            window.location.href = "/${session["entryController"]}/store?level="+paramLevel;
        }
        <%} else {%>
        if(grade!="" && grade!=null) {
            window.location.href = "/"+siteName+"/eBooks?level="+paramLevel+"&syllabus="+paramSyllabus+"&grade="+paramGrade;
        } else if(syllabus!="" && syllabus!=null) {
            window.location.href = "/"+siteName+"/eBooks?level="+paramLevel+"&syllabus="+paramSyllabus;
        } else {
            window.location.href = "/"+siteName+"/eBooks?level="+paramLevel;
        }
        <%}%>
    }

    function updateBookDescription(){
        let description = document.getElementById('bookDescHidden').value;
        if(description!==null && description!=="" && description!==undefined && description!=="null"){
            tabContent.innerHTML = description;
        }else{
            tabContent.innerHTML = "<p>No Description Available.</p>";
        }
    }

    function updateTableOfContent(){
        let chapterListHTML = "<ol style='padding-left: 30px;'>";
        const chapters = "${chapters}";
        if (chapters){
            const chaptersList = JSON.parse((("${chapters}").replace(/&#92;/g,'\\')).replace(/&quot;/g,'"'));
            chaptersList.forEach(chapter=>{
                chapterListHTML +="<li>"+chapter.name+"</li>"
            });
            chapterListHTML+="</ol>";
        }else{
            chapterListHTML = "<p>No Table of Content is available.</p>";
        }
        tabContent.innerHTML = chapterListHTML;
    }


    function openAffliatePage(bookId,linkType,productLink){
        <g:remoteFunction controller="log" action="createAffLog" params="'bookId='+bookId+'&bookType=eBook&source=web&siteId=${session["siteId"]}&linkType='+linkType+'&productLink='+productLink"/>
        window.open(productLink, '_blank');
    }

    <%if("1".equals(""+session["siteId"]) || "true".equals(session["prepjoySite"])){%>
    function getAffiliationPrices(){
        <g:remoteFunction controller="affiliation" action="getAffiliationPrices"  onSuccess='updateAffiliationPrice(data);'
            params="'bookId=${booksMst.id}&bookType=eBook'" />
    }
    <%}%>
    function updateAffiliationPrice(data){
        if (data.status!=="OK"){
            return;
        }
        const amazonPriceText = document.getElementById('amazonPrice');

        const amazonPrice = data.amazonPrice;
        let amazonLink = data.amazonLink;

        if((amazonPrice!=null && amazonLink !=null) && (!amazonPrice==""&&!amazonPrice=="0")){
            amazonPriceText.innerHTML = "<span class='rupee-symbol'>&#x20b9</span> <span >"+amazonPrice+"</span>"
        }else if (amazonLink!=null && amazonPrice==null){
            amazonPriceText.innerHTML = "<span style='font-size: 14px'>Check Price</span>";
        }else if(amazonLink==null){
            amazonPriceText.innerHTML = "<span style='font-size: 14px'>Check Price</span>"
            const affBookTitle = '${booksMst.title}'.replaceAll(' ','+');
            amazonLink =  'https://www.amazon.in/s?k='+affBookTitle+'&tag=wonderslate-21&linkCode=w13';
        }



        if(document.getElementById('amazonPriceCard')){
            const amazonPriceCard = document.getElementById('amazonPriceCard');
            amazonPriceCard.addEventListener('click',()=>{
                <g:remoteFunction controller="log" action="createAffLog" params="'bookId='+bookID+'&bookType=eBook&source=web&siteId=${session["siteId"]}&linkType=Amazon&productLink='+amazonLink"/>
                window.open(amazonLink, '_blank');
            })
        }

    }

    function updateReviews(){
        let reviewHTML="";
        let reviewsData="";
        const reviewCards = document.querySelector('.reviewCards');
        const reviewsDataWS = [
            {
                userName:"Kanishk Khosla",
                review:"This app is really very helpful for everyone. You can make notes , flash cards every thing in this app. Really very helpful ❤️ ✨👏"
            },
            {
                userName:"Sofi Emaan",
                review:"The best app..its features like flash cards ,current affairs ,e- books all are really nice it is users friendly"
            },
            {
                userName:"Meenakshi krishnamurthy",
                review:"Wonderslate app is very useful and handy to find textbooks and its lessons in the app . I liked the app Very much."
            },
            {
                userName:"Jayanthi Kamtam",
                review:"Really the best and easy app recommended for each and every student And even teachers for conducting tests in these online classes"
            },
            {
                userName:"Sonali Vardhan",
                review:"This app is very good and helpful for everyone it has many features like mc ques , flashcard , notes like other study app I appreciate wonderslate team. 👏👏👌👌❤️🙏"
            },
        ]
        const reviewsDataPrepjoy = [
            {
                userName:"Gopal Nayar",
                review:"Over powerful experience Thank you for providing us such a interesting and knowledgeable application thankyou so much sir."
            },
            {
                userName:"Rouf Bhat",
                review:"My long search for a current affairs app for my neice just came to an end with Prepjoy. It is a whole package, easy to use, with interesting segments according to one's taste."
            },
            {
                userName:"Ganesh",
                review:"I have found one of the best app ever....for current affairs...it is like a boon for those who can't afford paid version......really very very superb work for underpriviliged students......."
            },
            {
                userName:"Sofi Emaan",
                review:"Gaming App wow!! Best Ever app for current affairs all in one i can watch it, read it, and then play it. which is the best feature of this app and its sound is so attractive & helps in avoiding boringness."
            },
            {
                userName:"Pramesh Kanase",
                review:"Overall nice experience with the application.Very good current affairs section on the go in one platform."
            },
        ]
        if (siteID==='1'){
            reviewsData  = reviewsDataWS;
        }else if (siteID==='27'){
            reviewsData  = reviewsDataPrepjoy;
            document.getElementById('reviewTitle').innerHTML = 'Prepjoy Reviews';
        }

        reviewsData.forEach(review=>{
            reviewHTML+= "<div class='reviewCard'>" +
                "<div class='reviewCard__img'>" +
                "<div class='reviewCard__img-wrapper'>"+
                "<img src='${assetPath(src: 'resource/userIcon.png')}' alt='user' width='20px' height='20px'>" +
                "</div>"+
                "</div>" +
                "<div class='reviewCard__content'>" +
                "<p><strong>"+review.userName+"</strong></p>" +
                "<p>" +review.review+"</p>" +
                "</div>" +
                "</div>";
        })
        reviewCards.innerHTML = reviewHTML;
    }


    function encodeBookTitle(){
        var bookLink = "/library/"+encodedBookTitle+"?";
        if("${mainResourcesPage}" == "true") {
            bookLink = "/"+encodedBookTitle+"/ebook?";
        }
        if((!("print"===(""+"${booksMst.bookType}"))) ){
            if(!("0"===(""+"${eBookPrice}")||"0.0"===(""+"${eBookPrice}")))  {
            } else {
                const freeBookLink = bookLink+"siteName="+siteName+'&bookId='+'${booksMst.id}';
                window.open(freeBookLink,'_blank');
            }
        }
    }

    <%if(subscriptionMst!=null){%>
    function subscribeNow(price){
        if(document.getElementById("subscriptionId").selectedIndex==0){
            document.getElementById('subDurErr').classList.remove('d-none');
            setTimeout(function (){
                document.getElementById('subDurErr').classList.add('d-none');
            },1500)
            document.getElementById("subscriptionId").focus();
        }
        else if(document.getElementById("subscriptionMonthBookId").selectedIndex==0){
            document.getElementById('subMonErr').classList.remove('d-none');
            setTimeout(function (){
                document.getElementById('subMonErr').classList.add('d-none');
            },1500)
            document.getElementById("subscriptionMonthBookId").focus();
        }else{
            var subscriptionDurationStr = document.getElementById("subscriptionId")[document.getElementById("subscriptionId").selectedIndex].value.split("-");
            var duration = subscriptionDurationStr[0];
            var price = subscriptionDurationStr[1];

            var subsStartingBookId = document.getElementById("subscriptionMonthBookId")[document.getElementById("subscriptionMonthBookId").selectedIndex].value;
            addSubscriptionToCartFromDtl('${booksMst.id}',${subscriptionMst.id},subsStartingBookId,duration);
        }
    }
    <%}%>
    function hideAndShowLoader(action){
        if (action=='show'){
            document.querySelector('.loading-icon').classList.remove('hidden');
        }else if(action=='hide'){
            document.querySelector('.loading-icon').classList.add('hidden');
        }
    }
    let jsonDesc = document.getElementById('bookDescHidden').value;
    if (jsonDesc){
        jsonDesc = jsonDesc.replace('<p>','').replace('</p>','');
        jsonDesc = jsonDesc.replace('<strong>','').replace('</strong>','');
    }
    var scriptElement = document.createElement('script');
    scriptElement.type = 'application/ld+json';
    scriptElement.text = '{'+
        '"@context": "https://schema.org/",'+
        '"@type": "Book",'+
        '"name": "${booksMst.title}",'+
        '"image": "${bookCoverImage}",'+
        '"description":"'+jsonDesc+'"'+
        '"isbn": "${booksMst.isbn}",'+
        '"offers": {'+
        '"@type": "AggregateOffer",'+
        ' "url": "${request.getRequestURL()}",'+
        '"priceCurrency": "INR",'+
        <%if(eBookPrice){%>
        '"lowPrice": "${eBookPrice}",'+
        '"highPrice": "${eBookListPrice}"'+
        <%}%>
        '  },'+
        ' "inLanguage": {'+
        ' "@type": "Language",'+
        ' "name": ['+
        '"${booksMst.language!=null?booksMst.language:"English"}"'+
        ']'+
        '},'+
        '"educationalUse": "Study Materials",'+
        '"publisher": "${publisherName}"'+
        '}';
    document.head.appendChild(scriptElement);
    updateAddToCartButton(selectedBookType);
    <%if("1".equals(""+session["siteId"]) || "true".equals(session["prepjoySite"])){%>
        getAffiliationPrices();
        updateReviews();
    <%}%>
    <%if(session['wileySite'] && !showPrice){%>
        updateTableOfContent();
    <%}else{%>
        updateBookDescription();
    <%}%>
    document.addEventListener('DOMContentLoaded',function (){
        let pageURL = "https://wa.me/918088443860?text="+encodeURIComponent("Hello ")+window.location.href;
        document.getElementById('whatsappLink').setAttribute('href',pageURL)
    })
    function isMobileScreen() {
        return window.innerWidth <= 768;
    }
    <%if("true".equals(params.addToCart)){%>
    addToCartFromDtl('${booksMst.id}','${params.bookType}');
    <%}%>
</script>
<g:render template="/wsshop/cartScripts"></g:render>
<g:render template="/wsshop/searchScripts"></g:render>
