<asset:stylesheet href="wonderslate/flashcard.css" async="true"/>
<asset:stylesheet href="wonderslate/flashcardHome.css" async="true"/>
<asset:javascript src="sharer.min.js"/>

<div class="modal fade" id="publicModal" data-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-sm">
        <div class="modal-content">
            <div class="loaders">
                <div class="bar-line"></div>
            </div>
            <!-- Modal Header -->
            <div class="modal-header">

            </div>

            <!-- Modal body -->
            <div class="modal-body">
                <p>This will make your flashcards visible to everybody. Are you sure to continue?</p>
            </div>

            <!-- Modal footer -->
            <div class="modal-footer justify-content-center">
                <button type="button" onclick="javascript:cancelPublic();" class="btn cancel-btn">Cancel</button>
                <button type="button" onclick="javascript:makePublic();" class="btn close-btn">Set Public</button>
            </div>

        </div>
    </div>
</div>

<div class="modal fade" id="deleteModal" data-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-sm">
        <div class="modal-content">
            <div class="loaders">
                <div class="bar-line"></div>
            </div>
            <!-- Modal Header -->
            <div class="modal-header">

            </div>

            <!-- Modal body -->
            <div class="modal-body">
                <p>Are you sure to Delete this card?</p>
            </div>

            <!-- Modal footer -->
            <div class="modal-footer justify-content-center">
                <button type="button" onclick="javascript:cancelDelete();" class="btn cancel-btn">Cancel</button>
                <button type="button" onclick="javascript:deleteConfirm();" class="btn close-btn">Delete</button>
            </div>

        </div>
    </div>
</div>

<div class="modal fade" id="no-user-modal">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">

            <!-- Modal body -->
            <div class="modal-body">
                <div id="no-user-modal-body">Please login to view the flashcard.</div>
            </div>

            <!-- Modal footer -->
            <div class="modal-footer">
                <button type="button" id="no-user-modal-close" class="btn btn-danger" data-dismiss="modal">Close</button>
            </div>

        </div>
    </div>
</div>

<input type="hidden" id="getTitle">
<g:render template="/resources/shareContent"></g:render>
<g:render template="/resources/flashcardMatch"></g:render>
<asset:javascript src="topic.js"/>
<script>
    <%if(params.resId!=null&&params.name!=null){%>
        reviseNow('${params.resId}','${params.name}');
    <%}else{%>
        reviseNow('','');
    <%}%>
    var newStudySet=false;
    var flashTemplate="";
    var rotateSlider;
    var setId;
    var resourceName;
    var resname;
    var setName;
    var doneMode=false;
    var frontElement;
    var backElement;
    var source;
    var fromAppCreation=false;
    var flashCardId;
    var dailyTest=false;
    var prepjoySite = "${session['prepjoySite']}";

    //create card first time;
    function createNewSet(quoteVal) {
        document.getElementById('htmlreadingcontent').innerHTML="";
        newStudySet = true;
        flashCardEmptyTemplate(quoteVal);
        $('.nav-tabs.add-tabs a[href="#all"]').tab('show');
        if(previousChapterId!=-1&&!fromAppCreation){
            $('html, body').animate({scrollTop: '0px'}, 300);
              $('#footer-menu-popover').modal('hide');
        }
    }



    //create Empty template in existing card..
    function flashCardEmptyTemplate(quoteVal) {
        document.getElementById('htmlreadingcontent').style.display='block';
        var isEmpty = document.getElementById('htmlreadingcontent').innerHTML == "";
        var flashEmptyTemplate;
        if(isEmpty){
            newStudySet=true;
            studySetResId=-1;
            flashEmptyTemplate= "<div id='slider-wrapper' style='display:none;'></div>"+
                "<div class=\"col-12 col-lg-12 main-wrapper mr-lg-4 ml-lg-4 mt-4\">\n"+
                " <div class=\"form-group d-flex flex-wrap flex-lg-nowrap pl-lg-3 pr-lg-3\">\n" +
                "            <div class=\"d-flex setname-wrapper ws-input col-12 col-lg-6\">\n" +
                // "                <button class=\"back-flashCard\" onclick=\"javascript:backToMain();\"><i class=\"material-icons\">arrow_back</i> </button>\n" +
                "                <input type=\"text\" class=\"form-controls\" placeholder=\"'\" id=\"revisionTitle\" value='My Flashcard'>\n" +
                "<label class='ws-label' for=\"revisionTitle\">Name of Flashcard set  </label>"+
                "            </div>\n" +
                "<div class='col-12 col-lg-6 no-padding'>"+
                "<div class=\"d-flex setname-wrapper ws-input\">"+
                "<input type=\"text\" class=\"form-controls\" placeholder=\"'\" id=\"description\" maxlength = \"100\">\n" +
                "<label class='ws-label' for=\"description\">Description(Optional | Max 100Characters)</label>"+
                "</div>"+
                "</div>"+
                "</div>\n"+
                "<div class='col-12 col-lg-6 dots mt-4'>"+
                "<div class=\"card-box\" id='empty'>\n" +

                "<div class=\"cardHeader\">\n" +
                " <p>Front</p>\n" +
                "<button class='delete-card'><i class='material-icons'>delete</i></button>"+
                "                </div>\n" +
                "                <div class=\"revision-question\">\n" +
                "                    <form>\n" +
                "                        <textarea class=\"form-control\" placeholder=\"Front\" id=\"term\" maxlength='300'></textarea>\n" +
                "                    </form>\n" +
                "                </div>\n" +
                "                <button class=\"add-text mb-2\">Back</button>\n" +
                "                <div class=\"revision-answer\">\n" +
                "                   <textarea class=\"form-control mb-2\" placeholder=\"Back\" value='' id=\"definition\" maxlength='300'></textarea>\n" +
                "                </div>\n" +


                "            </div></div>\n" +
                "</div>";
            if(previousChapterId==-1) {
                flashEmptyTemplate += "<div class=\"col-12 col-lg-12 mt-3 submit-button text-center\">\n" +
                    "        <a href=\"javascript:window.history.back();\" class=\"btn cancel\">\n" +
                    "            <span>Cancel</span>\n" +
                    "        </a>\n";
            }
            else{
                flashEmptyTemplate += "<div class=\"col-12 col-lg-12 mt-3 submit-button text-center\">\n" +
                    "        <a href=\"javascript:closeResourceScreen();\" class=\"btn cancel\">\n" +
                    "            <span>Cancel</span>\n" +
                    "        </a>\n";
            }
            flashEmptyTemplate +="        <a href=\"javascript:addKeyValueCard();\" class=\"btn btn-default save-card\">\n" +
                "            <span>+ Add Card</span>\n" +
                "        </a>\n" +
                "    </div>";
            flashEmptyTemplate += "<div class='row col-12 col-lg-10 saveCard mt-4 justify-content-center'>"+
            "        <a href=\"javascript:done();\" class=\"btn btn-default saveSubmit\">\n" +
            "            <span>Save & Submit</span>\n" +
            "        </a>\n" +"</div>"+
            "</div>";
            document.getElementById('htmlreadingcontent').innerHTML += flashEmptyTemplate;
            document.getElementById('revisionTitle').focus();
            if(fromAppCreation) {
                <%if(("android".equals(session["appType"]))||("ios".equals(session["appType"]))){%>
                $('#book-read-material .hero-title i').css('display','none');
                document.getElementById('definition').value = quoteVal;
                <%}%>
            }
            <%if(("android".equals(session["appType"]))||("ios".equals(session["appType"]))){%>
            $('#book-read-material > .mobile-stocker').removeClass('mobile-stocker').removeClass('d-none').addClass('d-block');
            $('#htmlreadingcontent > .main-wrapper').addClass('reset-app').removeClass('mt-4');
            $('#book-read-material .hero-title i').css('margin-right','10px');
            <%}%>
        }
        else{
            flashEmptyTemplate =
                "<div class=\"col-12 col-lg-6 mt-4 dots\" id='empty'>\n"+
                "<div class=\"card-box\">\n" +
                "<div class=\"cardHeader\">\n" +
                " <p>Front</p>\n" +
                "<button class='delete-card'><i class='material-icons'>delete</i></button>"+
                "                </div>\n" +
                "                <div class=\"revision-question\">\n" +
                "                    <form>\n" +
                "                        <textarea class=\"form-control\" placeholder=\"Front\" id=\"term\" maxlength='300'></textarea>\n" +
                "                    </form>\n" +
                "                </div>\n" +
                "                <button class=\"add-text mb-2\">Back</button>\n" +
                "                <div class=\"revision-answer\">\n" +
                "                   <textarea class=\"form-control mb-2\" placeholder=\"Back\" value='' id=\"definition\" maxlength='300'></textarea>\n" +
                "                </div>\n" +


                "            </div>\n" +
                "        </div>";
            document.getElementById('my-cards').innerHTML += flashEmptyTemplate;
        }
        document.getElementById('content-data-all').style.display='none';
        $('#allAddButton').hide();
    }


    function addKeyValueCard() {
        var noOfItems=1;

        if(document.getElementById("revisionTitle").value==""){
            document.getElementById("revisionTitle").focus();
        } else if(document.getElementById("term").value=="") {
            document.getElementById("term").focus();
        } else if(document.getElementById("definition").value=="") {
            document.getElementById("definition").focus();
        } else {
            var title=encodeURIComponent(document.getElementById("revisionTitle").value);
            setName=document.getElementById("revisionTitle").value;
            var description = encodeURIComponent(document.getElementById("description").value);
            //  $('.loading-icon').removeClass('hidden');
            if ('${params.siteId}'!= undefined && '${params.siteId}'!=null && '${params.siteId}'!=""){
                const siteId = parseInt('${params.siteId}');
                var params = "noOfItems="+noOfItems+"&chapterId="+previousChapterId+"&studySetResId="+studySetResId+"&folderId=${params.folderId}&title="+title+"&description="+description+'&siteId='+siteId;
            }else{
                var params = "noOfItems="+noOfItems+"&chapterId="+previousChapterId+"&studySetResId="+studySetResId+"&folderId=${params.folderId}&title="+title+"&description="+description
            }


            for (i = 0; i < noOfItems;i++) {
                params += "&term"+i+"="+encodeURIComponent(document.getElementById("term").value);
                params += "&definition"+i+"="+encodeURIComponent(document.getElementById("definition").value);
            }

            if (!prepjoySite){
                $('.loading-icon').removeClass('hidden');
            }else{
                $('#loading').show();
            }
            <g:remoteFunction controller="resources" action="addKeyValues" onSuccess='keyValuesSaved(data);' params="params"></g:remoteFunction>
        }
    }

    function keyValuesSaved(data) {
        if (!prepjoySite){
            $('.loading-icon').addClass('hidden');
        }else{
            $('#loading').hide();
        }
        if(doneMode) {
            showCompletedModal();
        }
        else {
            getRevisionSet(data.resId);
            addRevisionToAllTab(data.title,data.resId,data.createdBy);
        }
        flashCardsPresent = true;

    }
    //Open Exisiting flashcard
    var setEditMode=false;
    function goToEditScreen(resId) {
        setEditMode=true;
        if(setEditMode){
            $('#successModal #msg-updated').html('Updated Successfully');
        }
        else{
            $('#successModal #msg-updated').html('Created Successfully');
        }
        setId=resId;
        document.getElementById('content-data-all').style.display='none';
        document.getElementById('htmlreadingcontent').style.display='block';
        //flashCardEmptyTemplate();
        getRevisionSet(resId);
        $('#allCards').hide();
        <%if("android".equals(session["appType"])){%>
            <%if("true".equals(params.prepjoyApp)){%>
                ReadJSInterface.flashCardLoaderEvent();
            <%}else{%>
                JSInterface.flashCardLoaderEvent();
            <%}%>
        <%}%>
        <%if("ios".equals(session["appType"])){%>
        webkit.messageHandlers.flashCardLoaderEvent.postMessage('');
        <%}%>

    }
    //get Existing Revision set

    function getRevisionSet(id) {
        <g:remoteFunction controller="funlearn" action="getFlashCards" onSuccess='displayRevisionSets(data);' params="'resId='+id"/>
    }

    //slider Wrapper
    function sliderTemplate() {
        var slideTemplate="";
        slideTemplate="<div id='slider-wrapper'></div>";
        document.getElementById('content-data-all').style.display='none';
        document.getElementById('htmlreadingcontent').style.display='block';
        document.getElementById('htmlreadingcontent').innerHTML +=slideTemplate;
    }


    //get slider data
    function reviseNow(resId,name) {
        //contentColumn();
        var id=resId;
        resname=name;
        newStudySet=false;
        source="book";
        if(previousChapterId==-1) source="FlashCardHome";
        if(resname==undefined){
            var titleName=document.getElementById('revisionTitle').value;
            resname=titleName;
        }
        resname=htmlDecode(resname)
        if(source==='book'){
            if($(window).width()<768) {
                $('.mobile-stocker,.mobile-footer-resource').removeClass('d-flex').addClass('d-none');
                $('.mobile-back-button').attr('onclick','closeResourceScreen()');
                $('html, body').animate({scrollTop: '0px'}, 300);
            }


        }

        sliderTemplate();
        if (!prepjoySite){
            $('.loading-icon').addClass('hidden');
        }else{
            $('#loading').hide();
        }
        if ('${params.dailyTestId}'!=null && '${params.dailyTestId}' !=""){
            <g:remoteFunction controller="funlearn" action="getFlashCards" onSuccess='displaySlider(data);' params="'dateInput=${params.dateInput}&dailyTestId=${params.dailyTestId}'"/>
        }
        else if('${params.retestMode}'!=null && '${params.retestMode}' !=""){
            <g:remoteFunction controller="prepjoy" action="getImproveQuestions" onSuccess='displaySlider(data);' params="'subject=${params.subject}'"/>
        }
        else if('${params.mode}' == 'retest'){
            <g:remoteFunction controller="prepjoy" action="getRetestDetailsForFlashcards" onSuccess='displaySlider(data);' params="'parentQuizRecId=${params.parentQuizRecId}&questionOptions=${params.questionOptions}'"/>
        }else if('${params.noOfQuestions}'!='' && '${params.noOfQuestions}'!=null && '${params.noOfQuestions}'){
            <g:remoteFunction controller="funlearn" action="getFlashCards" onSuccess='displaySlider(data);' params="'resId='+id+'&noOfQuestions='+${params.noOfQuestions}"/>
        }else{
            <g:remoteFunction controller="funlearn" action="getFlashCards" onSuccess='displaySlider(data);' params="'resId='+id"/>
        }

        <%if(params.resId!=null){%>
        <sec:ifNotLoggedIn>
        updateView(resId,source,"KeyValues");
        </sec:ifNotLoggedIn>
        <sec:ifLoggedIn>
        updateUserView(resId,source,"KeyValues");
        </sec:ifLoggedIn>
        <%}%>
        $('.generateTest').hide();


    }


    function statusSet(data){
        if(data.status=='ok') {
            $("#privacyLevel").disable = true;

            $('.public-text').text('Awaiting Approval');
            var clickval = $("#publicLevel").attr("onchange");
            $('#publicLevel').attr('onchange', clickval.replace('setPublic', 'awaitingApproval'));
            $('#publicModal').modal('hide');
            $('.slider-actions').append("<button onclick='javascript:shareFlashcard()' class='dropdown-toggle' data-toggle=\"dropdown\">" +
                "<i class='material-icons'>share</i><span>Share</span> " +
                "</button>");

        }
        else{
            $('#publicModal').modal('show');
        }
    }

    function alreadySetPublic(field){
        alert("Cannot change, once set to public");
        field.checked = true;
    }

    function awaitingApproval(field){
        alert("Awaiting approval to set this flashcard public.");
        field.checked = true;
    }

    var cardId;
    var keyValues;
    //Display slider
    function displaySlider(data){
        cardId=data.resId;
        flashCardId=data.resId;
        if ('${params.dailyTestId}'!=null){
            dailyTest=true;
            resname = data.resourceName;
        }
        if(!flashCardId && !dailyTest){
            $('#no-user-modal').modal('show');
            loginOpen();
            return;
        }
        getBestTime(data.resId);
        playMatch(data);
        $('.loading-icon').addClass('hidden');
        var colors=['#2EBAC6','#0D5FCE','#6FCF97','#F2C94C','#C20232','#FC7753','#E40039','#1abc9c','#FD7272','#55E6C1','#17c0eb'];
         keyValues=data.keyValues;


        var progressLength=100;
        var increaseLength=(100/(keyValues.length));

        var slider="";

        slider +="<div class='row'>" +

            "<div class='col-12 col-lg-10'> " +

            "<div class='d-flex flex-wrap flex-lg-nowrap align-items-center justify-content-center justify-content-lg-between mb-3 mt-md-4 p-lg-3 p-0'>" +
            "<div class='mb-3 mb-lg-0'>";
        <%if(("android".equals(session["appType"]))||("ios".equals(session["appType"]))){%>
        slider +="<div class='d-none mobile-stocker align-items-center justify-content-start px-2'>" ;
        <%}else{%>
        slider +="<div class='d-flex mobile-stocker align-items-center justify-content-start px-2'>" ;
        <%}%>

        if(previousChapterId==-1) {
            <%if(("android".equals(session["appType"]))||("ios".equals(session["appType"]))){%>
            if(fromQuiz) {
                slider += "<i class='material-icons backfromcard flex-fill d-none d-lg-block' onclick='backToQuiz();'>keyboard_backspace</i>";
            }
            else{
                slider += "<i class='material-icons backfromcard flex-fill d-lg-block' onclick='backFlashcardHome();'>keyboard_backspace</i>";
            }
            <%}else{%>
            slider += "<i class='material-icons backfromcard flex-fill' onclick='backFlashcardHome();' style='left: 0;font-size: 30px;'>keyboard_backspace</i>";
            <%}%>
            slider +="<h4 class='hero-title flex-fill'><span class='d-flex'> " +
                "<i class='material-icons d-none' onclick='javascript:backFlashcardHome();'>keyboard_backspace</i>" + resname + "</span></h4>";
        }else{
            slider += "<h4 class='hero-title flex-fill ml-5'>" +
                "<span class='d-flex'>";
            <%if(("android".equals(session["appType"]))||("ios".equals(session["appType"]))){%>
            slider +=  "<i class='material-icons' onclick='javascript:backToChapterList();'>keyboard_backspace</i>" + resname + "</span></h4>";
            <%}else{%>
            slider +=  "<i class='material-icons' onclick='javascript:closeResourceScreen();'>keyboard_backspace</i>" + resname + "</span></h4>";
            <%}%>
        }

        if(data.canEdit=="true"&&"public"==data.privacyLevel) {

            slider += "<div class=\"switch reset-switch toggleSwitch\"><span class='public-text'>Public</span>" +
                "<label class=\"switch\">\n" +
                "  <input type=\"checkbox\" checked readonly id='privacyLevel' onclick='javascript:alreadySetPublic(this);' '>\n" +
                "  <span class=\"slider round\"></span>\n" +
                "</label>" +
                "</div></label>";
        }else if(data.canEdit=="true"&&"awaiting_approval"==data.privacyLevel){
            slider += " <div class=\"switch reset-switch toggleSwitch\">" +
                "<span class='public-text'>Awaiting Approval</span>" +
                "<label class=\"switch\">\n" +
                "  <input type=\"checkbox\" checked readonly id='publicLevel' onchange='javascript:awaitingApproval(this);'>\n" +
                "  <span class=\"slider round\"></span>\n" +
                "</label>" +
                "</div>";

        }else if(data.canEdit=="true"&&"rejected"==data.privacyLevel){
            slider += " <div class=\"switch reset-switch toggleSwitch\">" +
                "<span class='public-text'>Cannot be made public</span>" +
                "<label class=\"switch\">\n" +
                "  <input type=\"checkbox\" disabled readonly id='publicLevel'>\n" +
                "  <span class=\"slider round\"></span>\n" +
                "</label>" +
                "</div>";

        }else if(data.canEdit=="true"){
            slider += "<div class=\"switch reset-switch toggleSwitch\"><span class='public-text'>Set as Public</span>" +
                "<label class=\"switch\">\n" +
                "  <input type=\"checkbox\" id='publicLevel' onchange='javascript:setPublic(this);'>\n" +
                "  <span class=\"slider round\"></span>\n" +
                "</label>" +
                "</div></label>";
        }
        slider +=  "</div>" +
            "</div>";

        slider += "<div class='ws-progressbar mt-2 mb-2 mb-lg-2 mt-lg-0'>" ;
        if(keyValues.length>1) {
            slider +=  "<div class='d-flex justify-content-between align-items-center mb-2'>" +
                "<button class='ws-previous' id='progress-prev'><i class='material-icons'>keyboard_backspace</i> Previous</button>" +
                "<p class='slider-roller'>" + "<span id='sliderIncrement'>1</span> of <span>" + keyValues.length + "</span>" + "</p>" +
                "<button class='ws-next' id='progress-next'> Next<i class='material-icons' style='transform: matrix(-1, 0, 0, 1, 0, 0);'>keyboard_backspace</i></button>" +
                "</div>" +
                "<div class=\"progress\">\n" +
                "    <div class=\"progress-bar\"  id='slider-progress'></div>\n" +
                "  </div>";
        }

        slider +=  "</div>";

        // if(data.canEdit=="true") {
        //     slider += "<div><button class='d-flex align-items-center edit' onclick='javascript:goToEditScreen(" + data.resId + ")'> <i class='material-icons'>edit</i> Edit</button> </div>" ;
        // }
        slider+="</div>";


        slider += "<div class=\"flashcards mt-0 mt-lg-4\">\n"+"<div id='shuffleAll'>";
        if(keyValues.length >=1) {
            for (var i = 0; i < keyValues.length; i++) {
                console.log(keyValues[i])
                console.log(keyValues[i].term.length)
                slider += "    <div class=\"flash-card\">\n" +
                    "        <div class=\"flip-box-inner\">\n" +
                    "            <div class=\"flip-box-front\">\n" +
                    "<p>" + keyValues[i].term + "</p>" ;
                if(keyValues[i].term.indexOf('</table>') > -1 || keyValues[i].term.match(/<img/) || keyValues[i].term.length > 400 ) {
                    slider += "<button type='button' class='btn btn-primary expand' onclick='expandModal(" + i + ",false)'>Expand</button>";
                }
                slider += "  </div>\n" +
                    "            <div class=\"flip-box-back\">\n" +
                    "<p>" + keyValues[i].definition + "</p>" ;
                if(keyValues[i].definition.indexOf('</table>') > -1 || keyValues[i].definition.match(/<img/) || keyValues[i].definition.length > 400) {
                    slider +=  "<button type='button' class='btn btn-primary expand' onclick='expandModal(" + i + ",true)'>Expand</button>";
                }
                slider += "            </div>\n" +
                    "        </div>\n" +
                    "    </div>\n";


            }
            slider += "</div>";
            slider += "    <button class=\"card-previous\" id=\"buttonPrev\">\n" +
                "        <i class=\"material-icons\">\n" +
                "            arrow_back\n" +
                "        </i>\n" +
                "    </button>\n" +
                "    <button class=\"card-next\" id=\"buttonNext\">\n" +
                "        <i class=\"material-icons\">\n" +
                "            arrow_forward\n" +
                "        </i>\n" +
                "    </button>\n" +
                "<button class='turn-slide' onclick='javascript:turnSlider()'><i class='material-icons'>loop</i> </button>" +
                "</div>\n" +
                "\n" +
                "<div>\n" +
                "    <p id=\"totalSlides\"></p>\n" +
                "    <span id=\"activeSlide\"></span>\n" +
                "</div>" +
                "</div>" +

                "<div class='col-12 col-lg-2 d-flex align-items-center'>" +
                "<div class='slider-actions'>" +
                "<button type='button' onclick='javascript:shuffleCards();'><i class='material-icons'>shuffle</i> <span>Shuffle</span></button>";

            if (data.canEdit == "true") {
                slider += "<button onclick='javascript:goToEditScreen(" + data.resId + ")'><i class='material-icons'>edit</i><span>Edit</span> </button>";
            }

            slider += "<button class='flipSlides' onclick='javascript:flipSlides()'><i class='material-icons'>flip_camera_android</i> <span>Flip Slides</span></button>" ;
            <%if(("android".equals(session["appType"]))||("ios".equals(session["appType"]))){%>
            <%}else{%>
                slider += "<button onclick='javascript:handlePlayMatchAction()'><i class='material-icons'>weekend</i> <span>Play Match</span></button>";
            <%}%>
            if (data.privacyLevel == "public") {
                <%if(("android".equals(session["appType"]))||("ios".equals(session["appType"]))){%>
                <%}else{%>
                slider += "<button onclick='javascript:shareFlashcard()' class='dropdown-toggle'><i class='material-icons'>share</i><span>Share</span> </button>";

                <%}%>
            }
            slider +=     "</div>" +
                "</div>" ;
            if(source=='FlashCardHome') {
                slider += "<div class='col-12 col-lg-10 w-100 text-center mt-3 down-slider'>" +
                    "<i class='material-icons iconStyle'>style</i>" +
                    "<i class=\"material-icons iconStyle d-block\">\n" +
                    "expand_more\n" +
                    "</i>" + "</div>";
            }
            slider +=  "</div>";
        }
        else{

            slider += "<div class='text-center'><img src=\"${assetPath(src: 'ws/boxempty.svg')}\"  width='125px' height='117px'>"+
                "<p class='flashcard-empty-msg' style='font-size:18px;'>Your set is empty</p>" ;
            if (data.canEdit == "true") {
                slider +="<button onclick='javascript:goToEditScreen(" + data.resId + ")' class='btn btn-flashcard mt-4' style='margin:0 auto;'><i class='material-icons'>edit</i><span>Add</span> </button></div>";
            }
        }
        if (!prepjoySite){
            $('.loading-icon').addClass('hidden');
        }else{
            $('#loading').hide();
        }
        document.getElementById('htmlreadingcontent').innerHTML=slider;
        if($('.flip-box-front,.flip-box-back').find('table').length || $('.flip-box-front,.flip-box-back').find('img').length){
            $('.flip-box-front,.flip-box-back').addClass('removeFlex');
        }
        else{
            $('.flip-box-front,.flip-box-back').removeClass('removeFlex');
        }
        <%if(("android".equals(session["appType"]))||("ios".equals(session["appType"]))){%>
        $('#htmlreadingcontent > .mobile-stocker').removeClass('mobile-stocker').addClass('set-app-slider');
        <%}%>
        if(keyValues.length >1) {
            document.getElementById('slider-progress').style.width = increaseLength + '%';
        }
        if(keyValues.length === 1){
            document.getElementById("buttonNext").style.display = 'none';
            document.getElementById("buttonPrev").style.display = 'none';
        }

        $('#slider-wrapper').show();
        $('.main-wrapper,.submit-buttons,#allAddButton').hide();
        var front = document.querySelectorAll(".flip-box-front");
        var back = document.querySelectorAll(".flip-box-back");
        rotateSlider=document.querySelectorAll('.flip-box-inner');
        for(var i=0 ; i < front.length; i++) {
            front[i].style.background = colors[i%11];
            // back[i].style.background = colors[i%11];
        }
        $('.flash-card').first().addClass('active');
        $('.flash-card').hide();
        $('.flash-card.active').show();
        if($('.flash-card').hasClass('active')){
            $('.flash-card.active').next('.flash-card').addClass('slide2');
            $('.flash-card.active').next().next('.flash-card').addClass('slide3');
        }
        $('#buttonNext,#progress-next').click(function(){
            document.getElementById("buttonPrev").disabled = false;
            document.getElementById("progress-prev").disabled = false;

            $('.flash-card.active').removeClass('active').addClass('oldActive');
            $('.flash-card').removeClass('slide2 slide3 slide-left slide-right');

            if ( $('.oldActive').is(':last-child')) {
                $('.flash-card').first().addClass('active');
            }
            else{
                $('.oldActive').next('.flash-card').addClass('active');
                $('.oldActive').next().next('.flash-card').addClass('slide2');
                $('.oldActive').next().next('.flash-card').next('.flash-card').addClass('slide3');
            }
            $('.oldActive').removeClass('oldActive').addClass('slide-left');
            $('.flash-card').fadeOut();
            $('.flash-card.active').fadeIn();
            if($('.flash-card:last').hasClass('active')){
                document.getElementById("buttonNext").disabled = true;
                document.getElementById("progress-next").disabled = true;
            }

            //slider-progress
            var currentWidth;
            currentWidth=document.getElementById('slider-progress').style.width;
            currentWidth = currentWidth.replace('%','');
            document.getElementById('slider-progress').style.width=(parseInt(currentWidth)+increaseLength).toString()+'%';
            var count = parseInt($('#sliderIncrement').text())+1;
            $('#sliderIncrement').text(count);
            if((keyValues.length > 2) && (count==keyValues.length)){
                $('.flash-card:first').addClass('slide2');
                $('.flash-card:nth-child(2)').addClass('slide3');
                $('.flash-card').removeClass('slide-left');
            }
            else if(keyValues.length==2){
                $('.flash-card:first').addClass('slide2');
                $('.flash-card').removeClass('slide-left');
            }
            else if(keyValues.length==1){
                $('#buttonNext,#progress-next').attr('disabled');
            }
        });
        $('#buttonPrev,#progress-prev').click(function(){
            document.getElementById("buttonNext").disabled = false;
            document.getElementById("progress-next").disabled = false;
            $('.flash-card.active').removeClass('active').addClass('oldActive');
            $('.flash-card').removeClass('slide2 slide3 slide-right slide-left');
            if ( $('.oldActive').is(':first-child')) {
                $('.flash-card').last().addClass('active');
            }
            else{
                $('.oldActive').prev('.flash-card').addClass('active').addClass('slide-right');;
                $('.oldActive').prev().prev('.flash-card').addClass('slide3');
                $('.oldActive').prev().prev('.flash-card').prev('.flash-card').addClass('slide2');
            }
            $('.oldActive').removeClass('oldActive');
            $('.flash-card').fadeOut();
            $('.flash-card.active').fadeIn();
            if($('.flash-card:first').hasClass('active')){
                document.getElementById("buttonPrev").disabled = true;
                document.getElementById("progress-prev").disabled = true;
            }
            //slider-progress
            var currentWidth;
            currentWidth=document.getElementById('slider-progress').style.width;
            currentWidth = currentWidth.replace('%','');
            document.getElementById('slider-progress').style.width=(parseInt(currentWidth)-increaseLength).toString()+'%';
            var count = parseInt($('#sliderIncrement').text())-1;
            $('#sliderIncrement').text(count);
            if((keyValues.length > 2) && (count==1)){
                $('.flash-card:last').addClass('slide3');
                $('.flash-card:last').prev('.flash-card').addClass('slide2');
                $('.flash-card').removeClass('slide-right');
            }
            else if(keyValues.length==2){
                $('.flash-card:last').addClass('slide2');
                $('.flash-card').removeClass('slide-left');
            }
            else if(keyValues.length==1){
                $('#buttonPrev,#progress-prev').attr('disabled');
            }
        });


        var allCards='';
        if(keyValues.length >=1) {
            allCards += "<div class='d-flex justify-content-between align-items-center col-12 col-lg-10 mt-2 mt-lg-4'>" +
                "<p class='info-text reset-color'>All Cards(" + keyValues.length + ")</p>" +
                " <button type=\"button\" onclick=\"printFlashcards('print-cards')\" class=\"d-none btn btn-flashcard print\">Print Cards<i class=\"material-icons\">print</i></button>"+

                "</div>";
        }
        allCards += "<div class=\"logotext\"></div>"+
            " <div class=\"d-flex flex-wrap col-12 col-lg-10 justify-content-center\" id='print-cards'>\n";


        for(var i=0;i<keyValues.length;i++){
            allCards +="<div class='flashcard-print row w-100 mt-3'> " +
                "<div class=\"col-12 col-lg-6 mt-3\">\n" +
                "                <div class=\"flashcard-set\" style='padding: 1rem;'>\n" +
                "                    <div class=\"title-wrapper d-flex align-items-center\">\n" +
                "                        <h4>"+keyValues[i].term+"</h4>\n" +
                "                    </div>\n" +
                "                </div>\n" +
                "            </div>\n"+
                "<div class=\"col-12 col-lg-6 mt-3\">\n" +
                "                <div class=\"flashcard-set\" style='padding: 1rem;'>\n" +
                "                    <div class=\"title-wrapper d-flex align-items-center\">\n" +
                "                        <h4>"+keyValues[i].definition +"</h4>\n" +
                "                    </div>\n" +
                "                </div>\n" +
                "            </div></div>\n";
        }
        allCards += "        </div>";
        if(previousChapterId==-1) document.getElementById('allCards').innerHTML=allCards;
        renderMathInElement(document.body);
        hideBackButton()
            if (showPubSlide) {
                $('#content-data-all').hide().addClass('d-none');
                showPubSlide = false;
            }

        if (window.location.href.includes('clickSource=myactivity')){
            $('.flashcards').css('margin-bottom','100px')
        }

        <%if("true".equals(params.prepjoyApp)){%>
            $('.mobile-stocker').removeClass('d-flex').addClass('d-none');
        <%}%>

    }

    function shareFlashcard(){
        var flashcardUrl = "https://wonderslate.page.link/?apn=com.wonderslate.wonderpublish&ibi=com.wonderslate.app&link="+encodeURIComponent(window.location.href);
        openShareContentModal('flashcard', flashcardUrl);
    }

    //Display Revision set

    function displayRevisionSets(data) {
        var keyValues=data.keyValues;
        flashCardId=data.resId;
        studySetResId = data.resId;
        resourceName=data.resourceName;
        var showSlideragain=String(resourceName);
        var displaySet="";
        if(setEditMode) {
            <%if(("android".equals(session["appType"]))||("ios".equals(session["appType"]))){%>
            displaySet +="   <div class='d-none align-items-center mobile-stocker edit-back'>" ;
            <%}else{%>
            displaySet +="   <div class='d-flex align-items-center mobile-stocker edit-back'>" ;
            <%}%>
            displaySet += " <a class=\"btn cancel \" href='javascript:reviseNow(" + data.resId + ",\"" + (data.resourceName).replace(/'/g,"&#39;") + "\")'>\n" +
                "            <span><i class='material-icons backfromcard d-lg-block' style='left: 0;font-size: 30px;'>keyboard_backspace</i> </span>\n" +
                "        </a>\n" +
                "<p class='mb-0'>Edit Sets</p>";
        displaySet += "</div>";
        }
        displaySet +=" <div class='col-12 col-lg-12 main-wrapper mr-lg-4 ml-lg-4 mt-4'> <div class=\"form-group d-flex flex-wrap flex-lg-nowrap col-12 pl-lg-3 pr-lg-3\">\n" +
            "            <div class=\"d-flex setname-wrapper ws-input col-12 col-lg-6\">\n" +
            // "                <button class=\"back-flashCard\" onclick=\"javascript:backToMain();\"><i class=\"material-icons\">arrow_back</i> </button>\n" +
            "                <input type=\"text\" class=\"form-controls\" placeholder=\"'\" id=\"revisionTitle\">\n" +
            "<label class='ws-label' for=\"revisionTitle\">Name of Flashcard set ";
        if(data.canEdit=="true"&&"public"==data.privacyLevel) {
            displaySet += " <div class=\"switch toggleSwitch\">" +
                "<span class='public-text'>Public</span>" +
                "<label class=\"switch\">\n" +
                "  <input type=\"checkbox\" checked readonly id='privacyLevel' onclick='javascript:alreadySetPublic(this);' '>\n" +
                "  <span class=\"slider round\"></span>\n" +
                "</label>" +
                "</div>";
        }else if(data.canEdit=="true"&&"awaiting_approval"==data.privacyLevel){
            displaySet += " <div class=\"switch toggleSwitch\">" +
                "<span class='public-text'>Awaiting Approval</span>" +
                "<label class=\"switch\">\n" +
                "  <input type=\"checkbox\" checked readonly id='publicLevel' onchange='javascript:awaitingApproval(this);'>\n" +
                "  <span class=\"slider round\"></span>\n" +
                "</label>" +
                "</div>";

        }else if(data.canEdit=="true"&&"rejected"==data.privacyLevel) {
            displaySet += " <div class=\"switch toggleSwitch\">" +
                "<span class='public-text'>Cannot be made public</span>" +
                "<label class=\"switch\">\n" +
                "  <input type=\"checkbox\" disabled readonly id='publicLevel'>\n" +
                "  <span class=\"slider round\"></span>\n" +
                "</label>" +
                "</div>";

        }else if(data.canEdit=="true") {
            displaySet += " <div class=\"switch toggleSwitch\">" +
                "<span class='public-text'>Set as Public</span>" +
                "<label class=\"switch\">\n" +
                "  <input type=\"checkbox\" id='publicLevel' onchange='javascript:setPublic(this);'>\n" +
                "  <span class=\"slider round\"></span>\n" +
                "</label>" +
                "</div>";
        }

        displaySet +=   "</label>"+
            "            </div>\n" +
            "<div class='col-12 col-lg-6 no-padding'>"+
            "<div class=\"d-flex setname-wrapper ws-input\">"+
            "<input type=\"text\" class=\"form-controls\" placeholder=\"'\" id=\"description\" maxlength = \"100\" >\n" +
            "<label class='ws-label' for=\"description\">Description(Optional | Max 100Characters)</label>"+
            "</div>"+
            "</div>"+
            "</div>"+"<div class='d-flex flex-wrap mt-4 mt-lg-0' id='my-cards'>";

        for(var i=0;i<keyValues.length;i++){
            displaySet +="<div class=\"col-12 col-lg-6 dots mt-4\" id=\"card"+keyValues[i].id+"\">\n" +
                "            <div class=\"card-box\">\n" +
                "                <div class=\"cardHeader\">\n" +
                "                    <p>Front</p>\n" +
                "<button class='delete-card' onclick='javascript:deleteCard("+keyValues[i].id+")'><i class='material-icons'>delete</i></button>"+
                "                </div>\n" +
                "                <div class=\"revision-question\">\n" +
                "                    <form>\n" +
                "                        <textarea class=\"form-control\" placeholder=\"Add Your Question here\" id=\"term"+keyValues[i].id+"\" onchange=\"editFlashCard("+keyValues[i].id+");\" maxlength='300'>"+keyValues[i].term+"</textarea>\n"+
                "                    </form>\n" +
                "                </div>\n" +
                "                <button class=\"add-text mb-2\">Back</button>\n" +
                "                <div class=\"revision-answer\">\n" +
                "                   <textarea class=\"form-control mb-2\" placeholder=\"\" value='"+(keyValues[i].definition).replace(/'/g,"&#39;")+"' id=\"definition"+keyValues[i].id+"\" onchange=\"editFlashCard("+keyValues[i].id+");\" maxlength='500'>"+keyValues[i].definition+"</textarea>\n" +
                "                </div>\n" +


                "            </div>\n" +
                "        </div>";
        }
        displaySet +="</div>"+
            "<div class=\"col-12 col-lg-10 mt-3 submit-buttons text-center mt-4\">\n" ;
        if(previousChapterId==-1) {
            displaySet += "                    <a class=\"btn cancel\" href='javascript:reviseNow(" + data.resId + ",\""+(data.resourceName).replace(/'/g,"&#39;")+"\")'>\n"+
                "            <span>Cancel</span>" +
                "        </a>";
        }
        else{
            displaySet += "        <a class=\"btn cancel\" href='javascript:reviseNow(" + data.resId + ",\""+(data.resourceName).replace(/'/g,"&#39;")+"\")'>\n"+
                "            <span>Cancel</span>\n" +
                "        </a>\n";
        }
        displaySet +=   "        <a href=\"javascript:addKeyValueCard();\" class=\"btn btn-default save-card\">\n" +
            "            <span>+ Add Card</span>\n" +
            "        </a>\n" +
            "    </div>" +
            "<div class='row col-12 col-lg-10 saveCard mt-4 justify-content-center'>"+
            "        <a href=\"javascript:done();\" class=\"btn btn-default saveSubmit\">\n" +
            "            <span>Save & Submit</span>\n" +
            "        </a>\n" +"</div>"+
            "</div>";
        if (!prepjoySite){
            $('.loading-icon').addClass('hidden');
        }else{
            $('#loading').hide();
        }
        document.getElementById('htmlreadingcontent').innerHTML = displaySet;
        document.getElementById("revisionTitle").value=data.resourceName;
        document.getElementById("description").value=data.description;
        if(data.canEdit=="true") {
            flashCardEmptyTemplate();
        }
    }


    function setPublic(field){
        if(field.checked) {
            $('#publicModal').modal('show');
        }

    }
    function makePublic() {
        <g:remoteFunction controller="resources" action="setPrivacyStatusToAwaitingApproval" onSuccess='statusSet(data);' params="'resId='+flashCardId"/>
    }
    function cancelPublic() {
        $('#publicModal').modal('hide');
        // $('#publicLevel').checked = false;
        $('#publicLevel').attr('checked', false);
    }

    //Edit Card
    function editFlashCard(keyValueId){
        var term =  encodeURIComponent(document.getElementById("term"+keyValueId).value);
        var definition = encodeURIComponent(document.getElementById("definition"+keyValueId).value);

        if((term && definition)!= '') {
            $('#card'+keyValueId).find('.card-box').addClass('card-border');
            <g:remoteFunction controller="resources" action="updateFlashCard"
            params="'term='+term+'&definition='+definition+'&keyValueId='+keyValueId"></g:remoteFunction>
        }
        else{
            alert('Edited card should not be empty.');
        }
    }


    // Play Match
    function handlePlayMatchAction(){
        $('#startMatch').modal('show');
        if(previousChapterId==-1) {
            document.querySelector('#htmlreadingcontent').style.display = 'none';
            document.querySelector('#allCards').style.display = 'none';
            document.querySelector('#play-match').style.display = 'block';
        }
        else{
            document.querySelector('#play-match').style.display = 'block';
            document.querySelector('#htmlreadingcontent').innerHTML= document.getElementById('matchCards').innerHTML;
            document.getElementById('matchCards').style.display = 'none';
        }
    }

    //delete card



    var deleteId;
    function deleteCard(keyValueId) {
        //alert(keyValueId);
        $('#deleteModal').modal('show');
        deleteId=keyValueId;

    }
    function deleteConfirm() {
        $("#card" + deleteId).remove();
        <g:remoteFunction controller="wonderpublish" action="deleteFlashCard" params="'keyValueId='+deleteId" onSuccess="deleteSuccess()"></g:remoteFunction>
    }

    function cancelDelete() {
        $('#deleteModal').modal('hide');
    }
    function deleteSuccess() {
        $('#deleteModal').modal('hide');
    }
    function turnSlider() {
        $(".flash-card.active > .flip-box-inner").toggleClass("flipCard");
        $(".turn-slide > i").toggleClass("rotate-icon");
    }

    //Back from slider to card
    function backSlider() {
        $('#slider-wrapper').hide();
        $('.main-wrapper,.submit-buttons,#allAddButton').show();
    }
    //Back to Main Page
    function backToMain() {
        //back to chapters page
        if(previousChapterId!=-1&&!fromAppCreation) {
            if(newStudySet){
                <g:remoteFunction controller="funlearn" action="chapterDetails" params="'topicId='+previousChapterId+'&mode=chapter&fromWeb=true'" onSuccess = "chapterDetailsRecieved(data);"/>
            }
            $('#content-data-all').show();
            document.getElementById('htmlreadingcontent').innerHTML = '';
            $('#allAddButton').css('display', 'flex');
            //exitContentColumn();
        }else{
            if(fromAppCreation){
                <%if("android".equals(session["appType"])){%>
                    <%if("true".equals(params.prepjoyApp)){%>
                        ReadJSInterface.flashCardAddedEvent();
                    <%}else{%>
                        JSInterface.flashCardAddedEvent();
                    <%}%>
                <%}else{%>
                webkit.messageHandlers.flashCardAddedEvent.postMessage('');
                <%}%>
            }
            else {
                window.location.replace(document.referrer);
            }
        }

    }
    function backToSlider(resId,name) {
        reviseNow(resId,name);
    }

    function done(){
        //if from chapter page, update the all list, depending upon whether it is new or not.
        var title = encodeURIComponent(document.getElementById("revisionTitle").value);
        var description = encodeURIComponent(document.getElementById("description").value);
        setName = document.getElementById("revisionTitle").value;

        if(!fromAppCreation){
            <g:remoteFunction controller="resources" action="updateFlashCardTitle"
            params="'resId='+studySetResId+'&title='+title+'&description='+description"></g:remoteFunction>
            if(document.getElementById("definition").value==""&&document.getElementById("term").value==""){
                showCompletedModal();
            }
            else{

                doneMode=true;
                addKeyValueCard();
            }
        }else{
            addKeyValueCard();
            doneMode=true;
        }
    }

    function showCompletedModal(){
        var setnameSaved=setName;
        document.getElementById('setName').innerHTML=setnameSaved;
        $('#successModal').modal('show');

    }
    function shuffleCards() {
        var parent = $("#shuffleAll");
        var divs = parent.children();

        while (divs.length) {
            parent.append(divs.splice(Math.floor(Math.random() * divs.length), 1)[0]);
        }
        $('.flash-card').removeClass('active');
        $('.flash-card').first().addClass('active');
        $('.flash-card').hide();
        $('.flash-card').removeClass('slide2 slide3 slide-right slide-left');
        $('.flash-card.active').show();
        if($('.flash-card').hasClass('active')){
            $('.flash-card.active').next('.flash-card').addClass('slide2');
            $('.flash-card.active').next().next('.flash-card').addClass('slide3');
        }
    }


    function flipSlides() {
        frontElement=$('.flash-card.active .flip-box-front').html();
        backElement=$('.flash-card.active .flip-box-back').html();

        $('.flash-card.active .flip-box-back').html(frontElement);
        $('.flash-card.active .flip-box-front').html(backElement);
        $(".flipSlides > i").toggleClass("rotate-icon");
    }



    function addRevisionToAllTab(linkName,resId){
        var cStr ='';
        cStr +="<div class='container'>"+
            "<div class='all-container'>"+
            "<div class=\"container-wrapper\" id='res_"+i+"'>\n" +
            "<div class='d-flex justify-content-between align-items-center'>";
        cStr += "        <div class=\"media\">\n" ;
        cStr += "                    <a class=\"mb-0 readnow\" href='javascript:reviseNow(" +resId + ",\""+linkName.replace(/'/g,"&#39;")+"\")'>";
        cStr +="   <div class='box yellow'>" +
            "<div>"+
            " <i class=\"align-self-center\">\n" +
            "                \n" +
            "            </i>\n"+
            "<p>Flash Card</p>"+
            "</div>"+"</div>";
        cStr += "            <div class=\"media-body\">\n" +
            "                <p class=\"title\">"+linkName+"</p>\n" ;
        cStr += "            </div>\n" +
            "</a>";
        cStr +=  "</div>"+
            "    </div>\n"+"</div></div></div>";

        document.getElementById('content-data-all').innerHTML += cStr;
    }

    <%if(("android".equals(session["appType"]))||("ios".equals(session["appType"]))){%>
    $('.create-card .mobile-stocker').addClass('d-none').removeClass('d-flex');
    document.addEventListener('touchmove', function (event) {
        if (event.scale != 1) { event.preventDefault(); }
    }, false);
    document.addEventListener('gesturestart', function (e) {
        e.preventDefault();
    });
    $('#openApps').modal('hide');
    $('#loginOpen').modal('hide');
    <%}%>

    function printFlashcards(divName) {

        var printContents = document.getElementById(divName).innerHTML;
        var w = window.open();
        w.document.write('<!DOCTYPE html>');
        w.document.write('<div class="flex">' +
            '<img src="/assets/ws/wonderslateLogo.svg" alt="wonderslate" class="logotext"/>' +
            '<p>Study online at <a href="https://www.wonderslate.com/">www.wonderslate.com</a></p>'+
            '</div>');
        w.document.write('<div class="line w-100"></div>');
        w.document.write("<h1 class='hero-title'>"+resname+"</h1>");
        w.document.write(printContents);
        w.document.write('<link rel="stylesheet" type="text/css"  href="/assets/wonderslate/notesprint.css"/><scr' + 'ipt type="text/javascript">' + 'window.onload = function() { window.print(); window.close(); };' + '</sc' + 'ript>');
        w.document.querySelector('head').innerHTML = '<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.3/dist/katex.min.css" integrity="sha384-Juol1FqnotbkyZUT5Z7gUPjQ9gzlwCENvUZTpQBAPxtusdwFLRy382PSDx5UUJ4/" crossorigin="anonymous">'+
        '<script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.3/dist/katex.min.js" integrity="sha384-97gW6UIJxnlKemYavrqDHSX3SiygeOwIZhwyOKRfSaf0JWKRVj9hLASHgFTzT+0O" crossorigin="anonymous">';
        w.document.close(); // necessary for IE >= 10
        w.focus(); // necessary for IE >= 10
        renderMathInElement(document.body);
        return true;
    }
    function backFlashcardHome() {
        <%if("android".equals(session["appType"])){%>
            <%if("true".equals(params.prepjoyApp)){%>
                ReadJSInterface.backToFlashcardHome();
            <%}else{%>
                JSInterface.backToFlashcardHome();
            <%}%>
        <%}else if("ios".equals(session["appType"])) {%>
        webkit.messageHandlers.backToFlashcardHome.postMessage('');
        <%}%>
        var name = localStorage.getItem('myLocal')
        if(name == 'hasTrue')
        {
            window.location.href='/ebooks';
            localStorage.clear();

        }
        else{
            if (('${params.questionOptions}' !="" && '${params.questionOptions}'!=undefined && '${params.questionOptions}' !=null) || '${params.fromReport}' == 'true' || '${params.fromQuiz}' == 'true'){
                window.close();
            }else{
                if('${params.noOfQuestions}' !=""){
                    window.close();
                }else{
                    window.history.back();
                }
            }
        }


    }


    function backToChapterList() {
        <%if("android".equals(session["appType"])){%>
            <%if("true".equals(params.prepjoyApp)){%>
                ReadJSInterface.backToChapter();
            <%}else{%>
                JSInterface.backToChapter();
            <%}%>
        <%}else if("ios".equals(session["appType"])) {%>
        webkit.messageHandlers.backToChapter.postMessage('');
        <%}%>
    }
    function closeResourceScreen(){
        <%if("android".equals(session["appType"])){%>
            <%if("true".equals(params.prepjoyApp)){%>
                ReadJSInterface.backButtonPressed();
            <%}else{%>
                JSInterface.backButtonPressed();
            <%}%>
        <%}else if("ios".equals(session["appType"])) {%>
        webkit.messageHandlers.backButtonPressed.postMessage('');
        <%}%>

        $("#content-data-all").show();
        $("#htmlreadingcontent").hide();
        $('#allAddButton').show();
        $('.epub-action').hide();
        $('.mobile-back-button').attr('onclick','mobileBackToChapters()');
        $('.mdl-js-layout,.add-tabs').show();
        document.getElementById('relatedBooksContainer').style.marginBottom = '0px';
        document.querySelector('.back_to_top').style.bottom = '25px';
    }

    function backToQuiz(){
        window.history.back();
    }
    function hideBackButton(){
        var name = localStorage.getItem('myLocal')
        if(name == 'hasTrue')
        {

            $('#htmlreadingcontent i.material-icons.backfromcard.flex-fill.d-none.d-lg-block').onclick(function(){
                localStorage.clear();
            });
        }


    }

</script>
