<g:render template="/wonderpublish/loginChecker"></g:render>
<%@ page import="javax.servlet.http.Cookie" %>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:stylesheet href="prepJoy/currentAffairsPage.css" async="true"/>
<link href="https://cdn.jsdelivr.net/npm/swiper@8/swiper-bundle.min.css" rel="stylesheet">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@sweetalert2/theme-dark@5/dark.css" />
<style>
.bookDetails__container{
    width: calc(100%  - 20%);
    margin: 0 auto;
}
div#relatedBooks>div {
    padding: 0px 5px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px){
    .bookDetails__container{
        width: calc(100%  - 2%);
        margin: 0 auto;
    }
    .popular_searches .popular_search_lists {
        margin-left: 0% !important;
    }

}
</style>
<!-------- LOADER --------->
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>

<main class="mainWrapperSection">
    <div class="titleSection">
        <% if(session['prepjoySite']){%>
        <h1>Daily Current Affairs</h1>
        <%}else{%>
        <h1 style="color: #000">Daily Current Affairs</h1>
        <%}%>

        <p>Get the latest current affairs</p>
    </div>

    <div class="mt-5 d-flex align-items-center justify-content-center flex-column">
        <div class="rangeSelector leaderboard w-100">
            <div class="filter-wrapper mb-3">
                <div class="d-flex position-relative">
                    <a href="#" class="col daily-tab active rangeTabElement" data-tab="daily" id="dailyRange">Daily</a>
                    <a href="#" class="col weekly-tab rangeTabElement" data-tab="weekly" id="weeklyRange">Weekly</a>
                    <a href="#" class="col monthly-tab rangeTabElement" data-tab="monthly" id="monthlyRange">Monthly</a>
                    <span class="active-tab-bg"></span>
                </div>
            </div>
        </div>
        <div class="d-flex align-items-center container">
            <div class="prev"><strong><</strong></div>
            <div id="datesList" class="swiper">
                <div class="swiper-wrapper"></div>
            </div>
            <div class="next"><strong>></strong></div>
        </div>
    </div>
</main>


<!--------  MCQ Option Modal --------->
<div class="modal fade modal-modifier" id="mcqOptionModal" data-backdrop="static">
    <div class="modal-dialog modal-dialog-modifier modal-dialog-centered modal-dialog-zoom">
        <div class="modal-content modal-content-modifier">
            <div class="modal-body modal-body-modifier text-center mcqOptionModalBody">
                <button type="button" class="close mr-2" data-dismiss="modal" aria-label="Close" style="text-align: end">
                    <span aria-hidden="true" class="text-white">x</span>
                </button>
                <div class="mcqOptionButtons">
                    <button id="mcqPlay" onclick="quizRedirector(this)" data-type="play">Play</button>
                    <button id="mcqPractice" onclick="quizRedirector(this)" data-type="practice">Practice</button>
                    <button id="mcqTest" onclick="quizRedirector(this)" data-type="test">Test</button>
                    <button id="mcqLearn" onclick="quizRedirector(this)" data-type="learn">Study</button>
                </div>
            </div>
        </div>
    </div>
</div>

<!--------  Read Modal --------->
<div class="modal fade modal-modifier" id="readCAModal" data-backdrop="static">
    <div class="modal-dialog modal-dialog-modifier modal-dialog-centered modal-dialog-zoom">
        <div class="modal-content modal-content-modifier readCAModalModifier">
            <div class="modal-body modal-body-modifier text-center mcqOptionModalBody">
                <button type="button" class="close mr-2" data-dismiss="modal" id="readCloseBtn" aria-label="Close" style="text-align: end">
                    <span aria-hidden="true" class="text-white">x</span>
                </button>
                <div class="readCAModalWrap" id="readCAContent">
                </div>
            </div>
        </div>
    </div>
</div>

<!--------  Watch Modal --------->
<div class="modal fade modal-modifier" id="watchCAModal" data-backdrop="static">
    <div class="modal-dialog modal-dialog-modifier modal-dialog-centered modal-dialog-zoom">
        <div class="modal-content modal-content-modifier readCAModalModifier">
            <div class="modal-body modal-body-modifier text-center mcqOptionModalBody">
                <button type="button" class="close mr-2 videoClose" data-dismiss="modal" aria-label="Close" style="text-align: end">
                    <span aria-hidden="true" class="text-white">x</span>
                </button>
                <div class="readCAModalWrap" id="watchCAContent">
                </div>
            </div>
        </div>
    </div>
</div>
<!--------  Audio Modal --------->
<div class="modal fade modal-modifier" id="listenCAModal" data-backdrop="static">
    <div class="modal-dialog modal-dialog-modifier modal-dialog-centered modal-dialog-zoom">
        <div class="modal-content modal-content-modifier readCAModalModifier">
            <div class="modal-body modal-body-modifier text-center mcqOptionModalBody">
                <button type="button" class="close mr-2 audioClose" data-dismiss="modal" aria-label="Close" style="text-align: end" onclick="audioClose()">
                    <span aria-hidden="true" class="text-white">x</span>
                </button>
                <div class="readCAModalWrap" id="listenCAContent">
                    <div class="player">
                        <div class="details">
                            <div class="now-playing">PLAYING x OF y</div>
                            <div class="track-art"></div>
                            <div class="track-name">Track Name</div>
                            <div class="track-artist">Track Artist</div>
                        </div>
                        <div class="buttons">
                            <div class="prev-track" onclick="prevTrack()">
                                <i class="fa fa-step-backward fa-2x"></i>
                            </div>
                            <div class="playpause-track" onclick="playpauseTrack()">
                                <i class="fa fa-play-circle fa-4x"></i>
                            </div>
                            <div class="next-track" onclick="nextTrack()">
                                <i class="fa fa-step-forward fa-2x"></i>
                            </div>
                        </div>

                        <div class="slider_container">
                            <div class="current-time">00:00</div>
                            <input type="range" min="1" max="100"
                                   value="0" class="seek_slider" onchange="seekTo()">
                            <div class="total-duration">00:00</div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div id="relatedDoubts" class="books-list">
    <g:render template="/resources/relatedBooks"></g:render>
</div>
<script src="https://cdnjs.cloudflare.com/ajax/libs/Swiper/8.4.6/swiper-bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.js"></script>

<% if(session['prepjoySite']){%>
<g:render template="/prepjoy/prepjoy-loader"></g:render>
<%}%>
<g:render template="/${session['entryController']}/footer_new"></g:render>

<script>
    let selectedType='';
    let formattedDate='';
    let currentItemIndex = 0;
    let contentArray;
    let language = 'english';
    let hindiContent = [];
    let englishContent = [];
    let selectedTab='daily';
    let latestDateTemp;
    let startingDateTemp;
    let noOfQ=15;
    let currentVideoSrc=0;
    let videoArray;
    let audioArray;
    let now_playing = document.querySelector(".now-playing");
    let track_art = document.querySelector(".track-art");
    let track_name = document.querySelector(".track-name");
    let track_artist = document.querySelector(".track-artist");

    let playpause_btn = document.querySelector(".playpause-track");
    let next_btn = document.querySelector(".next-track");
    let prev_btn = document.querySelector(".prev-track");

    let seek_slider = document.querySelector(".seek_slider");
    let volume_slider = document.querySelector(".volume_slider");
    let curr_time = document.querySelector(".current-time");
    let total_duration = document.querySelector(".total-duration");

    // Specify globally used values
    let track_index = 0;
    let isPlaying = false;
    let updateTimer;

    let curr_track = document.createElement('audio');
    let track_list = [];
    let API_DateInput="";
    let selectedResID = -1;
    var prepjoySite = "${session['prepjoySite']}";

    const swiper = new Swiper(".swiper", {
        slidesPerView: 5,
        spaceBetween: 5,
        loop: false,
        grabCursor: false,
        centeredSlides: false,
        slideActiveClass: "active",
        navigation: {
            nextEl: ".next",
            prevEl: ".prev"
        },
        pagination: {
            el: ".pagination",
            clickable: false
        },
        autoplay: {
            enabled: false,

        },
        // Media
        breakpoints: {
            320: {
                slidesPerView: 2,
                spaceBetween: 20
            },
            480: {
                slidesPerView: 4,
                spaceBetween: 5
            },
        }
    });

    function getDateRange(selectedTab){
        swiper.slideTo(0);
        const siteId = "${session['siteId']}";
        if (!prepjoySite){
            $('.loading-icon').removeClass('hidden');
        }else{
            document.querySelector('.next strong').style.color = '#fff';
            document.querySelector('.prev strong').style.color = '#fff';
            $('#loading').show();
        }
        <g:remoteFunction controller="admin" action="getCurrentAffairsLatestAndStartDates" params="'siteId='+siteId+'&currentAffairsType=${currentAffairsType}'" onSuccess="requestComplete(data,textStatus)"/>
    }
    getDateRange();

    function requestComplete(data,textStatus){
        if (!prepjoySite){
            $('.loading-icon').addClass('hidden');
        }else{
            $('#loading').hide();
        }
        if (textStatus == 'success'){
            latestDateTemp = data.latestDate;
            startingDateTemp = data.startingDate;
            const latestDate = new Date(data.latestDate);
            const startingDate = new Date(data.startingDate);
            const dateArray = [];

            while (startingDate<=latestDate){
                const options = { day: 'numeric', month: 'short', year: 'numeric' };
                const formattedDate = startingDate.toLocaleDateString(undefined, options)
                dateArray.unshift(formattedDate);
                startingDate.setDate(startingDate.getDate() + 1);
            }
            showDatesList(dateArray);
        }
    }

    function showDatesList(dateArray){
        var datesList = dateArray;
        var cardTitle = "";
        var datesListUI = document.querySelector('.swiper-wrapper');
        let dateItemsHTML = "";
        datesListUI.innerHTML = "";
        datesList.map((item,index)=>{
            if (selectedTab=='daily'){
                cardTitle = item;
            }else if (selectedTab == 'weekly'){
                cardTitle = item.start.replace(",","") + " - "+item.end.replace(",","");
            }else if(selectedTab =='monthly'){
                var parsedDate = new Date(item);
                cardTitle = parsedDate.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
            }
            var cardClass = prepjoySite? 'dateCardItem cardModificationOne' :'dateCardItem  cardModificationTwo';
            dateItemsHTML += "<div class='swiper-slide'>" +
                "<div class='"+cardClass+"'>" +
                "<p>"+cardTitle+"</p>";
            if (selectedTab == 'weekly' || selectedTab == 'monthly'){
                if (selectedTab == 'weekly'){
                    dateItemsHTML += "<button onclick=\"checkMultipleQuiz('"+item.start+"','mcq','25','7')\">25 MCQs</button>"+
                        "<button onclick=\"checkMultipleQuiz('"+item.start+"','mcq','50','7')\">50 MCQs</button>"+
                        "<button onclick=\"checkMultipleQuiz('"+item.start+"','mcq','75','7')\" data-qNo='75'>75 MCQs</button>";
                }else if (selectedTab == 'monthly'){
                    dateItemsHTML += "<button onclick=\"checkMultipleQuiz('"+item+"','mcq','25','30')\">25 MCQs</button>"+
                        "<button onclick=\"checkMultipleQuiz('"+item+"','mcq','50','30')\">50 MCQs</button>"+
                        "<button onclick=\"checkMultipleQuiz('"+item+"','mcq','75','30')\" data-qNo='75'>75 MCQs</button>";
                    dateItemsHTML += "<button onclick=\"checkMultipleQuiz('"+item+"','mcq','100','30')\">100 MCQs</button>";
                }
            }else if (selectedTab == 'daily'){
                dateItemsHTML +=    "<button onclick=\"checkCAData('"+item+"','mcq','15')\" data-qNo='15'>Quiz</button>";
            }
            <%if("71".equals(""+session["siteId"])){%>
            if ( selectedTab == 'daily'){
                dateItemsHTML +=  "<button onclick=\"checkCAData('"+item+"','read')\">Read</button>";
            }
            if (selectedTab == 'weekly' ){
                dateItemsHTML +=  "<button onclick=\"checkCAData('"+item.start+"','read')\">Read</button>";
            }
            <%}%>
            if (selectedTab == 'daily'){
                <%if("71".equals(""+session["siteId"])){%>
                dateItemsHTML +=  "<button onclick=\"checkCAData('"+item+"','watch')\">Watch</button>";
                <%}
                if("1".equals(""+session["siteId"])||"27".equals(""+session["siteId"])){%>
                dateItemsHTML +=   "<button onclick=\"checkCAData('"+item+"','audio')\">NEXT EXAM Audio</button>";
                <%}%>
            }
            dateItemsHTML +="</div>" +
                "</div>";
        });

        dateItemsHTML +="</ul>";
        datesListUI.innerHTML = dateItemsHTML;
    }

    function checkCAData(date,type,noOfQCount){
        var siteId = "${session['siteId']}";
        var selectedDateStr = date;
        var inputDate = new Date(selectedDateStr);
        var year = inputDate.getFullYear();
        var month = (inputDate.getMonth() + 1 ).toString().padStart(2,'0');
        var day = inputDate.getDate().toString().padStart(2,'0');
        formattedDate = year+'-'+month+'-'+day;
        selectedType = type;
        noOfQ = noOfQCount;
        var caFormDate = formattedDate.split('-');
        caFormDate = caFormDate[2]+"-"+caFormDate[1]+"-"+caFormDate[0];
        if (selectedTab=='weekly' && type=='read'){
            <g:remoteFunction controller="prepjoy" action="getWeeklyCurrentAffairsReadingMaterials" params="'siteId='+siteId+'&currentAffairsType=${currentAffairsType}&dateInput='+caFormDate" onSuccess="openCAWeekly(data)" />
        }else{
            <g:remoteFunction controller="admin" action="getCurrentAffairsForDate" params="'siteId='+siteId+'&currentAffairsType=${currentAffairsType}&inputDate='+caFormDate" onSuccess="openCA(data)" />
        }

    }

    function openCA(data){
        var readingMaterials = JSON.parse(data.readingMaterials);
        var videos = JSON.parse(data.videos);
        var quizResId = data.quizResId;
        selectedResID = quizResId;
        var audios = JSON.parse(data.audios);
        var canOpen = validateCA(readingMaterials.length,videos.length,quizResId,audios.length);

        if (canOpen && selectedType == 'mcq'){
            $('#mcqOptionModal').modal('show');
        }else if(canOpen && selectedType=='audio'){
            openAudio();
        }else if(canOpen && selectedType == 'read'){
            openRead();
        }else if (canOpen && selectedType=='watch'){
            openWatch();
        }
    }

    function validateCA(rm,vd,qz,ad){
        if (rm == 0 && selectedType == 'read'){
            Swal.fire({
                title: "Reading Material is not available for the date",
                icon: "error",
                button: "OK",
            });
            return false;
        }else if(vd == 0 && selectedType == 'watch'){
            Swal.fire({
                title: "Video is not available for the date",
                icon: "error",
                button: "OK",
            });
            return false;
        } else if(ad == 0 && selectedType == 'audio'){
            Swal.fire({
                title: "Audio is not available for the date",
                icon: "error",
                button: "OK",
            });
            return false;
        } else if((qz == '' || qz == null || qz==-1) && selectedType == 'mcq'){
            Swal.fire({
                title: "Quiz is not available for the date",
                icon: "error",
                button: "OK",
            });
            return false;
        }else if((qz == '' || qz == null || qz==-1) && selectedType == 'history'){
            Swal.fire({
                title: "History is not available for the date",
                icon: "error",
                button: "OK",
            });
            return false;
        }
        return true;
    }

    function checkMultipleQuiz(dateInput,type,nos,days){

        const dateval = new Date(dateInput);
        let temp = dateval.toLocaleString("en-US",{
            day:'2-digit',
            month:'2-digit',
            year:'numeric'
        });
        temp = temp.split("/");
        temp = temp[1]+"-"+temp[0]+"-"+temp[2]
        API_DateInput = temp;
        noOfQ = nos;
        <g:remoteFunction controller="prepjoy" action="getMultiDaysQuiz" params="'noOfQuestions='+nos+'&siteId='+${session['siteId']}+'&currentAffairsType=${currentAffairsType}&noOfDays='+days+'&dateInput='+API_DateInput" onSuccess="validateMultipleQuiz(data)" />
    }

    function validateMultipleQuiz(data){
        if (JSON.parse(data.results).length<=0){
            Swal.fire({
                title: "Quiz is not available for the date",
                icon: "error",
                button: "OK",
            });
            return;
        }
        $('#mcqOptionModal').modal('show');
    }

    function quizRedirector(element){
        if (element.getAttribute("data-type") == 'play'){
            openMCQ('',false,selectedResID,API_DateInput);
        }else if (element.getAttribute("data-type") == 'practice'){
            openMCQ('practice',false,selectedResID,API_DateInput);
        }else if (element.getAttribute("data-type") == 'test'){
            openMCQ('testSeries',false,selectedResID,API_DateInput);
        }else if (element.getAttribute("data-type") == 'learn'){
            var learnURL="";
            setTimeout(()=>{
                if (selectedTab=='daily'){
                    learnURL  = "/funlearn/quiz?resId="+selectedResID+"&mode="+selectedTab+"&quizMode=learn&pubDesk=false";
                }else{
                    const dd = selectedTab=='weekly'?7:30;
                    learnURL = "/funlearn/quiz?dateInput="+API_DateInput+"&mode="+selectedTab+"&quizMode=learn&pubDesk=false&noOfQ="+noOfQ+'&days='+dd;
                }
                window.open(learnURL, '_blank');
            })
        }
    }

    function openMCQ(quizType,learn,quizResId,dateInput){
        setTimeout(() => {
            if (selectedTab=='daily'){
                var quizURL = '/prepjoy/prepJoyGame?mode=caDaily&quizType='+quizType+'&learn='+learn+'&pubDesk=false&resId='+quizResId+'&noOfQ='+noOfQ;
            }else if(selectedTab=='weekly'){
                var quizURL = '/prepjoy/prepJoyGame?mode=caWeekly&quizType='+quizType+'&learn='+learn+'&pubDesk=false&dateInput='+dateInput+'&noOfQ='+noOfQ+'&days=7';
            }else if(selectedTab=='monthly'){
                var quizURL = '/prepjoy/prepJoyGame?mode=caMonthly&quizType='+quizType+'&learn='+learn+'&pubDesk=false&dateInput='+dateInput+'&noOfQ='+noOfQ+'&days=30';
            }
            window.open(quizURL, '_blank');
        })
    }

    function openHistory(resId){
        const siteId = "${session['siteId']}";
        setTimeout(() => {
            var quizURL = '/prepjoy/history?siteId='+siteId+'&resId='+resId+'&quizType=currentAffairs';
            window.open(quizURL, '_blank');
        })
    }

    function openRead(){
        const siteId = "${session['siteId']}";
        let readFormDate = formattedDate.split('-');
        readFormDate = readFormDate[2]+"-"+readFormDate[1]+"-"+readFormDate[0];
        if (selectedTab=='daily'){
            <g:remoteFunction controller="admin" action="getCurrentAffairsReadingMaterials" params="'siteId='+siteId+'&currentAffairsType=${currentAffairsType}&inputDate='+readFormDate" onSuccess="showRead(data)" />
        }else if(selectedTab=='weekly'){
            <g:remoteFunction controller="prepjoy" action="getWeeklyCurrentAffairsReadingMaterials" params="'siteId='+siteId+'&currentAffairsType=${currentAffairsType}&inputDate='+readFormDate" onSuccess="showRead(data)" />
        }
    }

    function showRead(data){
        var readingMaterials;
        var formattedContent;
        readingMaterials = data.readingMaterials;
        readingMaterials  = JSON.parse(readingMaterials);
        readingMaterials.forEach((item,index)=>{
            if (item.tag.language == "Hindi"){
                hindiContent.push(item);
            }else if(item.tag.language == "English"){
                englishContent.push(item)
            }
        });
        if (language == 'english'){
            contentArray = englishContent;
            formatRead(englishContent,language)
        }else if (language == 'hindi'){
            contentArray = hindiContent;
            formatRead(hindiContent,language)
        }
        $('#readCAModal').modal('show');
    }

    function formatRead(contentArray,language){
        updateReadView(contentArray[currentItemIndex],language);
    }

    function updateReadView(item,languageVal){
        var readCAContentHTML = "";
        readCAContentHTML += "<div style='display:flex;flex-direction: column;' class='readCAContentDiv'>" +
            "<h3 class='mb-4'>"+item.title+"</h3>"+
            ""+item.description+""+
            "<p class='mt-4'>"+item.tag.plainDescription+"</p>"+
            "<div class='d-flex align-items-center'>" +
            "  <p>To read full news </p><a href='"+item.referenceLink+"' class='text-danger ml-2' target='_blank'> click here</a>" +
            "</div>"+
            "<div class='d-flex align-items-center justify-content-between mt-auto'>"+
            "<div class='d-flex align-items-center' style='gap: 10px'>";
        if (language=='english' && (hindiContent.length>0 && englishContent.length>0)){
            readCAContentHTML +="<button class='langBtn' id='translateBtn'><img src='/assets/prepJoy/prepjoy-website/e2h.png' ></button>";
        }else if (language=='hindi' && (englishContent.length>0 && hindiContent.length>0)){
            readCAContentHTML +="<button class='langBtn' id='translateBtn'><img src='/assets/prepJoy/prepjoy-website/h2e.png'></button>";
        }

        readCAContentHTML +="<button class='langBtn' id='speech'><img src='/assets/prepJoy/prepjoy-website/speaker.png'></button>"+
            "</div>";
        readCAContentHTML +="<div style='display: flex;gap: 10px'>";
        if (currentItemIndex === 0){
            readCAContentHTML +="<button onclick='changeContent(-1)' id='prevRead' disabled>Previous</button>";
        }else{
            readCAContentHTML +="<button onclick='changeContent(-1)' id='prevRead'>Previous</button>";
        }
        if (currentItemIndex == contentArray.length-1){
            readCAContentHTML +="<button onclick='changeContent(1)' id='nextRead' disabled>Next</button>";
        }else{
            readCAContentHTML +="<button onclick='changeContent(1)' id='nextRead'>Next</button>";
        }
        readCAContentHTML +="</div>";
        readCAContentHTML +="</div>"+
            "</div>";
        document.getElementById('readCAContent').innerHTML = readCAContentHTML;

        const translateBtn = document.getElementById('translateBtn');
        const speech = document.getElementById('speech');

        if (translateBtn!=undefined){
            translateBtn.addEventListener('click',function (){
                if (languageVal=='english'){
                    language = 'hindi';
                    contentArray = hindiContent;
                    formatRead(contentArray,language);
                }else if (languageVal=='hindi'){
                    language = 'english';
                    contentArray = englishContent;
                    formatRead(contentArray,language)
                }
            });
        }

        const playPauseButton = document.getElementById('speech');
        playPauseButton.addEventListener('click', () => {
            const text = item.tag.plainDescription;
            textToSpeech(playPauseButton,text)
        });
    }

    function openCAWeekly(data){
        var readContent = JSON.parse(data.readingMaterials);
        if (readContent.length<=0){
            Swal.fire({
                title: "Reading Material is not available for the date",
                icon: "error",
                button: "OK",
            });
            return
        }
        showRead(data);
    }

    let currentUtterance = null;
    const synth = window.speechSynthesis;
    let voices = synth.getVoices();
    let isPaused = false;

    function textToSpeech(buttonElement,text){
        if ('speechSynthesis' in window) {
            if (currentUtterance!=null) {
                if (isPaused) {
                    synth.resume();
                    isPaused = false;
                    buttonElement.innerHTML = "<img src='/assets/prepJoy/prepjoy-website/pause-speech.png'>";
                } else {
                    synth.pause();
                    isPaused = true;
                    buttonElement.innerHTML = "<img src='/assets/prepJoy/prepjoy-website/speaker.png'>";
                }
            } else {
                if (text !== '') {
                    const utterance = new SpeechSynthesisUtterance(text);
                    const selectedVoice = voices.find(voice => voice.lang === 'en-US');
                    if (selectedVoice) {
                        utterance.voice = selectedVoice;
                    }
                    utterance.onend = () => {
                        currentUtterance = null;
                        buttonElement.innerHTML = "<img src='/assets/prepJoy/prepjoy-website/speaker.png'>";
                    };
                    synth.speak(utterance);
                    buttonElement.innerHTML = "<img src='/assets/prepJoy/prepjoy-website/pause-speech.png'>";
                    currentUtterance = utterance;
                }
            }
        }
    }

    function stopTTS(){
        window.speechSynthesis.cancel();
        currentUtterance = null;
        voices = [];
        isPaused = false;
    }

    window.addEventListener("beforeunload", function (e) {
        stopTTS();
    });

    function changeContent(count){
        stopTTS();
        if (count==-1){
            currentItemIndex -= 1;
            formatRead(contentArray,language);
        }else if(count==1){
            currentItemIndex += 1
            formatRead(contentArray,language);
        }
    }

    function openWatch(){
        let watchFormDate = formattedDate.split('-');
        watchFormDate = watchFormDate[2]+"-"+watchFormDate[1]+"-"+watchFormDate[0];
        <g:remoteFunction controller="admin" action="getCurrentAffairsVideos" params="'siteId='+siteId+'&currentAffairsType=${currentAffairsType}&inputDate='+watchFormDate" onSuccess="showVideo(data)" />
    }

    function showVideo(data){
        let response = data.videos
        response = JSON.parse(response);
        videoArray = response;
        updateVideoSRC(videoArray)
        $('#watchCAModal').modal('show');
    }

    function updateVideoSRC(srcArray){
        buildVideoSrc(srcArray[currentVideoSrc])
    }

    function buildVideoSrc(item){
        const watchCAContent = document.getElementById('watchCAContent');
        let videoHTML = "";
        if (item.videoLink!=null){
            let src = item.videoLink.split('=')[1];
            src = "https://www.youtube.com/embed/"+src;

            videoHTML += "<iframe width='100%' height='315' frameborder='0' src='"+src+"' allowfullscreen id='vsrc'></iframe>"+
                "<div class='mt-3 d-flex align-items-center justify-content-between'>";
            if (currentVideoSrc===0){
                videoHTML+="<button id='prevVideo' disabled>Previous</button>";
            }else{
                videoHTML+="<button id='prevVideo' onclick='changeVideo(-1)'>Previous</button>";
            }

            if (videoArray.length-1 === currentVideoSrc){
                videoHTML+= "<button id='nextVideo' disabled>Next</button>";
            }else{
                videoHTML+= "<button id='nextVideo' onclick='changeVideo(1)'>Next</button>";
            }
            videoHTML+="</div>";
        }
        watchCAContent.innerHTML = videoHTML;
        document.querySelector('.videoClose').addEventListener('click',function (){
            document.getElementById('vsrc').setAttribute('src','');
        })
    }

    function changeVideo(count){
        if (count==-1){
            currentVideoSrc -= 1;
            updateVideoSRC(videoArray);
        }else if(count==1){
            currentVideoSrc += 1
            updateVideoSRC(videoArray);
        }
    }

    function openAudio(){
        let audFormDate = formattedDate.split('-');
        audFormDate = audFormDate[2]+"-"+audFormDate[1]+"-"+audFormDate[0];
        track_list = [];
        <g:remoteFunction controller="admin" action="getCurrentAffairsAudios" params="'siteId='+siteId+'&currentAffairsType=${currentAffairsType}&inputDate='+audFormDate" onSuccess="showAudio(data)" />
    }

    function showAudio(data){
        audioArray = JSON.parse(data.audios)
        let formattedAudioLink;
        let extractedValue;
        if (audioArray!=null){
            audioArray.forEach(item=>{
                const regex = /\/d\/(.*?)\//;
                const match = item.videoLink.match(regex);

                if (match && match[1]) {
                    extractedValue = match[1];
                } else {
                    console.log("Value not found in the link");
                }
                formattedAudioLink = "https://docs.google.com/uc?export=open&id="+extractedValue;
                track_list.push({
                    name:item.title,
                    artist:"",
                    image:"",
                    path:formattedAudioLink
                })
            })
            loadTrack(track_index);
            playTrack();
            $('#listenCAModal').modal('show');
        }
    }

    const rangeTabElement = document.querySelectorAll('.rangeTabElement')

    rangeTabElement.forEach(item=>{
        item.addEventListener('click',function (e){
            rangeTabElement.forEach(elm=>elm.classList.remove('active'));
            e.target.classList.add('active');
            selectedTab = e.target.getAttribute('data-tab');
            if (e.target.id=='dailyRange'){
                getDateRange('dailyRange');
            }else if(e.target.id=='weeklyRange'){
                updateWeeklyDateRange();
            }else if(e.target.id=='monthlyRange'){
                updateMonthlyDateRange();
            }
        })
    })

    function updateWeeklyDateRange(){
        swiper.slideTo(0);
        var startingDate = new Date(startingDateTemp);
        var latestDate = new Date(latestDateTemp);
        var weeksArray = [];
        var currentWeekStartDate = new Date();
        currentWeekStartDate.setDate(currentWeekStartDate.getDate() - (currentWeekStartDate.getDay() + 1) % 7);

        for (var currentDate = startingDate; currentDate <= latestDate; currentDate.setDate(currentDate.getDate() + 1)) {
            if (currentDate.getDay() === 0) {
                var weekStartDate = new Date(currentDate);
                var weekEndDate = new Date(currentDate);

                weekEndDate.setDate(currentDate.getDate() + 6);

                if (weekEndDate <= latestDate) {
                    var options = { day: 'numeric', month: 'short', year: 'numeric' };
                    weeksArray.unshift({
                        start: weekStartDate.toLocaleDateString('en-US', options),
                        end: weekEndDate.toLocaleDateString('en-US', options),
                    });
                }
            }
        }
        showDatesList(weeksArray)
    }

    function updateMonthlyDateRange(){
        swiper.slideTo(0);
        var latestDate = new Date(latestDateTemp);
        var startingDate = new Date(startingDateTemp);
        var monthsDifference = (latestDate.getFullYear() - startingDate.getFullYear()) * 12 +
            (latestDate.getMonth() - startingDate.getMonth());
        var monthsArray = [];

        for (var i = 0; i <= monthsDifference; i++) {
            var currentMonth = new Date(startingDate);
            currentMonth.setMonth(startingDate.getMonth() + i);

            var monthStart = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), 1);
            var nextMonth = new Date(currentMonth);
            nextMonth.setMonth(currentMonth.getMonth() + 1);
            var monthEnd = new Date(nextMonth.getFullYear(), nextMonth.getMonth(), 0);

            if (monthEnd <= latestDate) {
                var options = { day: 'numeric', month: 'short', year: 'numeric' };
                var monthName = monthEnd.toLocaleDateString(undefined, options);
                monthsArray.unshift(monthName);
            }
        }

        showDatesList(monthsArray)
    }

    function loadTrack(track_index) {
        clearInterval(updateTimer);
        resetValues();
        curr_track.src = track_list[track_index].path;
        curr_track.load();

        track_art.style.backgroundImage =
            "url(" + track_list[track_index].image + ")";
        track_name.textContent = track_list[track_index].name;
        track_artist.textContent = track_list[track_index].artist;
        now_playing.textContent =
            "PLAYING " + (track_index + 1) + " OF " + track_list.length;
        updateTimer = setInterval(seekUpdate, 1000);
        if (audioArray[track_index].description!="" && audioArray[track_index].description!=null){
            document.querySelector('.track-art').innerHTML = audioArray[track_index].description;
        }else{
            document.querySelector('.track-art').innerHTML = "<p><img src='assets/prepjoy/prepjoy.svg' width='200'/></p>";
        }

        curr_track.addEventListener("ended", nextTrack);
    }

    function random_bg_color() {
        let red = Math.floor(Math.random() * 256) + 64;
        let green = Math.floor(Math.random() * 256) + 64;
        let blue = Math.floor(Math.random() * 256) + 64;

        let bgColor = "rgb(" + red + ", " + green + ", " + blue + ")";

        document.body.style.background = bgColor;
    }

    function resetValues() {
        curr_time.textContent = "00:00";
        total_duration.textContent = "00:00";
        seek_slider.value = 0;
    }

    function playpauseTrack() {
        if (!isPlaying) playTrack();
        else pauseTrack();
    }

    function playTrack() {
        curr_track.play();
        isPlaying = true;
        playpause_btn.innerHTML = '<i class="fa fa-pause-circle fa-4x"></i>';
    }

    function pauseTrack() {
        curr_track.pause();
        isPlaying = false;
        playpause_btn.innerHTML = '<i class="fa fa-play-circle fa-4x"></i>';
    }

    function nextTrack() {
        if (track_index < track_list.length - 1)
            track_index += 1;
        else track_index = 0;
        loadTrack(track_index);
        playTrack();
    }

    function prevTrack() {
        if (track_index > 0)
            track_index -= 1;
        else track_index = track_list.length - 1;
        loadTrack(track_index);
        playTrack();
    }
    function seekTo() {
        seekto = curr_track.duration * (seek_slider.value / 100);
        curr_track.currentTime = seekto;
    }

    function setVolume() {
        curr_track.volume = volume_slider.value / 100;
    }

    function seekUpdate() {
        let seekPosition = 0;
        if (!isNaN(curr_track.duration)) {
            seekPosition = curr_track.currentTime * (100 / curr_track.duration);
            seek_slider.value = seekPosition;

            let currentMinutes = Math.floor(curr_track.currentTime / 60);
            let currentSeconds = Math.floor(curr_track.currentTime - currentMinutes * 60);
            let durationMinutes = Math.floor(curr_track.duration / 60);
            let durationSeconds = Math.floor(curr_track.duration - durationMinutes * 60);

            if (currentSeconds < 10) { currentSeconds = "0" + currentSeconds; }
            if (durationSeconds < 10) { durationSeconds = "0" + durationSeconds; }
            if (currentMinutes < 10) { currentMinutes = "0" + currentMinutes; }
            if (durationMinutes < 10) { durationMinutes = "0" + durationMinutes; }
            curr_time.textContent = currentMinutes + ":" + currentSeconds;
            total_duration.textContent = durationMinutes + ":" + durationSeconds;
        }
    }

    function audioClose(){
        pauseTrack();
    }

    document.getElementById("readCloseBtn").addEventListener("click",function (e){
        stopTTS();
        contentArray = [];
        hindiContent = [];
        englishContent = [];
        currentItemIndex = 0;
    })
</script>


