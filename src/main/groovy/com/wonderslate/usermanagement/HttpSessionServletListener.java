package com.wonderslate.usermanagement;

import grails.events.*;
import javax.servlet.http.HttpSession;
import javax.servlet.http.HttpSessionEvent;
import javax.servlet.http.HttpSessionListener;
import org.springframework.security.core.session.SessionRegistry;
import org.springframework.util.Assert;

public class HttpSessionServletListener implements HttpSessionListener{
    private final SessionRegistry sessionRegistry;

    /**
     * Creates a new instance
     * @param sessionRegistry the {@link SessionRegistry} to use
     */
    HttpSessionServletListener(SessionRegistry sessionRegistry) {
        Assert.notNull(sessionRegistry, "sessionRegistry cannot be null");
        this.sessionRegistry = sessionRegistry;
    }
    // called by servlet container upon session creation
   public void sessionCreated(HttpSessionEvent event) {

    }

    // called by servlet container upon session destruction
   public void sessionDestroyed(HttpSessionEvent event) {
       sessionRegistry.removeSessionInformation(event.getSession().getId());
    }
}
