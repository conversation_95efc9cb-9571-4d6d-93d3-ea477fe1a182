{
  "mcqs": [
    {
      "question_number": 1,
      "option1": "Thursday",
      "option2": "Friday",
      "option3": "Sunday",
      "option4": "Saturday",
      "direction": "",
      "question_images": [],
      "option_images": {},
      "question_text": "If 21st January 2022 was Friday, then what will be the day of the week on 21st February 2025?",
      "was_corrected": true
    },
    {
      "question_number": 2,
      "option1": "59",
      "option2": "84",
      "option3": "95",
      "option4": "48",
      "direction": "",
      "question_images": [],
      "option_images": {},
      "question_text": "Select the option that is related to the fifth term in the same way as the second term is related to the first term and the fourth term is related to the third term\n3664 : 68 :: 4916 : 74 :: 8125 : ?",
      "was_corrected": false
    },
    {
      "question_number": 3,
      "option1": "Humanitarian",
      "option2": "Torture",
      "option3": "Mercy",
      "option4": "Refuge",
      "direction": "",
      "question_images": [],
      "option_images": {},
      "question_text": "Select the option that is related to the third word in the same way as the second word is related to the first word. (The words must be considered as meaningful English words and must NOT be related to each other based on the number of letters/number of consonants/vowels in the world).\nDungeon : Confinement :: Asylum : ?",
      "was_corrected": false
    },
    {
      "question_number": 4,
      "option1": null,
      "option2": null,
      "option3": null,
      "option4": null,
      "direction": "",
      "question_images": [],
      "option_images": {},
      "question_text": "Select the set in which the numbers are related in the same way as are the numbers of the following sets.\n(54, 38, 864)\n(39, 12, 1053)\n(Note: Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. For example, operations on 18 such as adding/deleting/multiplying etc. to 18 can be performed. Breaking down 18 into 1 and 8 and then performing mathematical operations on 1 and 8 is NOT allowed).",
      "was_corrected": false
    }
  ],
  "answer_keys": {},
  "explanations": []
}
{
  "mcqs": [
    {
      "question_number": 5,
      "option1": "S and P",
      "option2": "A and B",
      "option3": "S and A",
      "option4": "S and Q",
      "direction": "",
      "question_images": [],
      "option_images": {},
      "question_text": "Four persons A, B, C and D are sitting in a restaurant around a circular table for dinner facing the centre of the table but not necessarily in the same order. After some time, their four friends P, Q, R and S also joined them on the same table. A is sitting just opposite to Q and between C and R. D is sitting second to the right of R and third to the left of S. If B is sitting just opposite to S, who are the immediate neighbours of C?",
      "was_corrected": false
    },
    {
      "question_number": 6,
      "option1": "Mother",
      "option2": "Aunt",
      "option3": "Sister (Cousin)",
      "option4": "Sister-in-law",
      "direction": "",
      "question_images": [],
      "option_images": {},
      "question_text": "Satish is father of Mahesh, who is maternal uncle of Shobhit. Naina is the only daughter of Madhuri. Shreya is mother-in-law of Madhuri. Mahesh is the only son of Shreya. Tanvi is sister of Shobhit. How Naina is related to Shobhit?",
      "was_corrected": false
    },
    {
      "question_number": 7,
      "option1": "KISKZGCFV",
      "option2": "HFSKWDCFS",
      "option3": "HFVNWDFIS",
      "option4": "IERLVEDGT",
      "direction": "",
      "question_images": [],
      "option_images": {},
      "question_text": "In a certain code language, INCURABLE is written as OJUCFWVLS. How will PREMATURE be written in that code language?",
      "was_corrected": false
    },
    {
      "question_number": 8,
      "option1": null,
      "option2": null,
      "option3": null,
      "option4": null,
      "direction": "",
      "question_images": [],
      "option_images": {},
      "question_text": "Select the option that is related to the fifth letter-cluster in the same way as the second letter-cluster is related to the first letter-cluster and the fourth letter-cluster is related to the third letter-cluster.",
      "was_corrected": false
    }
  ],
  "answer_keys": {},
  "explanations": []
}
{
  "mcqs": [
    {
      "question_number": 9,
      "option1": "64",
      "option2": "100",
      "option3": "81",
      "option4": "121",
      "direction": "",
      "question_images": [
        "supload/pdfextracts/862/5889/11549/extractedQuizImages/question_9.png"
      ],
      "option_images": {},
      "question_text": "Find the missing number in the matrix given below :\n\\[\n\\begin{array}{ccc}\n27 & 12 & 225 \\\\\n26 & 13 & 169 \\\\\n30 & 21 & ? \\\\\n26 & 12 & 196 \\\\\n\\end{array}\n\\]",
      "was_corrected": false
    },
    {
      "question_number": 10,
      "option1": "MCQ_IMAGE",
      "option2": "MCQ_IMAGE",
      "option3": "MCQ_IMAGE",
      "option4": "MCQ_IMAGE",
      "direction": "",
      "question_images": [
        "supload/pdfextracts/862/5889/11549/extractedQuizImages/question_10.png"
      ],
      "option_images": {
        "option_1": [
          "supload/pdfextracts/862/5889/11549/extractedQuizImages/question_10_option_1.png"
        ],
        "option_2": [
          "supload/pdfextracts/862/5889/11549/extractedQuizImages/question_10_option_2.png"
        ]
      },
      "question_text": "Which of the answer figures is the exact mirror image of the given problem figure when the mirror is held at the right side?\nQuestion Figure :\nMCQ_IMAGE\nAnswer Figures :",
      "was_corrected": true
    }
  ],
  "answer_keys": {},
  "explanations": []
}
