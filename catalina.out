06-Jun-2025 03:59:18.458 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server version:        Apache Tomcat/8.5.13
06-Jun-2025 03:59:18.466 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server built:          Mar 27 2017 14:25:04 UTC
06-Jun-2025 03:59:18.467 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server number:         8.5.13.0
06-Jun-2025 03:59:18.467 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Name:               Linux
06-Jun-2025 03:59:18.469 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Version:            6.1.129-138.220.amzn2023.x86_64
06-Jun-2025 03:59:18.469 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Architecture:          amd64
06-Jun-2025 03:59:18.469 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Java Home:             /usr/lib/jvm/java-1.8.0-amazon-corretto.x86_64/jre
06-Jun-2025 03:59:18.470 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Version:           1.8.0_442-b06
06-Jun-2025 03:59:18.470 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Vendor:            Amazon.com Inc.
06-Jun-2025 03:59:18.471 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_BASE:         /u01/apache-tomcat-8.5.13
06-Jun-2025 03:59:18.471 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_HOME:         /u01/apache-tomcat-8.5.13
06-Jun-2025 03:59:18.472 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.config.file=/u01/apache-tomcat-8.5.13/conf/logging.properties
06-Jun-2025 03:59:18.472 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager
06-Jun-2025 03:59:18.473 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djdk.tls.ephemeralDHKeySize=2048
06-Jun-2025 03:59:18.473 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.protocol.handler.pkgs=org.apache.catalina.webresources
06-Jun-2025 03:59:18.474 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.base=/u01/apache-tomcat-8.5.13
06-Jun-2025 03:59:18.474 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.home=/u01/apache-tomcat-8.5.13
06-Jun-2025 03:59:18.475 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.io.tmpdir=/u01/apache-tomcat-8.5.13/temp
06-Jun-2025 03:59:18.475 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: /usr/java/packages/lib/amd64:/usr/lib64:/lib64:/lib:/usr/lib
06-Jun-2025 03:59:18.643 INFO [main] org.apache.coyote.AbstractProtocol.init Initializing ProtocolHandler ["ajp-nio-8009"]
06-Jun-2025 03:59:18.682 INFO [main] org.apache.tomcat.util.net.NioSelectorPool.getSharedSelector Using a shared selector for servlet write/read
06-Jun-2025 03:59:18.686 INFO [main] org.apache.catalina.startup.Catalina.load Initialization processed in 1204 ms
06-Jun-2025 03:59:18.735 INFO [main] org.apache.catalina.core.StandardService.startInternal Starting service Catalina
06-Jun-2025 03:59:18.735 INFO [main] org.apache.catalina.core.StandardEngine.startInternal Starting Servlet Engine: Apache Tomcat/8.5.13
06-Jun-2025 03:59:18.749 INFO [ws-startStop-1] org.apache.catalina.startup.HostConfig.deployDescriptor Deploying configuration descriptor /u01/apache-tomcat-8.5.13/conf/Catalina/ws/ROOT.xml
06-Jun-2025 03:59:18.819 INFO [ws-startStop-1] org.apache.catalina.startup.ExpandWar.expand An expanded directory [/u01/apache-tomcat-8.5.13/webapps/ROOT] was found with a last modified time that did not match the associated WAR. It will be deleted.
06-Jun-2025 03:59:32.664 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishbookCreate_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.666 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishbookCreateNew_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.669 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishindependentContent_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.670 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishindexHome_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.671 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishinstructorLibrary_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.672 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishinstructorLibrary_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.673 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishmanageExams_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.674 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishmanageExams_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.675 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishmanageTabs_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.676 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishmanageTabs_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.677 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishmybooks_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.678 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishmybooks_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.679 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishmylibrary_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.680 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishmylibrary_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.681 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishnotescreator_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.682 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishnotescreator_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.682 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishopenPDF_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.683 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishopenPDF_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.684 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishorders_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.685 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishorders_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.686 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishpdfReader_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.687 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishpdfReader_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.688 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishpubDesk_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.689 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishpubDesk_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.690 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishpubSales_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.690 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishpubSales_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.691 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishqandaCreator_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.692 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishqandaCreator_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.693 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishquizcreator_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.694 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishquizcreator_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.695 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishquizcreatorbulk_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.696 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishquizcreatorbulk_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.697 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishquizcreatorbulkinput_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.697 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishquizcreatorbulkinput_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.698 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishrelatedVideosAdmin_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.699 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishrelatedVideosAdmin_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.700 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishresetPasswordEmail_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.701 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishresetPasswordEmail_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.702 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishselfService_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.703 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishselfService_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.703 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishstudySet_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.704 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishstudySet_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.705 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishtestgenerator_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.706 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishtestgenerator_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.707 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishuploadTex_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.707 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishuploadTex_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.708 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishvideoExplanation_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.709 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishvideoExplanation_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.710 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishvideoplayer_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.710 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishvideoplayer_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.711 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishwseditor_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.712 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishwseditor_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.713 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderslatekidsindex_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.713 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderslatekidsindex_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.714 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmain_aiOptions_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.715 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmain_aiOptions_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.716 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainbookai_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.716 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainbookai_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.717 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainindex_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.718 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainindex_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.719 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainmyLibrary_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.719 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainmyLibrary_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.720 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainunmarkedQA_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.721 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainunmarkedQA_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.722 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsLibraryaccessCode_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.722 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsLibraryaccessCode_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.723 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsLibraryindex_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.723 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsLibraryindex_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.724 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsLibrarymyLibrary_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.724 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsLibrarymyLibrary_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.725 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshop_blogEnglishDisplay_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.725 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshop_blogEnglishDisplay_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.726 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshop_cartScripts_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.726 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshop_cartScripts_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.726 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshop_searchScripts_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.727 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshop_searchScripts_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.727 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshop_shoppingCartLogic_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.728 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshop_shoppingCartLogic_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.729 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshop_videoCreator_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.729 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshop_videoCreator_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.729 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshopaddGradeInfo_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.730 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshopaddGradeInfo_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.730 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshopamazonorderconfirmation_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.731 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshopamazonorderconfirmation_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.731 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshopblogEnglish_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.732 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshopblogEnglish_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.732 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshopblogHindi_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.733 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshopblogHindi_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.733 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshopcart_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.734 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshopcart_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.734 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshopmanageShopSpecials_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.735 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshopmanageShopSpecials_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.735 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshoporderManagement_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.736 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshoporderManagement_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.736 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshopvalidityExtensionAdmin_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.737 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshopvalidityExtensionAdmin_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.737 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.738 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/ibookso] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.741 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/usermanagement] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.742 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/discussion] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.742 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/admin] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.743 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/librarybooks] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.743 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/institute] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.744 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/logs] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.746 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.747 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/cache] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.747 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/groups] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.748 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/toDo] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.748 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/shop] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.749 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/comparison] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.749 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/WsLibrary] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.750 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/drive] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.750 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/qp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.750 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/report] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.751 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/log] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.751 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/prepjoy] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.752 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/harper] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.752 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/seo] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.753 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/learn] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.753 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/information] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.754 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/publish] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.754 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/discussions] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.754 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/publiclibrary] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.755 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/content] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.755 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/games] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.766 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/client] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.858 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/view] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.858 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/marketing] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:32.859 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/sqlutil] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.744 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/ibookso/products] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.763 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.767 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/folder/folderHome.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.768 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/libwonder/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.768 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/libwonder/_footer_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.769 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/libwonder/_navheader_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.769 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/pdfReader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.769 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_allSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.770 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_notesHighlightSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.770 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_printBookSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.770 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_resourceContents.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.771 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_security.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.771 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/createFlashCards.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.776 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/mobilePdfReader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.777 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/contactus.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.777 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_ebookInclude.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.777 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/ebook.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.778 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_videoSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.778 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_shareContent.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.779 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/displayFlashCards.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.779 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_wsOtherResource.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.780 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_bookTestSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.780 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/notesViewer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.781 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_weblinksSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.781 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_flashcardMatch.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.782 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_revision.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.783 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_displayChapterDetails.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.783 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/quizcreator.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.783 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_relatedBooks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.784 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/flpDemo.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.784 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/displayPdfMaterial.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.785 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_resourceChapterList.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.785 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/book.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.785 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/flashcardHome.css] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.788 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_cartScripts.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.790 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_navheader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.790 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_readSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.797 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/deleteChapters.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.800 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/flashCardHome.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.800 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/eBookDtl.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.800 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/createPdfBook.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.801 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_eBookSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.801 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_mobileResourceMenu.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.801 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_collectionBooks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.802 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_genericReader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.802 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_readerView.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.802 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_qaSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.802 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_eBookSection_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.803 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/previewChapter.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.803 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_ebookFeatures.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.803 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/editQuiz.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.804 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/pdfViewer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.804 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/epubReader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.804 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_bookGPTSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.804 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/robots.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.806 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/discountManager.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.806 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/searchDetails.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.807 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/printBooksDownloadPage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.808 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/videoExplanationUpdate.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.808 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/informationPage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.809 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/refundDetails.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.809 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/managePublishers.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.809 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/accessCodeUsage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.810 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/jwplayerLive.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.812 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/cartPurchase.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.812 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/list.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.812 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/contentModeration.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.815 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/publisherDsize.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.815 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/dataRetriever.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.816 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/informationType.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.816 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/manageBatchUsers.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.817 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/priceList.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.817 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/userBooks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.817 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/_pomodoro.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.818 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/manageBookExpiry.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.818 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/insertjob.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.818 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/manageNotifications.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.818 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/emailWhitelist.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.819 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/externalOrders.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.820 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/moderation.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.820 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/deleteuser.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.820 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/informationPageDtl.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.821 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/paymentDetails.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.821 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/bookgptPromptAdmin.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.822 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/_bookgptChatViewer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.822 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/_bookgptPDFViewer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.823 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/difficultyLevelMapping.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.825 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/salesDashboard/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.826 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/log/appVersionManagement.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.826 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/log/userAccess.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.827 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/log/unblockUser.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.827 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/log/quizissues.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.827 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/log/notificationManagement.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.828 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/log/liveTestRanks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.828 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/log/userManagement.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.829 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/log/enquiryDetails.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.829 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/log/enquiryForm.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.829 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/log/packageBooksReport.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.830 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/log/deleteUserBooks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.830 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/log/notification.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.830 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/log/migrateuser.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.831 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/karnataka.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.832 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/dailyCurrentAffairs.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.832 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/cacscma.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.832 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/ctet.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.833 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.833 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/prepJoyNew.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.834 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/enggentrances.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.834 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/quiz.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.834 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/_footer_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.835 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/affiliation.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.835 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/_navheader_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.835 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/dailyTest.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.836 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/currentaffairs.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.836 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/quizAnalytics.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.837 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/leaderBoard.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.837 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/_prepjoySignup.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.837 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/currentAffairsDaily.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.838 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/currentAffairs.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.838 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/neet.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.838 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/eBooks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.839 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/history.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.839 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/joinGame.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.839 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/prepJoyGame.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.840 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/audioChat.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.840 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/_prepjoy-loader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.840 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/join.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.841 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/creator.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.841 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/admin.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.842 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/_instituteBanners.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.843 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/usageReportInstituteAdmin.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.843 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/usageReport.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.843 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/ntse.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.844 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/manageInstitutePage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.844 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/isbnKeyword.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.844 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/home.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.845 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/userLoginLimit.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.845 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/admin.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.846 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/usageReportDoris.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.846 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.846 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/userManagement.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.847 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/manageClasses.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.847 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/registeredUserReport.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.847 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/libAdmin.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.848 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/recentInstitutes.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.848 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/libraryUserUploader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.848 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/usageReportTitleWise.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.849 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/downloadUsageReport.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.849 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/bookCreateNew.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.849 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/usageReportCorporateWise.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.850 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/instituteProduct.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.851 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/evidya/store.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.851 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/evidya/_footer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.851 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/evidya/privacy.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.852 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/evidya/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.852 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/evidya/_storeHolder.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.852 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/evidya/_commonfooter_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.853 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/evidya/store1.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.853 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/evidya/contact.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.853 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/evidya/about.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.854 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/evidya/_footer_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.854 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/evidya/_navheader_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.854 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/evidya/_navheader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.855 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/evidya/termsCondition.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.855 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/evidya/library.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.856 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/evidya/terms.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.856 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/whitelabel/_storeDisplay.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.857 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/whitelabel/_billingShipping.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.857 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/whitelabel/_shoppingCartDisplay.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.857 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/whitelabel/_shoppingCartLogic.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.858 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/whitelabel/_storeLogic.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.858 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/whitelabel/migrateBooks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.859 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/whitelabel/recharge.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.859 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/whitelabel/setBookType.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.860 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wolterskluwer/store.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.860 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wolterskluwer/_footer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.861 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wolterskluwer/_storeHolder.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.861 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wolterskluwer/_footer_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.861 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wolterskluwer/_navheader_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.862 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wolterskluwer/_navheader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.862 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/mtg/store.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.862 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/mtg/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.863 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/mtg/_footer_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.863 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/mtg/_navheader_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.864 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/appinapp/store.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.864 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/appinapp/_footer_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.864 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/appinapp/_navheader_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.865 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/oswaal/store.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.865 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/oswaal/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.866 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/oswaal/_footer_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.866 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/oswaal/_navheader_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.867 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/wrongPage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.867 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/packageBooks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.868 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/faq.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.868 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/eduWonder.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.869 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_home_reportSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.869 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/schoolProducts.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.869 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/sageLanding.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.870 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/books.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.870 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/store.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.870 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/description.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.871 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_leaderBoard.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.871 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/shoppingCart.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.872 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/home.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.872 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_moreOptions.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.872 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/ebooks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.873 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_footer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.873 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/contactus.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.873 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/ebook.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.874 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_shareContent.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.874 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_signup_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.874 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/vtulibrary.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.875 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/vtuanalytics.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.875 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.875 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/toppers.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.876 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/informationPage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.876 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_home_latestQuiz.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.876 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/eBooksStoreIntegration.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.877 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/mylibrary.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.877 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_wsabout.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.877 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/myHome.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.878 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/careercounselling.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.878 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_footer_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.878 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/book.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.879 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_signIn.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.879 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_home_sideBar.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.879 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_login.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.880 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_navheader_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.880 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/aboutus.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.881 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_navheader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.881 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_home_institutes.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.881 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_home_searchSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.882 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/landingPage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.882 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/bookdtl.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.882 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/checkout.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.883 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/dashboard.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.883 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_bannerSlider.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.883 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/leaderboard.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.884 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_pomodoro.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.884 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/directSales.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.885 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/appLink.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.885 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/vtubook.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.885 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/myActivity.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.886 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_bfooter.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.886 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/userOrderProcess.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.886 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/bookCreateNew.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.887 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/products.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.887 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_home_continueLearning.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.887 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/publishersProduct.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.888 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/informationPageDtl.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.888 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/vtu.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.888 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/teachersProduct.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.889 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/vtuadmin.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.889 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/studentproblems.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.890 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/manageExams.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.890 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/pdftoepub.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.890 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/blogs.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.891 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_ibookgpt-promotion.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.892 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sageUI/_footer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.892 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sageUI/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.893 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sageUI/authorDetails.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.893 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sageUI/_navheader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.893 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wpmain/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.894 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wpmain/myLibrary.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.894 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wpmain/unmarkedQA.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.895 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wpmain/_aiOptions.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.895 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wpmain/bookai.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.896 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/autoVideo/automatedVideo.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.896 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/autoVideo/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.896 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/autoVideo/autoImageVideo.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.897 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/resetPassword.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.898 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/instituteWelcomeEmail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.898 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/etextsSendContactUs.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.898 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/welcomeModal.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.899 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/invoice2020.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.899 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/ebouquetUser.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.899 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/_welcomeModal.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.900 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/ccavRequestHandler.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.900 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/_pageupload.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.900 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/radianBooksInvoice.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.901 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/wsinvoice.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.901 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/winnersInvoice.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.902 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/wolterskluwerSuggestbookemail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.902 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/userCreationEmailSage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.902 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/otpRequestEmail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.903 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/_loggedactivities.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.903 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/otpRequestEmailArihant.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.903 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/edugorillaInvoice.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.904 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/otpRequestEmailLibwonder.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.904 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/etextsSendFeedback.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.904 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/userCreationEmailEbouquet.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.905 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/userCreationEmailEvidya.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.905 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/resetPasswordEmail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.906 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/wsuser.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.906 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/userBookPurchaseEmail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.906 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/blackspineInvoice.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.907 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/ebouquetSuggestbookemail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.907 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/etextsSuggestbookemail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.907 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.908 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/resetPasswordEmailEtexts.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.908 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/ebouquetSuggestbookemailuser.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.908 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/userActiveCartEmail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.908 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/otpRequestEmailEbouquet.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.909 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/authordetails.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.909 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/userProfile.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.909 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/instituteUserEmail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.910 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/userPaidPreviewEmail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.910 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/jbclassInvoice.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.910 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/otpRequestEmailSage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.911 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/paymentPendingEmail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.911 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/userCreationEmailEtexts.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.911 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/enquiryFormEmail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.912 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/ebouquetRequestDemo.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.912 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/_pageaddquiz.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.912 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/_register.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.913 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/displayChapter.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.913 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/contentModerationEmailWS.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.913 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/mtgInvoice.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.913 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/oswaalInvoice.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.914 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/userPurchasedBookEmail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.914 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/affiliationEmail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.915 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/list.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.915 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/evidyaSuggestbookemail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.915 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/userBookPurchase.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.916 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/editProfile.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.916 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/_addTopic.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.916 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/_invoice.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.916 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/chapterDownloadError.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.917 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/_changePasswordModal.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.917 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/invoice.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.917 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/returnPolicy.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.918 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/addResource.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.918 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/_userProfile.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.918 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/oswalpublisherInvoice.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.919 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/ebouquetRetriever.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.919 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/externalPurchaseMail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.919 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/etextsSuggestbookemailuser.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.920 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/_pageaddlink.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.920 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/resetPasswordEmailSage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.920 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/evidyaSuggestbookemailuser.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.920 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/approveContent.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.921 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/_userProfile_prepjoy.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.921 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/userCreationEmail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.921 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/_contentCreator.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.922 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/_userProfileSage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.922 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/arihantInvoice.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.922 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/addUser.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.923 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/privateLabelInvoice.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.923 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/prepjoyInvoice.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.923 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/resetPasswordEmailEvidya.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.924 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/_userProfile_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.924 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/purchaseConfirmationEmail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.924 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/appInAppInvoice.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.926 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/bookPrice/_priceManager.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.927 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/oswalpublisher/store.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.927 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/oswalpublisher/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.928 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/oswalpublisher/_footer_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.928 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/oswalpublisher/_navheader_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.929 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/deliveryCharges/manageDeliveryCharges.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.929 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/printbooks/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.929 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/printbooks/_categories.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.930 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/printbooks/printbooksmanagement.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.930 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/printbooks/_printSearch.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.931 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/mail/activitiesUpdate.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.931 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/mail/printbooks] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.931 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/mail/sendInvite.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.932 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/mail/printbooks/printbooksmanagement.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.932 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/pageManager.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.933 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/store.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.933 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/admin.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.933 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/allWebsites.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.934 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/contactus.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.934 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.934 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/ibookso.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.935 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/customPageCreation.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.935 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/_footer_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.935 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/_otpOnlyLogin.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.936 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/_navheader_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.936 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/_ibooksoBanner.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.936 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/createNew.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.936 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/wileySignup.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.937 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/customPage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.937 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/deleteAccount.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.937 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/cuetAcademics.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.938 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/loginPage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.938 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/knimbus.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.938 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/cart.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.939 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/gptsir.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.939 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/accessCodeBooks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.939 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/addSite.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.940 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/listSites.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.941 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/radianbooks/store.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.941 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/radianbooks/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.941 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/radianbooks/_footer_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.942 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/radianbooks/_navheader_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.942 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_mobileSignup.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.942 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_plindex.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.943 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/notescreator.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.943 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_wpfooter.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.943 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_mobileLogin.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.944 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_topicscripts.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.944 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/flpReportDownload.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.944 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/profile.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.945 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/home.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.945 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/success.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.945 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_footer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.946 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_mcq.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.946 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/signUp.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.946 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/privacy.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.947 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.947 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_mainheader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.947 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_tandc.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.947 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_topicdisplay.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.948 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/findFriends.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.948 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/mylibrary.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.948 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_mcqalt.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.949 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/forgotpassword.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.949 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/quizcreator.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.949 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/topic.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.949 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/groupdtl.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.950 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/facebook.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.950 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/quiz.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.950 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/signIn.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.951 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_mta.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.951 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/book.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.951 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_chaptersModal.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.952 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/tour.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.952 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_signIn.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.952 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_wsindex.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.953 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/recent.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.953 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_topicinclude.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.953 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/test.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.953 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/aboutus.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.954 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_navheader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.954 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/futureLearningProgram.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.954 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/groups.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.955 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/careers.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.955 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/termsandconditions.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.955 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_tof.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.956 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_fib.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.956 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_opp.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.956 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_signupNew.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.956 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_pnavheader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.957 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/renderContent.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.957 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/renderMCQContent.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.957 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/deleteAccount.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.958 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instituteDashboard/doubts.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.959 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instituteDashboard/_footer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.959 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instituteDashboard/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.959 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instituteDashboard/reports.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.960 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instituteDashboard/classes.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.960 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instituteDashboard/students.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.960 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instituteDashboard/_navheader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.960 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instituteDashboard/teachers.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.961 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/groups/postDetail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.961 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/groups/groupReport.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.962 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/groups/members.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.962 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/groups/groupDtl.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.962 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/groups/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.963 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/groups/_signIn.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.963 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/groups/groupCreate.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.963 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/groups/reported.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.963 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/groups/memberRequests.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.964 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderslatekids/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.965 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/toDo/toDoListAndUpdate.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.965 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/testgenerator/_testgen.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.965 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/testgenerator/monthlyQuiz.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.966 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepJoy/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.966 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepJoy/prepJoyNew.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.966 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepJoy/_navheader_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.967 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepJoy/currentAffairsDaily.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.967 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepJoy/currentAffairs.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.968 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/analytics/reportDetails.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.968 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/analytics/leaderBoardForAdmins.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.968 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/support/getAllPayments.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.969 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/support/addUser.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.969 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/support/findScratchCode.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.969 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wlibrary/library.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.970 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wsshop/validityExtensionAdmin.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.970 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wsshop/cart.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.970 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wsshop/amazonorderconfirmation.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.971 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wsshop/_cartScripts.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.971 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wsshop/_searchScripts.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.971 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wsshop/manageShopSpecials.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.972 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wsshop/orderManagement.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.972 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wsshop/_shoppingCartLogic.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.972 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wsshop/blogEnglish.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.973 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wsshop/blogHindi.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.973 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wsshop/addGradeInfo.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.973 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wsshop/_videoCreator.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.974 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wsshop/_blogEnglishDisplay.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.974 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/messaging/messages.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.975 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/discussionBoardAdmin/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.975 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/eutkarsh/store.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.976 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/eutkarsh/_footer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.976 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/eutkarsh/_tandc.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.976 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/eutkarsh/_wsabout.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.977 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/eutkarsh/contact.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.977 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/eutkarsh/_footer_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.977 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/eutkarsh/eutkarshrefund.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.978 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/eutkarsh/_navheader_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.978 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/eutkarsh/_navheader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.978 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/eutkarsh/eutkarshprivacy.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.979 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/eutkarsh/eutkarshterms.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.979 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/intelligence/demo.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.979 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/intelligence/admin.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.980 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/intelligence/gptContent.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.980 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/intelligence/bookChapters.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.980 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/intelligence/chat.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.981 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_gradeSelector.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.981 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_addnotes.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.982 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/pdfReader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.982 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_bookdetailsTab.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.982 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/notescreator.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.982 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_allSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.983 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_notesHighlightSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.983 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/mybooks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.983 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/instructorLibrary.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.983 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/bookCreate.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.984 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_testgenModal.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.984 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_materialTabs.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.984 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/openPDF.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.985 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/demoHome.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.985 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_bookReviewModal.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.985 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_testgeneratorscripts.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.986 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_footer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.986 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_answerMatchModal.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.986 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_additionalReferences.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.986 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/resetPasswordEmail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.987 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/uploadTex.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.987 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_wsFooter.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.987 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_videoSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.988 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/wseditor.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.988 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.988 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_weblinksSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.988 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/orders.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.989 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/qandaCreator.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.989 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_commonfooter_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.989 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/bannerManagement.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.990 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_loginChecker.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.990 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_revision.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.990 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_displayChapterDetails.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.990 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/mylibrary.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.991 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/pubDesk.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.991 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/quizcreator.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.991 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_addNotesScripts.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.992 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_additionalReferenceSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.992 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_bookanalytics.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.992 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_bookChaptersTab.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.992 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/indexHome.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.993 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_quizSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.993 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/authorDetails.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.993 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_bookReviews.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.993 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/book.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.994 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_flashCardSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.994 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_commonfooter.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.994 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/answerFixerInput.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.995 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/selfService.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.995 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/answerFixerProcessor.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.995 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_buyOrAdd.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.996 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/relatedVideosAdmin.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.996 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/testgenerator.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.996 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/answerMatch.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.996 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_navheader_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.997 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_discussform.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.997 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_navheader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.997 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_uploadResourcesModal.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.997 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_gradeSelectorScript.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.998 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/pubSales.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.998 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_reviewrating.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.998 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_booksHolder.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.999 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/manageTabs.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.999 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/bookdtl.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.999 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_discforum.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:33.999 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_slider.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.000 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/independentContent.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.000 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/quizcreatorbulkinput.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.001 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_buyChapters.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.001 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/quizcreatorbulk.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.001 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_booksHolderTemplate.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.001 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/videoplayer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.002 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/studySet.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.002 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/bookCreateNew.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.002 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/arihant.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.003 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/errorPage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.003 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_wonderGoaStudySet.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.003 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_videoPlay.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.003 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/videoExplanation.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.004 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_readerView.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.004 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_qaSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.004 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/manageExams.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.005 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_readingSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.005 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/deleteAccount.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.005 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_bookGPTTab.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.006 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/qandacreator.js] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.007 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/layouts/main.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.008 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/dashboard/sales.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.008 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/dashboard/profile.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.008 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/dashboard/contentCreators.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.009 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/dashboard/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.009 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/dashboard/reports.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.009 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/dashboard/users.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.009 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/dashboard/classes.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.010 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/dashboard/students.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.010 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/dashboard/content.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.010 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/dashboard/teachers.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.011 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/dashboard/amazonLink.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.011 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/dashboard/test.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.011 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/dashboard/fixBrokenResources.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.012 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/progress/progressReport.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.012 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/libraryBooks/myLibrary.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.013 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/libraryBooks/_publishersList.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.013 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/etexts/store.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.013 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/etexts/help.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.014 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/etexts/_footer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.014 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/etexts/privacy.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.014 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/etexts/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.015 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/etexts/_storeHolder.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.015 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/etexts/_commonfooter_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.015 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/etexts/contact.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.016 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/etexts/_footer_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.016 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/etexts/_navheader_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.016 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/etexts/_navheader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.016 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/etexts/packages.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.017 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/etexts/feedback.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.017 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/etexts/termsCondition.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.017 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/etexts/library.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.018 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/etexts/terms.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.018 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/default/discussionBoard.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.019 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/default/chapterDetails.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.019 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/default/jsonPage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.019 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/default/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.019 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/default/remote.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.020 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/default/_showResult.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.020 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/usermanagement/myTracker.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.021 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/usermanagement/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.021 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/usermanagement/specimenCopyRequests.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.021 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/usermanagement/orders.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.021 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/usermanagement/favouriteMcqs.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.022 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/usermanagement/teachersNomineeForm.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.022 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/usermanagement/teachersPollResult.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.022 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/usermanagement/addUserAndBooks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.023 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/usermanagement/_ordersListInclude.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.023 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/usermanagement/editprofile.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.023 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/usermanagement/nominations.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.023 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/usermanagement/nomineeDetails.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.024 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/publisherManagement/addPublisher.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.024 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/publisherManagement/publisherReport.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.025 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/publisherManagement/publisher.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.025 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/reports/externalReportInput.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.025 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/reports/reports.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.026 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/reports/instituteReport.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.026 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/reports/_navheader_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.026 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/reports/pubSales.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.027 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/reports/getBulkUsersAddedReportInput.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.027 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/reports/scratchCardReport.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.027 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resourceCreator/mergeQuizzes.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.028 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resourceCreator/mcqSorter.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.028 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resourceCreator/addNotes.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.028 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resourceCreator/sectionModifier.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.029 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/excel/addBulkUsersAndBooks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.029 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/excel/formulaFixer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.029 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/excel/bookInput.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.030 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/excel/fileUploader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.030 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/excel/_fileUploaderInclude.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.030 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/excel/imageFixer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.030 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/excel/quizExtractor.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.031 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/excel/uploadMCQ.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.031 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/excel/extractPDF.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.031 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/excel/upload.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.032 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/excel/htmlClassExtractor.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.032 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/excel/wordUpload.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.032 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/excel/exportmcqspage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.032 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/excel/uploadQA.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.033 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/_instructorResourcesContent.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.033 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/faq.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.034 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/studentResourcesLocked.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.034 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/_userlogin.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.034 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/_footer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.035 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/ebook.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.035 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/privacy.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.035 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.035 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/instructorResources.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.036 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/instructorResourcesAdmin.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.036 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/_studentResourcesLocked.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.036 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/instructorResourcesLocked.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.037 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/aboutus.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.037 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/_navheader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.037 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/register.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.037 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/_askAuthorModal.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.038 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/verification.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.038 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/disciplines.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.038 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/doris.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.038 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/_additionalStudentInfo.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.039 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/terms.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.039 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wsLibrary/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.040 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wsLibrary/myLibrary.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.040 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wsLibrary/accessCode.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.040 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/document/create.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.041 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/document/list.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.041 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/document/excelUpload.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.042 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/faq.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.042 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/store.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.042 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/ebouquetReviewerLogin.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.042 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/virtualLibrary.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.043 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.043 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/_storeHolder.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.043 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/_commonfooter_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.043 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/information.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.044 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/contact.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.044 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/_accessCodeLogin.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.044 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/_footer_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.045 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/howItWorks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.045 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/termsOfUse.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.045 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/_navheader_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.045 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/_navheader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.046 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/privacyPolicy.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.046 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/decision.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.046 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/requestDemo.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.046 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/cookies.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.047 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/printOrderManagement/orderDetails.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.048 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/mocktests/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.048 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/mocktests/_relatedBooksAndLeaderboard.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.048 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/mocktests/listExams.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.048 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/mocktests/_topLevelTests.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.049 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/mocktests/examPage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.049 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/mocktests/_mockTestFaq.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.050 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/articles/showGradeBooks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.050 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/articles/getGradeBooks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.050 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/articles/showBlogPages.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.051 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/affiliationAdmin/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.051 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/affiliationAdmin/categoryManager.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.051 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/affiliationAdmin/searchResults.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.052 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/list.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.052 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/bookgpt.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.052 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/_bookgptChatViewer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.053 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/_bookgptPDFViewer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.053 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/showGptLogsForResId.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.053 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/showGptLogsForFeedback.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.053 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/showGptLogsForUser.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.054 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/manualGpt.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.054 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/_bookgptPDFViewerMobile.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.054 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/_bookgptChatViewerMobile.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.055 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/myDriveReader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.055 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/_driveChat.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.055 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/_driveMobileReader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.056 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/pdfReader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.056 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/_driveMobileChat.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.056 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/_driveReader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.056 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/_chatModule.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.057 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/_codeRunner.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.057 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/_bookgptContentHolder.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.058 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/bookmark/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.058 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/digitalLibrary/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.058 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/digitalLibrary/showCategory.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.059 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/digitalLibrary/_categorySection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.059 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/digitalLibrary/adminIndex.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.060 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/autogpt/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.060 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/autogpt/promptTemplateManager.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.060 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/autogpt/duplicateFixer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.060 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/autogpt/kindleTemplate.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.061 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/finance/salesReportByPublisher.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.061 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/finance/salesReportForAccounts.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.062 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/partner/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.062 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/studyMaterial/_copilot.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.062 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/studyMaterial/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.063 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/webInteraction/manageLinks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.063 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/webInteraction/_aira.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.063 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/webInteraction/pageInteractions.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.064 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ibookgpt/howItWorks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.064 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ibookgpt/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.064 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ibookgpt/_footer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.065 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ibookgpt/contact.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.065 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ibookgpt/faq.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.065 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ibookgpt/_navheader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.065 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ibookgpt/benefits.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.066 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ibookgpt/features.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.066 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ibookgpt/printBookBundling.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.066 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sdk/sdkIntegrationReport.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.067 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instManager/listBatches.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.067 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instManager/listUsersInBatch.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.067 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instManager/addInstitute.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.068 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instManager/editUser.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.068 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instManager/getBatch.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.068 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instManager/adminDashboard.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.068 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instManager/listInstitutes.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.069 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instManager/listBooksInBatch.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.069 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instManager/assignUserToBatch.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.069 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instManager/assignBookToBatch.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.070 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instManager/editBatch.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.070 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instManager/editCourse.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.070 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instManager/createBatch.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.070 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instManager/listCourses.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.071 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instManager/createCourse.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.071 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instManager/manageInstitutePrompts.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.072 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/promptLanguages/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.072 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/onlineTest/testQuestionAnalytics.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.072 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/onlineTest/blockStudents.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.073 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/onlineTest/listTests.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.073 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/onlineTest/testResults.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.073 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/onlineTest/viewQuestions.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.074 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/pdfExtractor/mcqExtractor.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.074 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/pdfExtractor/_api_modal.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.074 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/pdfExtractor/createSolution.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.075 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/contentdelivery/getPdfFile.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.075 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/contentdelivery/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.075 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/contentdelivery/cookieTest.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.076 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/questionPaper/createQuestionPaperPage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.076 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/questionPaper/listQuestionPapers.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.076 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/questionPaper/viewQuestionPaper.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.077 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/questionPaper/addQuestionType.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.077 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/questionPaper/viewPattern.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.077 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/questionPaper/listPatterns.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.078 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/questionPaper/addSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.078 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/questionPaper/printQuestionPaper.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.078 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/aireport/dashboard.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.079 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/questionTypes/create.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.079 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/questionTypes/list.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.080 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderslate/LoginFilters.groovy] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.080 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/spring/resources.groovy] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:34.086 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp/views.properties] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
06-Jun-2025 03:59:44.249 INFO [ws-startStop-1] org.apache.jasper.servlet.TldScanner.scanJars At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
06-Jun-2025 03:59:46.420 INFO [ws-startStop-1] org.apache.catalina.core.ApplicationContext.log 2 Spring WebApplicationInitializers detected on classpath

Configuring Spring Security Core ...
... finished configuring Spring Security Core


Configuring Spring Security REST 2.0.0.M2...
... finished configuring Spring Security REST

	... with GORM support
06-Jun-2025 04:01:36.327 INFO [ws-startStop-1] org.apache.catalina.startup.HostConfig.deployDescriptor Deployment of configuration descriptor /u01/apache-tomcat-8.5.13/conf/Catalina/ws/ROOT.xml has finished in 137,577 ms
06-Jun-2025 04:01:36.335 INFO [ws-startStop-1] org.apache.catalina.startup.HostConfig.deployWAR Deploying web application archive /u01/apache-tomcat-8.5.13/webapps/wonderlive.war
06-Jun-2025 04:01:41.452 INFO [ws-startStop-1] org.apache.catalina.core.ApplicationContext.log 2 Spring WebApplicationInitializers detected on classpath

  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
 :: Spring Boot ::        (v2.2.2.RELEASE)

2025-06-06 04:01:43.825  INFO 4160652 --- [ ws-startStop-1] com.wonderslate.chat.ServletInitializer  : Starting ServletInitializer v0.0.1-SNAPSHOT on ip-172-31-30-27.us-west-2.compute.internal with PID 4160652 (/u01/apache-tomcat-8.5.13/webapps/wonderlive/WEB-INF/classes started by root in /u01/apache-tomcat-8.5.13/bin)
2025-06-06 04:01:43.846  INFO 4160652 --- [ ws-startStop-1] com.wonderslate.chat.ServletInitializer  : No active profile set, falling back to default profiles: default
HelloJob says hello at Fri Jun 06 04:01:45 UTC 2025 in production
06-Jun-2025 04:01:47.766 INFO [ws-startStop-1] org.apache.catalina.core.ApplicationContext.log Initializing Spring embedded WebApplicationContext
2025-06-06 04:01:47.775  INFO 4160652 --- [ ws-startStop-1] o.s.web.context.ContextLoader            : Root WebApplicationContext: initialization completed in 3820 ms
2025-06-06 04:01:48.705  INFO 4160652 --- [ ws-startStop-1] o.s.s.concurrent.ThreadPoolTaskExecutor  : Initializing ExecutorService 'clientInboundChannelExecutor'
2025-06-06 04:01:48.710  INFO 4160652 --- [ ws-startStop-1] o.s.s.concurrent.ThreadPoolTaskExecutor  : Initializing ExecutorService 'clientOutboundChannelExecutor'
2025-06-06 04:01:48.854  INFO 4160652 --- [ ws-startStop-1] o.s.s.c.ThreadPoolTaskScheduler          : Initializing ExecutorService 'messageBrokerTaskScheduler'
2025-06-06 04:01:49.094  INFO 4160652 --- [ ws-startStop-1] o.s.s.concurrent.ThreadPoolTaskExecutor  : Initializing ExecutorService 'brokerChannelExecutor'
2025-06-06 04:01:50.550  WARN 4160652 --- [ ws-startStop-1] o.s.b.a.f.FreeMarkerAutoConfiguration    : Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-06-06 04:01:50.838  INFO 4160652 --- [ ws-startStop-1] o.s.m.s.b.SimpleBrokerMessageHandler     : Starting...
2025-06-06 04:01:50.841  INFO 4160652 --- [ ws-startStop-1] o.s.m.s.b.SimpleBrokerMessageHandler     : BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [DefaultSubscriptionRegistry[cache[0 destination(s)], registry[0 sessions]]]]
2025-06-06 04:01:50.843  INFO 4160652 --- [ ws-startStop-1] o.s.m.s.b.SimpleBrokerMessageHandler     : Started.
2025-06-06 04:01:50.861  INFO 4160652 --- [ ws-startStop-1] com.wonderslate.chat.ServletInitializer  : Started ServletInitializer in 8.765 seconds (JVM running for 154.032)
06-Jun-2025 04:01:50.900 INFO [ws-startStop-1] org.apache.catalina.startup.HostConfig.deployWAR Deployment of web application archive /u01/apache-tomcat-8.5.13/webapps/wonderlive.war has finished in 14,564 ms
06-Jun-2025 04:01:50.903 INFO [ws-startStop-1] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory /u01/apache-tomcat-8.5.13/webapps/.well-known
06-Jun-2025 04:01:50.935 INFO [ws-startStop-1] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory /u01/apache-tomcat-8.5.13/webapps/.well-known has finished in 31 ms
06-Jun-2025 04:01:50.935 INFO [ws-startStop-1] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory /u01/apache-tomcat-8.5.13/webapps/tools
06-Jun-2025 04:01:50.976 INFO [ws-startStop-1] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory /u01/apache-tomcat-8.5.13/webapps/tools has finished in 41 ms
06-Jun-2025 04:01:50.995 INFO [main] org.apache.coyote.AbstractProtocol.start Starting ProtocolHandler ["ajp-nio-8009"]
06-Jun-2025 04:01:51.027 INFO [main] org.apache.catalina.startup.Catalina.start Server startup in 152340 ms
HelloJob says hello at Fri Jun 06 04:02:45 UTC 2025 in production
2025-06-06 04:02:49.087  INFO 4160652 --- [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats    : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
06-Jun-2025 04:03:02.968 INFO [ajp-nio-8009-exec-2] org.apache.tomcat.util.http.parser.Cookie.logInvalidHeader A cookie header was received [Jun 06 2025 09:05:12 GMT+0530 (India Standard Time); _ga_2Y9D9ELSN2=GS2.1.s1749180911$o1296$g1$t1749181248$j60$l0$h0; pomodoroCurrentDuration=138; daysDuration=58] that contained an invalid cookie. That cookie will be ignored.Note: further occurrences of this error will be logged at DEBUG level.
Executing SQL: select scad.book_id bookId,bm.cover_image,bm.title,(select name from publishers where id=bm.publisher_id) publisherName,bm.price,bm.tests_price,scad.book_type,bm.upgrade_price,scad.subscription_id,scad.subs_duration,scad.subs_starting_book_id,bm.book_type baseBookType,bm.publisher_id  from  shopping_cart_active_dtl scad ,books_mst bm where  bm.id=scad.book_id and    scad.username='1B0D6823BD170F1D43E7AB2246D31473F_temp' and scad.site_id='1' order by scad.id Desc 
here B0D6823BD170F1D43E7AB2246D31473F
Executing SQL: select scad.book_id bookId,bm.cover_image,bm.title,(select name from publishers where id=bm.publisher_id) publisherName,bm.price,bm.tests_price,scad.book_type,bm.upgrade_price,scad.subscription_id,scad.subs_duration,scad.subs_starting_book_id,bm.book_type baseBookType,bm.publisher_id  from  shopping_cart_active_dtl scad ,books_mst bm where  bm.id=scad.book_id and    scad.username='1BF4F64241C936110AAECC73FD1698852_temp' and scad.site_id='1' order by scad.id Desc 
Executing SQL:  SELECT bud.batch_id, im.id institute_id, im.name,im.publisher_id publisherId,im.logo,im.edu_wonder,cbd.name batchName,im.full_library_view,cbd.syllabus,cbd.grade,im.level,im.syllabus instituteSyllabus,im.drive_for_instructor,drive_for_student,ra_for_instructor,ra_for_student,show_reference_section,bud.user_type,im.enable_test,im.enable_analytics,im.enable_question_paper  FROM wsuser.batch_user_dtl bud, wsuser.course_batches_dtl cbd, wsuser.institute_mst im  where im.site_id=1 and bud.username='<EMAIL>'  and cbd.id=bud.batch_id and im.id=cbd.conducted_by  and cbd.status='active' and (cbd.start_date <= SYSDATE() or cbd.start_date is null)  union  select cbd.id batch_id, im.id institute_id, im.name,im.publisher_id publisherId,im.logo,im.edu_wonder,cbd.name batchName,im.full_library_view,cbd.syllabus,cbd.grade,im.level,im.syllabus instituteSyllabus,im.drive_for_instructor,drive_for_student,ra_for_instructor,ra_for_student,show_reference_section,'false' user_type,im.enable_test,im.enable_analytics,im.enable_question_paper from wsuser.institute_mst im, wsuser.course_batches_dtl cbd, wsuser.institute_ip_address iia  where iia.ip_address= '**************' and im.id =iia.institute_id and  im.id=cbd.conducted_by  and cbd.status='active' and (cbd.start_date <= SYSDATE() or cbd.start_date is null) and im.site_id=1
[batchId:200, id:143, name:New Test Institute, publisherId:169, batchName:Default, fullLibraryView:true, syllabus:null, grade:null, logo:1.png, isEduWonder:null, isInstructor:false, level:null, instituteSyllabus:null, driveForInstructor:null, driveForStudent:null, raForInstructor:null, raForStudent:null, showReferenceSection:null, enableTest:null, enableAnalytics:null, enableQuestionPaper:null, userType:null]
[batchId:273, id:213, name:R V College, publisherId:109, batchName:Default, fullLibraryView:null, syllabus:null, grade:null, logo:null, isEduWonder:null, isInstructor:false, level:null, instituteSyllabus:null, driveForInstructor:null, driveForStudent:null, raForInstructor:null, raForStudent:null, showReferenceSection:null, enableTest:null, enableAnalytics:null, enableQuestionPaper:null, userType:null]
[batchId:342, id:270, name:Pavithra New Institute July, publisherId:129, batchName:Default, fullLibraryView:true, syllabus:null, grade:null, logo:null, isEduWonder:null, isInstructor:false, level:null, instituteSyllabus:null, driveForInstructor:null, driveForStudent:null, raForInstructor:null, raForStudent:null, showReferenceSection:null, enableTest:null, enableAnalytics:null, enableQuestionPaper:null, userType:null]
[batchId:401, id:312, name:New Institute with free and paid book non IP, publisherId:170, batchName:Default, fullLibraryView:true, syllabus:null, grade:null, logo:null, isEduWonder:false, isInstructor:false, level:, instituteSyllabus:, driveForInstructor:null, driveForStudent:null, raForInstructor:null, raForStudent:null, showReferenceSection:null, enableTest:null, enableAnalytics:null, enableQuestionPaper:null, userType:null]
[batchId:472, id:368, name:New Eduwonder full library 14th, publisherId:206, batchName:Default, fullLibraryView:true, syllabus:null, grade:null, logo:E-vidyaimage.png, isEduWonder:true, isInstructor:false, level:, instituteSyllabus:, driveForInstructor:null, driveForStudent:null, raForInstructor:null, raForStudent:null, showReferenceSection:null, enableTest:null, enableAnalytics:null, enableQuestionPaper:null, userType:null]
[batchId:473, id:368, name:Class 10, publisherId:206, batchName:Class 10, fullLibraryView:true, syllabus:null, grade:null, logo:E-vidyaimage.png, isEduWonder:true, isInstructor:false, level:, instituteSyllabus:, driveForInstructor:null, driveForStudent:null, raForInstructor:null, raForStudent:null, showReferenceSection:null, enableTest:null, enableAnalytics:null, enableQuestionPaper:null, userType:null]
Executing SQL: select max(date_created) max_date ,book_id 
from wslog.books_view_dtl 
where username='<EMAIL>' and institute_id is not null 
group by book_id
order by max_date desc limit 8
Executing SQL: select u.username from wsuser.user u,wsuser.user_role ur,wsuser.role r where r.authority='ROLE_BOOK_CREATOR' and ur.role_id=r.id and ur.user_id=u.id and u.site_id=1
HelloJob says hello at Fri Jun 06 04:03:45 UTC 2025 in production
HelloJob says hello at Fri Jun 06 04:04:45 UTC 2025 in production
HelloJob says hello at Fri Jun 06 04:05:45 UTC 2025 in production
siteName=prepjoy
Executing SQL: select scad.book_id bookId,bm.cover_image,bm.title,(select name from publishers where id=bm.publisher_id) publisherName,bm.price,bm.tests_price,scad.book_type,bm.upgrade_price,scad.subscription_id,scad.subs_duration,scad.subs_starting_book_id,bm.book_type baseBookType,bm.publisher_id  from  shopping_cart_active_dtl scad ,books_mst bm where  bm.id=scad.book_id and    scad.username='272DDF3EC3BE6C3B68979438F568F61644_temp' and scad.site_id='27' order by scad.id Desc 
Executing SQL: SELECT distinct(btd.level) FROM wsshop.books_tag_dtl btd, books_mst bm,levels_mst lm where bm.site_id in(0,1,2,3,8,10,11,13,14,15,17,18,26,27,28,29,30,31,32,33,35,36,37,38,39,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,80,90,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,114,115,129) and bm.id=btd.book_id  and bm.status='published' and lm.site_id=1 and lm.name=btd.level  order by level
Executing SQL: SELECT level,syllabus FROM wsshop.books_tag_dtl btd, books_mst bm,levels_mst lm  where bm.site_id in(0,1,2,3,8,10,11,13,14,15,17,18,26,27,28,29,30,31,32,33,35,36,37,38,39,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,80,90,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,114,115,129) and bm.id=btd.book_id  and bm.status='published' and lm.site_id=1 and btd.level=lm.name  group by level,syllabus order by level,syllabus
Executing SQL: select bm.id,bm.image_name,bm.image_path,COALESCE(bm.book_id,''),bm.image_path_mobile,COALESCE(bms.title,''),bm.action  from  banners_mst bm LEFT JOIN  books_mst bms  ON  bm.book_id=bms.id where  bm.publisher_id is null and  bm.institute_id is  null and bm.site_id='27'   order by bm.id Desc
HelloJob says hello at Fri Jun 06 04:06:45 UTC 2025 in production
HelloJob says hello at Fri Jun 06 04:07:45 UTC 2025 in production
HelloJob says hello at Fri Jun 06 04:08:45 UTC 2025 in production
HelloJob says hello at Fri Jun 06 04:09:45 UTC 2025 in production
HelloJob says hello at Fri Jun 06 04:10:45 UTC 2025 in production
2025-06-06 04:11:01.671 ERROR --- [nio-8009-exec-8] StackTrace                               : Full Stack Trace:

java.lang.NumberFormatException: For input string: "null"
	at java.lang.NumberFormatException.forInputString(NumberFormatException.java:65)
	at java.lang.Integer.parseInt(Integer.java:580)
	at java.lang.Integer.<init>(Integer.java:867)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at org.codehaus.groovy.reflection.CachedConstructor.invoke(CachedConstructor.java:83)
	at org.codehaus.groovy.runtime.callsite.ConstructorSite$ConstructorSiteNoUnwrapNoCoerce.callConstructor(ConstructorSite.java:105)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCallConstructor(CallSiteArray.java:60)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:235)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:247)
	at com.wonderslate.data.PromptService.$tt__checkIsTeacher(PromptService.groovy:890)
	at com.wonderslate.data.PromptService$_checkIsTeacher_closure26.doCall(PromptService.groovy)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:93)
	at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:325)
	at org.codehaus.groovy.runtime.metaclass.ClosureMetaClass.invokeMethod(ClosureMetaClass.java:294)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1027)
	at groovy.lang.Closure.call(Closure.java:414)
	at groovy.lang.Closure.call(Closure.java:430)
	at grails.transaction.GrailsTransactionTemplate$2.doInTransaction(GrailsTransactionTemplate.groovy:96)
	at org.springframework.transaction.support.TransactionTemplate.execute(TransactionTemplate.java:133)
	at grails.transaction.GrailsTransactionTemplate.execute(GrailsTransactionTemplate.groovy:93)
	at com.wonderslate.data.PromptService.checkIsTeacher(PromptService.groovy)
	at com.wonderslate.data.PromptService$checkIsTeacher$0.call(Unknown Source)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:48)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:113)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:141)
	at com.wonderslate.data.PromptController.$tt__getDefaultPromptListForResource(PromptController.groovy:1379)
	at com.wonderslate.data.PromptController$_getDefaultPromptListForResource_closure30.doCall(PromptController.groovy)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:93)
	at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:325)
	at org.codehaus.groovy.runtime.metaclass.ClosureMetaClass.invokeMethod(ClosureMetaClass.java:294)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1027)
	at groovy.lang.Closure.call(Closure.java:414)
	at groovy.lang.Closure.call(Closure.java:430)
	at grails.transaction.GrailsTransactionTemplate$2.doInTransaction(GrailsTransactionTemplate.groovy:96)
	at org.springframework.transaction.support.TransactionTemplate.execute(TransactionTemplate.java:133)
	at grails.transaction.GrailsTransactionTemplate.execute(GrailsTransactionTemplate.groovy:93)
	at com.wonderslate.data.PromptController.getDefaultPromptListForResource(PromptController.groovy)
	at org.grails.core.DefaultGrailsControllerClass$MethodHandleInvoker.invoke(DefaultGrailsControllerClass.java:223)
	at org.grails.core.DefaultGrailsControllerClass.invoke(DefaultGrailsControllerClass.java:188)
	at org.grails.web.mapping.mvc.UrlMappingsInfoHandlerAdapter.handle(UrlMappingsInfoHandlerAdapter.groovy:90)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:963)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:897)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:970)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:861)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:635)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:846)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:742)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:230)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:165)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:52)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:192)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:165)
	at javax.servlet.FilterChain$doFilter.call(Unknown Source)
	at grails.plugin.springsecurity.rest.RestLogoutFilter.doFilter(RestLogoutFilter.groovy:80)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:192)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:165)
	at org.springframework.boot.web.filter.ApplicationContextHeaderFilter.doFilterInternal(ApplicationContextHeaderFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:192)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:165)
	at org.springframework.boot.actuate.trace.WebRequestTraceFilter.doFilterInternal(WebRequestTraceFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:192)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:165)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:317)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:127)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:91)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:115)
	at grails.plugin.springsecurity.web.UpdateRequestContextHolderExceptionTranslationFilter.doFilter(UpdateRequestContextHolderExceptionTranslationFilter.groovy:64)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:115)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at javax.servlet.FilterChain$doFilter.call(Unknown Source)
	at grails.plugin.springsecurity.rest.RestTokenValidationFilter.processFilterChain(RestTokenValidationFilter.groovy:122)
	at sun.reflect.GeneratedMethodAccessor715.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.codehaus.groovy.runtime.callsite.PogoMetaMethodSite$PogoCachedMethodSiteNoUnwrapNoCoerce.invoke(PogoMetaMethodSite.java:210)
	at org.codehaus.groovy.runtime.callsite.PogoMetaMethodSite.callCurrent(PogoMetaMethodSite.java:59)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callCurrent(AbstractCallSite.java:190)
	at grails.plugin.springsecurity.rest.RestTokenValidationFilter.doFilter(RestTokenValidationFilter.groovy:90)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at grails.plugin.springsecurity.web.filter.GrailsAnonymousAuthenticationFilter.doFilter(GrailsAnonymousAuthenticationFilter.groovy:53)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at org.springframework.security.web.authentication.rememberme.RememberMeAuthenticationFilter.doFilter(RememberMeAuthenticationFilter.java:150)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:169)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at javax.servlet.FilterChain$doFilter.call(Unknown Source)
	at grails.plugin.springsecurity.rest.RestAuthenticationFilter.doFilter(RestAuthenticationFilter.groovy:143)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at grails.plugin.springsecurity.web.authentication.logout.MutableLogoutFilter.doFilter(MutableLogoutFilter.groovy:62)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at grails.plugin.springsecurity.web.SecurityRequestHolderFilter.doFilter(SecurityRequestHolderFilter.groovy:58)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:214)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:177)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:192)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:165)
	at org.grails.web.servlet.mvc.GrailsWebRequestFilter.doFilterInternal(GrailsWebRequestFilter.java:77)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:192)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:165)
	at org.grails.web.filters.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:67)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:192)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:165)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:197)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:192)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:165)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:192)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:165)
	at org.springframework.boot.actuate.autoconfigure.MetricsFilter.doFilterInternal(MetricsFilter.java:106)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:192)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:165)
	at org.springframework.boot.web.support.ErrorPageFilter.doFilter(ErrorPageFilter.java:117)
	at org.springframework.boot.web.support.ErrorPageFilter.access$000(ErrorPageFilter.java:61)
	at org.springframework.boot.web.support.ErrorPageFilter$1.doFilterInternal(ErrorPageFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.springframework.boot.web.support.ErrorPageFilter.doFilter(ErrorPageFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:192)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:165)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:198)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:478)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:140)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:80)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:87)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:341)
	at org.apache.coyote.ajp.AjpProcessor.service(AjpProcessor.java:486)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:861)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1455)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)

2025-06-06 04:11:01.676 ERROR --- [nio-8009-exec-8] StackTrace                               : Full Stack Trace:

java.lang.NumberFormatException: For input string: "null"
	at java.lang.NumberFormatException.forInputString(NumberFormatException.java:65)
	at java.lang.Integer.parseInt(Integer.java:580)
	at java.lang.Integer.<init>(Integer.java:867)
	at com.wonderslate.data.PromptService.$tt__checkIsTeacher(PromptService.groovy:890)
	at grails.transaction.GrailsTransactionTemplate$2.doInTransaction(GrailsTransactionTemplate.groovy:96)
	at org.springframework.transaction.support.TransactionTemplate.execute(TransactionTemplate.java:133)
	at grails.transaction.GrailsTransactionTemplate.execute(GrailsTransactionTemplate.groovy:93)
	at com.wonderslate.data.PromptController.$tt__getDefaultPromptListForResource(PromptController.groovy:1379)
	at grails.transaction.GrailsTransactionTemplate$2.doInTransaction(GrailsTransactionTemplate.groovy:96)
	at org.springframework.transaction.support.TransactionTemplate.execute(TransactionTemplate.java:133)
	at grails.transaction.GrailsTransactionTemplate.execute(GrailsTransactionTemplate.groovy:93)
	at org.grails.core.DefaultGrailsControllerClass$MethodHandleInvoker.invoke(DefaultGrailsControllerClass.java:223)
	at org.grails.core.DefaultGrailsControllerClass.invoke(DefaultGrailsControllerClass.java:188)
	at org.grails.web.mapping.mvc.UrlMappingsInfoHandlerAdapter.handle(UrlMappingsInfoHandlerAdapter.groovy:90)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:963)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:897)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:970)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:861)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:846)
	at grails.plugin.springsecurity.rest.RestLogoutFilter.doFilter(RestLogoutFilter.groovy:80)
	at org.springframework.boot.web.filter.ApplicationContextHeaderFilter.doFilterInternal(ApplicationContextHeaderFilter.java:55)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:317)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:127)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:91)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:115)
	at grails.plugin.springsecurity.web.UpdateRequestContextHolderExceptionTranslationFilter.doFilter(UpdateRequestContextHolderExceptionTranslationFilter.groovy:64)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:115)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at grails.plugin.springsecurity.rest.RestTokenValidationFilter.processFilterChain(RestTokenValidationFilter.groovy:122)
	at grails.plugin.springsecurity.rest.RestTokenValidationFilter.doFilter(RestTokenValidationFilter.groovy:90)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at grails.plugin.springsecurity.web.filter.GrailsAnonymousAuthenticationFilter.doFilter(GrailsAnonymousAuthenticationFilter.groovy:53)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at org.springframework.security.web.authentication.rememberme.RememberMeAuthenticationFilter.doFilter(RememberMeAuthenticationFilter.java:150)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:169)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at grails.plugin.springsecurity.rest.RestAuthenticationFilter.doFilter(RestAuthenticationFilter.groovy:143)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at grails.plugin.springsecurity.web.authentication.logout.MutableLogoutFilter.doFilter(MutableLogoutFilter.groovy:62)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at grails.plugin.springsecurity.web.SecurityRequestHolderFilter.doFilter(SecurityRequestHolderFilter.groovy:58)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:214)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:177)
	at org.grails.web.servlet.mvc.GrailsWebRequestFilter.doFilterInternal(GrailsWebRequestFilter.java:77)
	at org.grails.web.filters.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:67)
	at org.springframework.boot.web.support.ErrorPageFilter.doFilter(ErrorPageFilter.java:117)
	at org.springframework.boot.web.support.ErrorPageFilter.access$000(ErrorPageFilter.java:61)
	at org.springframework.boot.web.support.ErrorPageFilter$1.doFilterInternal(ErrorPageFilter.java:92)
	at org.springframework.boot.web.support.ErrorPageFilter.doFilter(ErrorPageFilter.java:110)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

2025-06-06 04:11:01.716 ERROR --- [nio-8009-exec-8] o.g.web.errors.GrailsExceptionResolver   : NumberFormatException occurred when processing request: [GET] /prompt/getDefaultPromptListForResource
For input string: "null". Stacktrace follows:

java.lang.NumberFormatException: For input string: "null"
	at java.lang.NumberFormatException.forInputString(NumberFormatException.java:65)
	at java.lang.Integer.parseInt(Integer.java:580)
	at java.lang.Integer.<init>(Integer.java:867)
	at com.wonderslate.data.PromptService.$tt__checkIsTeacher(PromptService.groovy:890)
	at grails.transaction.GrailsTransactionTemplate$2.doInTransaction(GrailsTransactionTemplate.groovy:96)
	at org.springframework.transaction.support.TransactionTemplate.execute(TransactionTemplate.java:133)
	at grails.transaction.GrailsTransactionTemplate.execute(GrailsTransactionTemplate.groovy:93)
	at com.wonderslate.data.PromptController.$tt__getDefaultPromptListForResource(PromptController.groovy:1379)
	at grails.transaction.GrailsTransactionTemplate$2.doInTransaction(GrailsTransactionTemplate.groovy:96)
	at org.springframework.transaction.support.TransactionTemplate.execute(TransactionTemplate.java:133)
	at grails.transaction.GrailsTransactionTemplate.execute(GrailsTransactionTemplate.groovy:93)
	at org.grails.core.DefaultGrailsControllerClass$MethodHandleInvoker.invoke(DefaultGrailsControllerClass.java:223)
	at org.grails.core.DefaultGrailsControllerClass.invoke(DefaultGrailsControllerClass.java:188)
	at org.grails.web.mapping.mvc.UrlMappingsInfoHandlerAdapter.handle(UrlMappingsInfoHandlerAdapter.groovy:90)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:963)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:897)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:970)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:861)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:846)
	at grails.plugin.springsecurity.rest.RestLogoutFilter.doFilter(RestLogoutFilter.groovy:80)
	at org.springframework.boot.web.filter.ApplicationContextHeaderFilter.doFilterInternal(ApplicationContextHeaderFilter.java:55)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:317)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:127)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:91)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:115)
	at grails.plugin.springsecurity.web.UpdateRequestContextHolderExceptionTranslationFilter.doFilter(UpdateRequestContextHolderExceptionTranslationFilter.groovy:64)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:115)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at grails.plugin.springsecurity.rest.RestTokenValidationFilter.processFilterChain(RestTokenValidationFilter.groovy:122)
	at grails.plugin.springsecurity.rest.RestTokenValidationFilter.doFilter(RestTokenValidationFilter.groovy:90)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at grails.plugin.springsecurity.web.filter.GrailsAnonymousAuthenticationFilter.doFilter(GrailsAnonymousAuthenticationFilter.groovy:53)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at org.springframework.security.web.authentication.rememberme.RememberMeAuthenticationFilter.doFilter(RememberMeAuthenticationFilter.java:150)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:169)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at grails.plugin.springsecurity.rest.RestAuthenticationFilter.doFilter(RestAuthenticationFilter.groovy:143)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at grails.plugin.springsecurity.web.authentication.logout.MutableLogoutFilter.doFilter(MutableLogoutFilter.groovy:62)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at grails.plugin.springsecurity.web.SecurityRequestHolderFilter.doFilter(SecurityRequestHolderFilter.groovy:58)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:214)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:177)
	at org.grails.web.servlet.mvc.GrailsWebRequestFilter.doFilterInternal(GrailsWebRequestFilter.java:77)
	at org.grails.web.filters.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:67)
	at org.springframework.boot.web.support.ErrorPageFilter.doFilter(ErrorPageFilter.java:117)
	at org.springframework.boot.web.support.ErrorPageFilter.access$000(ErrorPageFilter.java:61)
	at org.springframework.boot.web.support.ErrorPageFilter$1.doFilterInternal(ErrorPageFilter.java:92)
	at org.springframework.boot.web.support.ErrorPageFilter.doFilter(ErrorPageFilter.java:110)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

Executing SQL: select count(*) as mcqcount from resource_dtl rd, chapters_mst cm,objective_mst ob where rd.chapter_id=cm.id and  ob.quiz_id=rd.res_link and rd.sharing is null and rd.id>0 and cm.id in (16220) 
Executing SQL: select res_type,count(*) res_count from resource_dtl rd, chapters_mst cm where rd.chapter_id=cm.id and rd.sharing is null and rd.id>0 and cm.id=16220 group by res_type order by res_type ; 
Executing SQL: select res_type,count(*) res_count from resource_dtl rd, chapters_mst cm where rd.chapter_id=cm.id and rd.sharing is null and rd.id>0 and cm.id=16220 group by res_type order by res_type ; 
Executing SQL: select count(*) as mcqcount from resource_dtl rd, chapters_mst cm,objective_mst ob where rd.chapter_id=cm.id and  ob.quiz_id=rd.res_link and rd.sharing is null and rd.id>0 and cm.id in (16221) 
Executing SQL: select res_type,count(*) res_count from resource_dtl rd, chapters_mst cm where rd.chapter_id=cm.id and rd.sharing is null and rd.id>0 and cm.id=16221 group by res_type order by res_type ; 
Executing SQL: select res_type,count(*) res_count from resource_dtl rd, chapters_mst cm where rd.chapter_id=cm.id and rd.sharing is null and rd.id>0 and cm.id=16221 group by res_type order by res_type ; 
******** resourceId=29988
******** rightFileName=29988.pdf
******** filenameCheck not needed
https://db2cc4e7dph71.cloudfront.net/qa/upload/books/216838/chapters/16220/29988/29988.pdf?Expires=1749183184&Signature=Nk0bDdRPxm9D8XP2ptUEgFXvM6ZHILN4wvo0mZFJJNzO9ojSeSklkNwP69yYll3ib0oGRqHMTdzqrpAKdRR1-VWssYoMyIiiVhtPXJW6P-A1xRVpLgk-RIQA~h3zUr1Wg2HFDrgo0~rHm9Z1kNIP34yih80HPBocace~PRxPhX2St4JFCKd4exXtDaBTRPVWR7UDIMVyahZnz9YEX1jhCLm9~icJ83MHS1lH1DXrCJDXAKymvGJJD~SQpG0MWjJDZMAnLZ~rWd~JiEYPDOjEv0gY1rcMg1Xoq6g9qrFMXfqiS~8DgnVR0xpEuYVjzxcVWCjwJNIb7gwJJ1N1VoNhag__&Key-Pair-Id=K1K3820LSJ1OXC
Request body
[option_images:[], resId:-1, explanation_images:[supload/pdfextracts/312747/22496/38979/extractedQuizImages/explanation_1.png], directions:, chapterId:22496, option3:8 : 11 : 15, option4:7 : 9 : 15, Question:If \(a : b = 4 : 5\), \(b : c = 2 : 3\) then \(a : b : c = ?\), option1:8 : 10 : 15, option2:8 : 9 : 10, correctAnswer:1, answerDescription:(1) \( \alpha : b : c \)  
\( 4 : 5 \)  
\( 2 : 3 \)  
\(\frac{4 \times 2 : 5 \times 2 : 5 \times 3}{ }\)  
\(\Rightarrow 8 : 10 : 15\), question_images:[], username:null]grails.validation.ValidationException: Validation Error(s) occurred during save():
- Field error in object 'com.wonderslate.data.ResourceDtl' on field 'createdBy': rejected value [null]; codes [com.wonderslate.data.ResourceDtl.createdBy.nullable.error.com.wonderslate.data.ResourceDtl.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.error.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.error.java.lang.String,com.wonderslate.data.ResourceDtl.createdBy.nullable.error,resourceDtl.createdBy.nullable.error.com.wonderslate.data.ResourceDtl.createdBy,resourceDtl.createdBy.nullable.error.createdBy,resourceDtl.createdBy.nullable.error.java.lang.String,resourceDtl.createdBy.nullable.error,com.wonderslate.data.ResourceDtl.createdBy.nullable.com.wonderslate.data.ResourceDtl.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.java.lang.String,com.wonderslate.data.ResourceDtl.createdBy.nullable,resourceDtl.createdBy.nullable.com.wonderslate.data.ResourceDtl.createdBy,resourceDtl.createdBy.nullable.createdBy,resourceDtl.createdBy.nullable.java.lang.String,resourceDtl.createdBy.nullable,nullable.com.wonderslate.data.ResourceDtl.createdBy,nullable.createdBy,nullable.java.lang.String,nullable]; arguments [createdBy,class com.wonderslate.data.ResourceDtl]; default message [Property [{0}] of class [{1}] cannot be null]

Request body
[option_images:[], resId:-1, explanation_images:[supload/pdfextracts/312747/22496/38979/extractedQuizImages/explanation_2.png], directions:, chapterId:22496, option3:160 : 280 : 168 : 189, option4:165 : 280 : 168 : 189, Question:If \(a : b = 4 : 7\), \(b : c = 5 : 3\) and \(c : d = 8 : 9\) then \(a : b : c : d = ?\), option1:160 : 260 : 250 : 189, option2:150 : 260 : 167 : 168, correctAnswer:3, answerDescription:(3) \( \alpha : b : c \)  
\( 4 : 7 \)  
\( 5 : 3 \)  
\(\frac{4 \times 5 : 7 \times 5 : 7 \times 3}{ }\)  
= 20 : 35 : 21  
= \(a : b : c : d\)  
= 20 : 35 : 21  
\(\frac{8 : 9}{160 : 280 : 168 : 189}\), question_images:[], username:null]grails.validation.ValidationException: Validation Error(s) occurred during save():
- Field error in object 'com.wonderslate.data.ResourceDtl' on field 'createdBy': rejected value [null]; codes [com.wonderslate.data.ResourceDtl.createdBy.nullable.error.com.wonderslate.data.ResourceDtl.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.error.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.error.java.lang.String,com.wonderslate.data.ResourceDtl.createdBy.nullable.error,resourceDtl.createdBy.nullable.error.com.wonderslate.data.ResourceDtl.createdBy,resourceDtl.createdBy.nullable.error.createdBy,resourceDtl.createdBy.nullable.error.java.lang.String,resourceDtl.createdBy.nullable.error,com.wonderslate.data.ResourceDtl.createdBy.nullable.com.wonderslate.data.ResourceDtl.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.java.lang.String,com.wonderslate.data.ResourceDtl.createdBy.nullable,resourceDtl.createdBy.nullable.com.wonderslate.data.ResourceDtl.createdBy,resourceDtl.createdBy.nullable.createdBy,resourceDtl.createdBy.nullable.java.lang.String,resourceDtl.createdBy.nullable,nullable.com.wonderslate.data.ResourceDtl.createdBy,nullable.createdBy,nullable.java.lang.String,nullable]; arguments [createdBy,class com.wonderslate.data.ResourceDtl]; default message [Property [{0}] of class [{1}] cannot be null]

Request body
[option_images:[], resId:-1, explanation_images:[supload/pdfextracts/312747/22496/38979/extractedQuizImages/explanation_3.png], directions:, chapterId:22496, option3:107 : 185, option4:103 : 185, Question:If \(a : b = 2 : 9\), \(b : c = 7 : 8\) then \((5a + 2c) : (11a + 3c) = ?\), option1:107 : 210, option2:109 : 185, correctAnswer:3, answerDescription:(3) \( \alpha : b : c \)  
2 : 9  
7 : 8  
14 : 63 : 72

\(\frac{5a + 2c}{11a + 3c} = \frac{5 \times 14 + 2 \times 72}{11 \times 14 + 3 \times 72} = \frac{70 + 144}{154 + 216}\)  
\(= \frac{214}{370} = \frac{107}{185} = 107 : 185\), question_images:[], username:null]grails.validation.ValidationException: Validation Error(s) occurred during save():
- Field error in object 'com.wonderslate.data.ResourceDtl' on field 'createdBy': rejected value [null]; codes [com.wonderslate.data.ResourceDtl.createdBy.nullable.error.com.wonderslate.data.ResourceDtl.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.error.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.error.java.lang.String,com.wonderslate.data.ResourceDtl.createdBy.nullable.error,resourceDtl.createdBy.nullable.error.com.wonderslate.data.ResourceDtl.createdBy,resourceDtl.createdBy.nullable.error.createdBy,resourceDtl.createdBy.nullable.error.java.lang.String,resourceDtl.createdBy.nullable.error,com.wonderslate.data.ResourceDtl.createdBy.nullable.com.wonderslate.data.ResourceDtl.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.java.lang.String,com.wonderslate.data.ResourceDtl.createdBy.nullable,resourceDtl.createdBy.nullable.com.wonderslate.data.ResourceDtl.createdBy,resourceDtl.createdBy.nullable.createdBy,resourceDtl.createdBy.nullable.java.lang.String,resourceDtl.createdBy.nullable,nullable.com.wonderslate.data.ResourceDtl.createdBy,nullable.createdBy,nullable.java.lang.String,nullable]; arguments [createdBy,class com.wonderslate.data.ResourceDtl]; default message [Property [{0}] of class [{1}] cannot be null]

Request body
[option_images:[], resId:-1, explanation_images:[supload/pdfextracts/312747/22496/38979/extractedQuizImages/explanation_4.png], directions:, chapterId:22496, option3:28 years, option4:30 years, Question:10 years ago the ratio between age of A & B was 5 : 1 respectively. 4 years after this ratio will become 3 : 2 respectively. Find the present age of A ?, option1:30 years, option2:20 years, correctAnswer:2, answerDescription:(2) A \(\quad\) B (10 years ago)  
\(5 \times 1 : 1 \times 1\)  
\(\underline{\quad 4 \quad}\)  

\(3 - 2\)  
\(\underline{1}\) (4 years after)  

\(\Rightarrow 5 \times 1 \quad 1 \times 1\)  
Differences \(\Rightarrow 12 - 5 = 7\)  
7 unit = 10 + 4  

1 unit = \(\frac{14}{7} = 2\)  

5 unit = \(5 \times 2 = 10\)  
\(\Rightarrow 10 + 10 = 20\) years, question_images:[], username:null]grails.validation.ValidationException: Validation Error(s) occurred during save():
- Field error in object 'com.wonderslate.data.ResourceDtl' on field 'createdBy': rejected value [null]; codes [com.wonderslate.data.ResourceDtl.createdBy.nullable.error.com.wonderslate.data.ResourceDtl.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.error.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.error.java.lang.String,com.wonderslate.data.ResourceDtl.createdBy.nullable.error,resourceDtl.createdBy.nullable.error.com.wonderslate.data.ResourceDtl.createdBy,resourceDtl.createdBy.nullable.error.createdBy,resourceDtl.createdBy.nullable.error.java.lang.String,resourceDtl.createdBy.nullable.error,com.wonderslate.data.ResourceDtl.createdBy.nullable.com.wonderslate.data.ResourceDtl.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.java.lang.String,com.wonderslate.data.ResourceDtl.createdBy.nullable,resourceDtl.createdBy.nullable.com.wonderslate.data.ResourceDtl.createdBy,resourceDtl.createdBy.nullable.createdBy,resourceDtl.createdBy.nullable.java.lang.String,resourceDtl.createdBy.nullable,nullable.com.wonderslate.data.ResourceDtl.createdBy,nullable.createdBy,nullable.java.lang.String,nullable]; arguments [createdBy,class com.wonderslate.data.ResourceDtl]; default message [Property [{0}] of class [{1}] cannot be null]

Request body
[option_images:[], resId:-1, explanation_images:[], directions:, chapterId:22496, option3:36, option4:38, Question:15 years ago the ratio between ages of A & B was 8 : 1 respectively. 4 years after this ratio will become 5 : 3 respectively. Find the age of A after 5 years ?, option1:31, option2:35, correctAnswer:3, answerDescription:(3) A \(\quad\) B  
\(8 \times 2 \quad 1 \times 2\) (15 years ago)  

Difference \(\Rightarrow \frac{5 \times 7}{19} - \frac{3 \times 7}{19}\) (4 years ago)  

19 unit = 15 + 4  

1 unit = \(\frac{19}{19} = 1\)  

16 unit = \(16 \times 1 = 16 \Rightarrow\) A (15 years ago)  
\(\Rightarrow 16 + 15 + 5 = 16 + 20 = 36\) years, question_images:[], username:null]grails.validation.ValidationException: Validation Error(s) occurred during save():
- Field error in object 'com.wonderslate.data.ResourceDtl' on field 'createdBy': rejected value [null]; codes [com.wonderslate.data.ResourceDtl.createdBy.nullable.error.com.wonderslate.data.ResourceDtl.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.error.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.error.java.lang.String,com.wonderslate.data.ResourceDtl.createdBy.nullable.error,resourceDtl.createdBy.nullable.error.com.wonderslate.data.ResourceDtl.createdBy,resourceDtl.createdBy.nullable.error.createdBy,resourceDtl.createdBy.nullable.error.java.lang.String,resourceDtl.createdBy.nullable.error,com.wonderslate.data.ResourceDtl.createdBy.nullable.com.wonderslate.data.ResourceDtl.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.java.lang.String,com.wonderslate.data.ResourceDtl.createdBy.nullable,resourceDtl.createdBy.nullable.com.wonderslate.data.ResourceDtl.createdBy,resourceDtl.createdBy.nullable.createdBy,resourceDtl.createdBy.nullable.java.lang.String,resourceDtl.createdBy.nullable,nullable.com.wonderslate.data.ResourceDtl.createdBy,nullable.createdBy,nullable.java.lang.String,nullable]; arguments [createdBy,class com.wonderslate.data.ResourceDtl]; default message [Property [{0}] of class [{1}] cannot be null]

Request body
[option_images:[], resId:-1, explanation_images:[], directions:, chapterId:22496, option3:15 years ago, A was 5 times B, but 5 years after the ratio between the age of A & B will be 3 : 2., option4:10 years ago the age, of A was 4 time the age of B, but 15 years after A will be 5 times the age of B., Question:Which one of the following is true ?, option1:12 years ago the ratio between age of A & B was 6 : 1, but 8 years after this ratio will become 2 : 3 respectively., option2:20 years ago the ratio between age of A & B was 5 : 4 respectively, but 10 years after this ratio will become 6 : 1., correctAnswer:3, answerDescription:(3) The ratio of the age of two persons will decrease from before to after., question_images:[], username:null]grails.validation.ValidationException: Validation Error(s) occurred during save():
- Field error in object 'com.wonderslate.data.ResourceDtl' on field 'createdBy': rejected value [null]; codes [com.wonderslate.data.ResourceDtl.createdBy.nullable.error.com.wonderslate.data.ResourceDtl.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.error.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.error.java.lang.String,com.wonderslate.data.ResourceDtl.createdBy.nullable.error,resourceDtl.createdBy.nullable.error.com.wonderslate.data.ResourceDtl.createdBy,resourceDtl.createdBy.nullable.error.createdBy,resourceDtl.createdBy.nullable.error.java.lang.String,resourceDtl.createdBy.nullable.error,com.wonderslate.data.ResourceDtl.createdBy.nullable.com.wonderslate.data.ResourceDtl.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.java.lang.String,com.wonderslate.data.ResourceDtl.createdBy.nullable,resourceDtl.createdBy.nullable.com.wonderslate.data.ResourceDtl.createdBy,resourceDtl.createdBy.nullable.createdBy,resourceDtl.createdBy.nullable.java.lang.String,resourceDtl.createdBy.nullable,nullable.com.wonderslate.data.ResourceDtl.createdBy,nullable.createdBy,nullable.java.lang.String,nullable]; arguments [createdBy,class com.wonderslate.data.ResourceDtl]; default message [Property [{0}] of class [{1}] cannot be null]

Request body
[option_images:[], resId:-1, explanation_images:[], directions:, chapterId:22496, option3:24 years, option4:28 years, Question:Peenki's age 8 years ago is equal to sum of present age of her son & daughter. 5 years hence the ratio between her daughter's age & her son's age will be 7 : 6 respectively. Peenki's husband is 7 years older than her. Her husband's present age is 3 times the present age of his son. What is their daughter's present age., option1:23 years, option2:25 years, correctAnswer:1, answerDescription:(1) Daughter : Son  
\(7 : 6\) (5 years after)  
\(\downarrow \quad \downarrow\)  
\(7x \quad 6x\)  

Pinki's age (8 years ago) = \((7x + 6x) - 10 = 13x - 10 = 13x - 10\)  
Pinki's husband age = \(13x - 10 + 8 + 7 = 13x + 5\)  
\(13x + 5 = (6x - 5) \times 3 \Rightarrow 13x + 5 = 18x - 15\)  
\(\Rightarrow 5x = 20 \Rightarrow x = 4\)  
Daughter's present age = \(7 \times 4 - 5 = 23\) years, question_images:[], username:null]grails.validation.ValidationException: Validation Error(s) occurred during save():
- Field error in object 'com.wonderslate.data.ResourceDtl' on field 'createdBy': rejected value [null]; codes [com.wonderslate.data.ResourceDtl.createdBy.nullable.error.com.wonderslate.data.ResourceDtl.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.error.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.error.java.lang.String,com.wonderslate.data.ResourceDtl.createdBy.nullable.error,resourceDtl.createdBy.nullable.error.com.wonderslate.data.ResourceDtl.createdBy,resourceDtl.createdBy.nullable.error.createdBy,resourceDtl.createdBy.nullable.error.java.lang.String,resourceDtl.createdBy.nullable.error,com.wonderslate.data.ResourceDtl.createdBy.nullable.com.wonderslate.data.ResourceDtl.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.java.lang.String,com.wonderslate.data.ResourceDtl.createdBy.nullable,resourceDtl.createdBy.nullable.com.wonderslate.data.ResourceDtl.createdBy,resourceDtl.createdBy.nullable.createdBy,resourceDtl.createdBy.nullable.java.lang.String,resourceDtl.createdBy.nullable,nullable.com.wonderslate.data.ResourceDtl.createdBy,nullable.createdBy,nullable.java.lang.String,nullable]; arguments [createdBy,class com.wonderslate.data.ResourceDtl]; default message [Property [{0}] of class [{1}] cannot be null]

Request body
[option_images:[], resId:-1, explanation_images:[], directions:, chapterId:22496, option3:Rs. 30,000, option4:Rs. 35,000, Question:The price of diamond is directly proportion to cube of its weight. It has been broken into three pieces in the ratio of 2 : 3 : 5 respectively. Its price is decreased by Rs. 16800. Find its original cost ?, option1:Rs. 20,000, option2:Rs. 25,000, correctAnswer:1, answerDescription:(1) \(\Rightarrow 10 \Rightarrow (10)^3 = 1000\)  

I \(\quad\) II \(\quad\) III  
\(2 : 3 : 5\)  
\(\underline{+ \quad \quad \quad}\)  
10  

\(2^3 \quad 3^3 \quad 5^3\)  
\(8 : 27 : 125\)  
\(\underline{+ \quad \quad \quad}\)  
10  
\(\Rightarrow 160 \Rightarrow 1000 - 160 = 840\)

840 unit = 16800  
\(1 \text{ unit} = \frac{16800}{840} = 20\)  
1000 unit = 20 \(\times\) 1000 = Rs. 20000, question_images:[], username:null]grails.validation.ValidationException: Validation Error(s) occurred during save():
- Field error in object 'com.wonderslate.data.ResourceDtl' on field 'createdBy': rejected value [null]; codes [com.wonderslate.data.ResourceDtl.createdBy.nullable.error.com.wonderslate.data.ResourceDtl.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.error.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.error.java.lang.String,com.wonderslate.data.ResourceDtl.createdBy.nullable.error,resourceDtl.createdBy.nullable.error.com.wonderslate.data.ResourceDtl.createdBy,resourceDtl.createdBy.nullable.error.createdBy,resourceDtl.createdBy.nullable.error.java.lang.String,resourceDtl.createdBy.nullable.error,com.wonderslate.data.ResourceDtl.createdBy.nullable.com.wonderslate.data.ResourceDtl.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.java.lang.String,com.wonderslate.data.ResourceDtl.createdBy.nullable,resourceDtl.createdBy.nullable.com.wonderslate.data.ResourceDtl.createdBy,resourceDtl.createdBy.nullable.createdBy,resourceDtl.createdBy.nullable.java.lang.String,resourceDtl.createdBy.nullable,nullable.com.wonderslate.data.ResourceDtl.createdBy,nullable.createdBy,nullable.java.lang.String,nullable]; arguments [createdBy,class com.wonderslate.data.ResourceDtl]; default message [Property [{0}] of class [{1}] cannot be null]

Request body
[option_images:[], resId:-1, explanation_images:[], directions:, chapterId:22496, option3:972 cm, option4:672 cm, Question:The gravitation of a planet is equivalent to the product of density & radius of that planet. Radius of jupiter is 10 times the radius of earth whereas density of jupiter & earth are 1.75 & 5.67 respectively. If a man can jump 3000 cm high on earth. How much can he jump on Jupiter ?, option1:872 cm, option2:772 cm, correctAnswer:3, answerDescription:(3) Jump on Jupiter = \(x\) cm  
\(\frac{x}{3000} = \frac{1 \times 5.67}{10 \times 1.75}\)  
\(\Rightarrow x = 972\) cm, question_images:[], username:null]grails.validation.ValidationException: Validation Error(s) occurred during save():
- Field error in object 'com.wonderslate.data.ResourceDtl' on field 'createdBy': rejected value [null]; codes [com.wonderslate.data.ResourceDtl.createdBy.nullable.error.com.wonderslate.data.ResourceDtl.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.error.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.error.java.lang.String,com.wonderslate.data.ResourceDtl.createdBy.nullable.error,resourceDtl.createdBy.nullable.error.com.wonderslate.data.ResourceDtl.createdBy,resourceDtl.createdBy.nullable.error.createdBy,resourceDtl.createdBy.nullable.error.java.lang.String,resourceDtl.createdBy.nullable.error,com.wonderslate.data.ResourceDtl.createdBy.nullable.com.wonderslate.data.ResourceDtl.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.java.lang.String,com.wonderslate.data.ResourceDtl.createdBy.nullable,resourceDtl.createdBy.nullable.com.wonderslate.data.ResourceDtl.createdBy,resourceDtl.createdBy.nullable.createdBy,resourceDtl.createdBy.nullable.java.lang.String,resourceDtl.createdBy.nullable,nullable.com.wonderslate.data.ResourceDtl.createdBy,nullable.createdBy,nullable.java.lang.String,nullable]; arguments [createdBy,class com.wonderslate.data.ResourceDtl]; default message [Property [{0}] of class [{1}] cannot be null]

Request body
[option_images:[], resId:-1, explanation_images:[], directions:, chapterId:22496, option3:Rs. 25,000, option4:Rs. 35,000, Question:The respective ratio between monthly salary of A & B is 5 : 6 respectively. A & B spends 40% & 25% of their salary. On which they invest \(\frac{5}{8}\) & \(\frac{3}{5}\) in LIC respectively. If A invests Rs. 1750 more than B in LIC. What is A's monthly salary ?, option1:Rs. 28,000, option2:Rs. 30,000, correctAnswer:3, answerDescription:(3) 40% = \(\frac{2}{5}\), 25% = \(\frac{1}{4}\)  

\(\frac{2}{5}, \frac{1}{4}, \frac{5}{8}, \frac{3}{5} \Rightarrow\) LCM of 5, 4, 8, 5 = 40  

\[  
\begin{array}{c c}  
A & B \\  
5 & : & 6 \\  
5 \times 40 & & 6 \times 40 \\  
200 & & 240 \\  
\end{array}  
\]

\(A \Rightarrow 200 \times \frac{2}{5} \times \frac{5}{8} = 50 \quad \left[ - 14 \right]\)  

\(B \Rightarrow 200 \times \frac{1}{4} \times \frac{3}{5} = 36\)  

14 unit = 1750  
\(200 \text{ unit} \times \frac{1750}{14} = \times 200 = \text{Rs. } 25000\), question_images:[], username:null]grails.validation.ValidationException: Validation Error(s) occurred during save():
- Field error in object 'com.wonderslate.data.ResourceDtl' on field 'createdBy': rejected value [null]; codes [com.wonderslate.data.ResourceDtl.createdBy.nullable.error.com.wonderslate.data.ResourceDtl.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.error.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.error.java.lang.String,com.wonderslate.data.ResourceDtl.createdBy.nullable.error,resourceDtl.createdBy.nullable.error.com.wonderslate.data.ResourceDtl.createdBy,resourceDtl.createdBy.nullable.error.createdBy,resourceDtl.createdBy.nullable.error.java.lang.String,resourceDtl.createdBy.nullable.error,com.wonderslate.data.ResourceDtl.createdBy.nullable.com.wonderslate.data.ResourceDtl.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.java.lang.String,com.wonderslate.data.ResourceDtl.createdBy.nullable,resourceDtl.createdBy.nullable.com.wonderslate.data.ResourceDtl.createdBy,resourceDtl.createdBy.nullable.createdBy,resourceDtl.createdBy.nullable.java.lang.String,resourceDtl.createdBy.nullable,nullable.com.wonderslate.data.ResourceDtl.createdBy,nullable.createdBy,nullable.java.lang.String,nullable]; arguments [createdBy,class com.wonderslate.data.ResourceDtl]; default message [Property [{0}] of class [{1}] cannot be null]

Request body
[option_images:[], resId:-1, explanation_images:[], directions:, chapterId:22496, option3:Rs. 30,000, option4:Rs. 50,000, Question:A man deposit 10% of his salary in PF. He saves 30% of the remaining. The ratio of his expense on medicine & groceries is 3 : 4 of the remaining salary after savings. If his expense on medicine was Rs. 8100. Find his monthly salary ?, option1:Rs. 35,000, option2:Rs. 40,000, correctAnswer:3, answerDescription:Monthly Salary = \(\frac{100}{90}\)  PF = 10  Saving = \(90 \times \frac{30}{100} = 27\)  Remaining = 90 - 27 = 63  7 unit = 63%  1 unit = 9%  3 unit = 27%  27% = 8100  1% = 300  100% = 300 \(\times\) 100 = Rs. 30000, question_images:[], username:null]grails.validation.ValidationException: Validation Error(s) occurred during save():
- Field error in object 'com.wonderslate.data.ResourceDtl' on field 'createdBy': rejected value [null]; codes [com.wonderslate.data.ResourceDtl.createdBy.nullable.error.com.wonderslate.data.ResourceDtl.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.error.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.error.java.lang.String,com.wonderslate.data.ResourceDtl.createdBy.nullable.error,resourceDtl.createdBy.nullable.error.com.wonderslate.data.ResourceDtl.createdBy,resourceDtl.createdBy.nullable.error.createdBy,resourceDtl.createdBy.nullable.error.java.lang.String,resourceDtl.createdBy.nullable.error,com.wonderslate.data.ResourceDtl.createdBy.nullable.com.wonderslate.data.ResourceDtl.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.java.lang.String,com.wonderslate.data.ResourceDtl.createdBy.nullable,resourceDtl.createdBy.nullable.com.wonderslate.data.ResourceDtl.createdBy,resourceDtl.createdBy.nullable.createdBy,resourceDtl.createdBy.nullable.java.lang.String,resourceDtl.createdBy.nullable,nullable.com.wonderslate.data.ResourceDtl.createdBy,nullable.createdBy,nullable.java.lang.String,nullable]; arguments [createdBy,class com.wonderslate.data.ResourceDtl]; default message [Property [{0}] of class [{1}] cannot be null]

Request body
[option_images:[], resId:-1, explanation_images:[supload/pdfextracts/312747/22496/38979/extractedQuizImages/explanation_12.png], directions:, chapterId:22496, option3:Rs. 3250, option4:Rs. 7250, Question:The ratio of fare of first, second & third class was 10 : 8 : 3 respectively. The number of passangers of first, second & third class was in the ratio of 3 : 4 : 10 respectively & the revenue income from the tickets was Rs. 16,100. How much money was received from third class tickets?, option1:Rs. 4250, option2:Rs. 5250, correctAnswer:2, answerDescription:Fare ratio: 10 : 8 : 3  Passenger ratio: 3 : 4 : 10  Revenue ratio = 10×3 : 8×4 : 3×10 = 30 : 32 : 30 = 15 : 16 : 15  Total units = 46  46 unit = 16100  1 unit = 16100/46  15 unit = (16100/46) × 15 = Rs. 5250, question_images:[], username:null]grails.validation.ValidationException: Validation Error(s) occurred during save():
- Field error in object 'com.wonderslate.data.ResourceDtl' on field 'createdBy': rejected value [null]; codes [com.wonderslate.data.ResourceDtl.createdBy.nullable.error.com.wonderslate.data.ResourceDtl.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.error.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.error.java.lang.String,com.wonderslate.data.ResourceDtl.createdBy.nullable.error,resourceDtl.createdBy.nullable.error.com.wonderslate.data.ResourceDtl.createdBy,resourceDtl.createdBy.nullable.error.createdBy,resourceDtl.createdBy.nullable.error.java.lang.String,resourceDtl.createdBy.nullable.error,com.wonderslate.data.ResourceDtl.createdBy.nullable.com.wonderslate.data.ResourceDtl.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.java.lang.String,com.wonderslate.data.ResourceDtl.createdBy.nullable,resourceDtl.createdBy.nullable.com.wonderslate.data.ResourceDtl.createdBy,resourceDtl.createdBy.nullable.createdBy,resourceDtl.createdBy.nullable.java.lang.String,resourceDtl.createdBy.nullable,nullable.com.wonderslate.data.ResourceDtl.createdBy,nullable.createdBy,nullable.java.lang.String,nullable]; arguments [createdBy,class com.wonderslate.data.ResourceDtl]; default message [Property [{0}] of class [{1}] cannot be null]

Request body
[option_images:[], resId:-1, explanation_images:[], directions:, chapterId:22496, option3:53, option4:23, Question:There are 240 students (boys + girls) in a school, who opted for either cricket or football. 100 students (boys + girls) opted for cricket. Number of boys who opted for football was 106 more than those of girls. If the respective ratio between no. of boys & girls was 2 : 1, how many girls opted for cricket ?, option1:63, option2:36, correctAnswer:1, answerDescription:Total students = 240  Boys : Girls = 2 : 1  Boys = \(\frac{2}{3} \times 240 = 160\), Girls = \(\frac{1}{3} \times 240 = 80\)  Let girls who opted football = x, then boys who opted football = x + 106  Total footballers = 240 - 100 = 140  So, x + (x + 106) = 140  \(2x + 106 = 140 \Rightarrow 2x = 34 \Rightarrow x = 17\)  Girls football = 17, Boys football = 123  Boys cricket = 160 - 123 = 37  Girls cricket = 100 - 37 = 63, question_images:[], username:null]grails.validation.ValidationException: Validation Error(s) occurred during save():
- Field error in object 'com.wonderslate.data.ResourceDtl' on field 'createdBy': rejected value [null]; codes [com.wonderslate.data.ResourceDtl.createdBy.nullable.error.com.wonderslate.data.ResourceDtl.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.error.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.error.java.lang.String,com.wonderslate.data.ResourceDtl.createdBy.nullable.error,resourceDtl.createdBy.nullable.error.com.wonderslate.data.ResourceDtl.createdBy,resourceDtl.createdBy.nullable.error.createdBy,resourceDtl.createdBy.nullable.error.java.lang.String,resourceDtl.createdBy.nullable.error,com.wonderslate.data.ResourceDtl.createdBy.nullable.com.wonderslate.data.ResourceDtl.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.java.lang.String,com.wonderslate.data.ResourceDtl.createdBy.nullable,resourceDtl.createdBy.nullable.com.wonderslate.data.ResourceDtl.createdBy,resourceDtl.createdBy.nullable.createdBy,resourceDtl.createdBy.nullable.java.lang.String,resourceDtl.createdBy.nullable,nullable.com.wonderslate.data.ResourceDtl.createdBy,nullable.createdBy,nullable.java.lang.String,nullable]; arguments [createdBy,class com.wonderslate.data.ResourceDtl]; default message [Property [{0}] of class [{1}] cannot be null]

Request body
[option_images:[], resId:-1, explanation_images:[], directions:, chapterId:22496, option3:8, option4:12, Question:In a family of 30 members, each member requires equal quantity of rice. At a particular day, due to absence of some members the consumption of rice is reduced in the ratio 6 : 5 respectively. What is the number of absent members in that day ?, option1:10, option2:5, correctAnswer:2, answerDescription:Consumption ratio 6 : 5  6 units = 30 members  1 unit = 5 members  Difference = 6 - 5 = 1 unit = 5 members absent, question_images:[], username:null]grails.validation.ValidationException: Validation Error(s) occurred during save():
- Field error in object 'com.wonderslate.data.ResourceDtl' on field 'createdBy': rejected value [null]; codes [com.wonderslate.data.ResourceDtl.createdBy.nullable.error.com.wonderslate.data.ResourceDtl.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.error.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.error.java.lang.String,com.wonderslate.data.ResourceDtl.createdBy.nullable.error,resourceDtl.createdBy.nullable.error.com.wonderslate.data.ResourceDtl.createdBy,resourceDtl.createdBy.nullable.error.createdBy,resourceDtl.createdBy.nullable.error.java.lang.String,resourceDtl.createdBy.nullable.error,com.wonderslate.data.ResourceDtl.createdBy.nullable.com.wonderslate.data.ResourceDtl.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.java.lang.String,com.wonderslate.data.ResourceDtl.createdBy.nullable,resourceDtl.createdBy.nullable.com.wonderslate.data.ResourceDtl.createdBy,resourceDtl.createdBy.nullable.createdBy,resourceDtl.createdBy.nullable.java.lang.String,resourceDtl.createdBy.nullable,nullable.com.wonderslate.data.ResourceDtl.createdBy,nullable.createdBy,nullable.java.lang.String,nullable]; arguments [createdBy,class com.wonderslate.data.ResourceDtl]; default message [Property [{0}] of class [{1}] cannot be null]

Request body
[option_images:[], resId:-1, explanation_images:[], directions:, chapterId:22496, option3:200, option4:600, Question:There are three types of coins of Rs. 1, 50 paise & 25 Paise in a bag. The number of coins of Rs. 1 & 50 paise are in the ratio of 3 : 4 and 50 paise & 25 paise are in the ratio of 2 : 5 respectively. If this bag contains Rs. 450 Find the number of coins of 25 paise ?, option1:150, option2:300, correctAnswer:4, answerDescription:Number ratio: Rs.1 : 50 paise : 25 paise  = 3 : 4 : ?  Given 50 paise : 25 paise = 2 : 5  So, Rs.1 : 50 paise : 25 paise = 6 : 8 : 20 (LCM applied)  Value ratio = 6×1 : 8×0.5 : 20×0.25 = 6 : 4 : 5  Total value units = 15  15 units = Rs. 450  1 unit = Rs. 30  Number of 25 paise coins = 20 × 30 = 600, question_images:[], username:null]grails.validation.ValidationException: Validation Error(s) occurred during save():
- Field error in object 'com.wonderslate.data.ResourceDtl' on field 'createdBy': rejected value [null]; codes [com.wonderslate.data.ResourceDtl.createdBy.nullable.error.com.wonderslate.data.ResourceDtl.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.error.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.error.java.lang.String,com.wonderslate.data.ResourceDtl.createdBy.nullable.error,resourceDtl.createdBy.nullable.error.com.wonderslate.data.ResourceDtl.createdBy,resourceDtl.createdBy.nullable.error.createdBy,resourceDtl.createdBy.nullable.error.java.lang.String,resourceDtl.createdBy.nullable.error,com.wonderslate.data.ResourceDtl.createdBy.nullable.com.wonderslate.data.ResourceDtl.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.java.lang.String,com.wonderslate.data.ResourceDtl.createdBy.nullable,resourceDtl.createdBy.nullable.com.wonderslate.data.ResourceDtl.createdBy,resourceDtl.createdBy.nullable.createdBy,resourceDtl.createdBy.nullable.java.lang.String,resourceDtl.createdBy.nullable,nullable.com.wonderslate.data.ResourceDtl.createdBy,nullable.createdBy,nullable.java.lang.String,nullable]; arguments [createdBy,class com.wonderslate.data.ResourceDtl]; default message [Property [{0}] of class [{1}] cannot be null]

Request body
[option_images:[], resId:-1, explanation_images:[], directions:, chapterId:22496, option3:750, option4:800, Question:A bag contains the coins of Rs. 1, Rs. 2, Rs. 5. The ratio between value of these coins are 5 : 8 : 15 respectively. If this bag contain 600 coins. Find the value of Rs. 5 coins ?, option1:150, option2:600, correctAnswer:3, answerDescription:Value ratio: Rs.1 : Rs.2 : Rs.5 = 5 : 8 : 15  Number of coins ratio: Rs.1 : Rs.2 : Rs.5 = 5 : 4 : 3 (dividing value by denomination)  Total coins = 5 + 4 + 3 = 12 units = 600 coins  1 unit = 50 coins  Number of Rs.5 coins = 3 × 50 = 150  Value of Rs.5 coins = 150 × 5 = Rs. 750, question_images:[], username:null]grails.validation.ValidationException: Validation Error(s) occurred during save():
- Field error in object 'com.wonderslate.data.ResourceDtl' on field 'createdBy': rejected value [null]; codes [com.wonderslate.data.ResourceDtl.createdBy.nullable.error.com.wonderslate.data.ResourceDtl.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.error.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.error.java.lang.String,com.wonderslate.data.ResourceDtl.createdBy.nullable.error,resourceDtl.createdBy.nullable.error.com.wonderslate.data.ResourceDtl.createdBy,resourceDtl.createdBy.nullable.error.createdBy,resourceDtl.createdBy.nullable.error.java.lang.String,resourceDtl.createdBy.nullable.error,com.wonderslate.data.ResourceDtl.createdBy.nullable.com.wonderslate.data.ResourceDtl.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.java.lang.String,com.wonderslate.data.ResourceDtl.createdBy.nullable,resourceDtl.createdBy.nullable.com.wonderslate.data.ResourceDtl.createdBy,resourceDtl.createdBy.nullable.createdBy,resourceDtl.createdBy.nullable.java.lang.String,resourceDtl.createdBy.nullable,nullable.com.wonderslate.data.ResourceDtl.createdBy,nullable.createdBy,nullable.java.lang.String,nullable]; arguments [createdBy,class com.wonderslate.data.ResourceDtl]; default message [Property [{0}] of class [{1}] cannot be null]

Request body
[option_images:[], resId:-1, explanation_images:[], directions:, chapterId:22496, option3:3 : 4, option4:7 : 6, Question:Rs. 25,000 is to be distributed among of A & B in the ratio of 3 : 2 respectively. If Rs. 5000 is to be added with every person. Which one of the following will be new ratio., option1:4 : 3, option2:5 : 4, correctAnswer:1, answerDescription:Original ratio A : B = 3 : 2  Total units = 5  1 unit = Rs. 5000  A = 3 × 5000 = 15000, B = 2 × 5000 = 10000  After adding Rs. 5000 each: A = 15000 + 5000 = 20000, B = 10000 + 5000 = 15000  New ratio = 20000 : 15000 = 4 : 3, question_images:[], username:null]grails.validation.ValidationException: Validation Error(s) occurred during save():
- Field error in object 'com.wonderslate.data.ResourceDtl' on field 'createdBy': rejected value [null]; codes [com.wonderslate.data.ResourceDtl.createdBy.nullable.error.com.wonderslate.data.ResourceDtl.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.error.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.error.java.lang.String,com.wonderslate.data.ResourceDtl.createdBy.nullable.error,resourceDtl.createdBy.nullable.error.com.wonderslate.data.ResourceDtl.createdBy,resourceDtl.createdBy.nullable.error.createdBy,resourceDtl.createdBy.nullable.error.java.lang.String,resourceDtl.createdBy.nullable.error,com.wonderslate.data.ResourceDtl.createdBy.nullable.com.wonderslate.data.ResourceDtl.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.java.lang.String,com.wonderslate.data.ResourceDtl.createdBy.nullable,resourceDtl.createdBy.nullable.com.wonderslate.data.ResourceDtl.createdBy,resourceDtl.createdBy.nullable.createdBy,resourceDtl.createdBy.nullable.java.lang.String,resourceDtl.createdBy.nullable,nullable.com.wonderslate.data.ResourceDtl.createdBy,nullable.createdBy,nullable.java.lang.String,nullable]; arguments [createdBy,class com.wonderslate.data.ResourceDtl]; default message [Property [{0}] of class [{1}] cannot be null]

Request body
[option_images:[], resId:-1, explanation_images:[], directions:, chapterId:22496, option3:4, option4:6, Question:There are 36 students in a group, in which boys & girls are in the ratio of 3 : 1 respectively. How many girls were added to get this ratio 9 : 5 respectively., option1:8, option2:3, correctAnswer:4, answerDescription:Initial ratio B : G = 3 : 1  Total students = 36  So, 4 units = 36  1 unit = 9  Boys = 3 × 9 = 27, Girls = 9  Let x girls added, new ratio = 9 : 5  \(\frac{27}{9 + x} = \frac{9}{5}\)  \(9 + x = 15 \Rightarrow x = 6\), question_images:[], username:null]grails.validation.ValidationException: Validation Error(s) occurred during save():
- Field error in object 'com.wonderslate.data.ResourceDtl' on field 'createdBy': rejected value [null]; codes [com.wonderslate.data.ResourceDtl.createdBy.nullable.error.com.wonderslate.data.ResourceDtl.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.error.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.error.java.lang.String,com.wonderslate.data.ResourceDtl.createdBy.nullable.error,resourceDtl.createdBy.nullable.error.com.wonderslate.data.ResourceDtl.createdBy,resourceDtl.createdBy.nullable.error.createdBy,resourceDtl.createdBy.nullable.error.java.lang.String,resourceDtl.createdBy.nullable.error,com.wonderslate.data.ResourceDtl.createdBy.nullable.com.wonderslate.data.ResourceDtl.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.java.lang.String,com.wonderslate.data.ResourceDtl.createdBy.nullable,resourceDtl.createdBy.nullable.com.wonderslate.data.ResourceDtl.createdBy,resourceDtl.createdBy.nullable.createdBy,resourceDtl.createdBy.nullable.java.lang.String,resourceDtl.createdBy.nullable,nullable.com.wonderslate.data.ResourceDtl.createdBy,nullable.createdBy,nullable.java.lang.String,nullable]; arguments [createdBy,class com.wonderslate.data.ResourceDtl]; default message [Property [{0}] of class [{1}] cannot be null]

Request body
[option_images:[], resId:-1, explanation_images:[], directions:, chapterId:22496, option3:\frac{5}{9}, option4:2, Question:If \(\frac{b}{a} = 0.25\), then, \(\frac{2a - b}{2a + b} + \frac{2}{9} = ?\), option1:1, option2:\frac{4}{9}, correctAnswer:1, answerDescription:Given \(\frac{b}{a} = \frac{1}{4} \Rightarrow b = 1, a = 4\)  \(\Rightarrow \frac{2a - b}{2a + b} + \frac{2}{9} = \frac{2 \times 4 - 1}{2 \times 4 + 1} + \frac{2}{9} = \frac{7}{9} + \frac{2}{9} = \frac{9}{9} = 1\), question_images:[], username:null]grails.validation.ValidationException: Validation Error(s) occurred during save():
- Field error in object 'com.wonderslate.data.ResourceDtl' on field 'createdBy': rejected value [null]; codes [com.wonderslate.data.ResourceDtl.createdBy.nullable.error.com.wonderslate.data.ResourceDtl.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.error.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.error.java.lang.String,com.wonderslate.data.ResourceDtl.createdBy.nullable.error,resourceDtl.createdBy.nullable.error.com.wonderslate.data.ResourceDtl.createdBy,resourceDtl.createdBy.nullable.error.createdBy,resourceDtl.createdBy.nullable.error.java.lang.String,resourceDtl.createdBy.nullable.error,com.wonderslate.data.ResourceDtl.createdBy.nullable.com.wonderslate.data.ResourceDtl.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.java.lang.String,com.wonderslate.data.ResourceDtl.createdBy.nullable,resourceDtl.createdBy.nullable.com.wonderslate.data.ResourceDtl.createdBy,resourceDtl.createdBy.nullable.createdBy,resourceDtl.createdBy.nullable.java.lang.String,resourceDtl.createdBy.nullable,nullable.com.wonderslate.data.ResourceDtl.createdBy,nullable.createdBy,nullable.java.lang.String,nullable]; arguments [createdBy,class com.wonderslate.data.ResourceDtl]; default message [Property [{0}] of class [{1}] cannot be null]

Request body
[option_images:[], resId:-1, explanation_images:[], directions:, chapterId:22496, option3:\left(\frac{1}{24}\right)^2, option4:(24)^2, Question:\(\frac{1}{144} : ? : : 108 : 27\), option1:\left(\frac{1}{25}\right)^2, option2:(26)^2, correctAnswer:3, answerDescription:Given \(\frac{1}{144} : x = 108 : 27\)  \(\Rightarrow \frac{1}{144 \times x} = \frac{108}{27}\)  \(\Rightarrow x = \frac{27}{108 \times 144} = \frac{1}{576} = \left(\frac{1}{24}\right)^2\), question_images:[], username:null]grails.validation.ValidationException: Validation Error(s) occurred during save():
- Field error in object 'com.wonderslate.data.ResourceDtl' on field 'createdBy': rejected value [null]; codes [com.wonderslate.data.ResourceDtl.createdBy.nullable.error.com.wonderslate.data.ResourceDtl.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.error.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.error.java.lang.String,com.wonderslate.data.ResourceDtl.createdBy.nullable.error,resourceDtl.createdBy.nullable.error.com.wonderslate.data.ResourceDtl.createdBy,resourceDtl.createdBy.nullable.error.createdBy,resourceDtl.createdBy.nullable.error.java.lang.String,resourceDtl.createdBy.nullable.error,com.wonderslate.data.ResourceDtl.createdBy.nullable.com.wonderslate.data.ResourceDtl.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.java.lang.String,com.wonderslate.data.ResourceDtl.createdBy.nullable,resourceDtl.createdBy.nullable.com.wonderslate.data.ResourceDtl.createdBy,resourceDtl.createdBy.nullable.createdBy,resourceDtl.createdBy.nullable.java.lang.String,resourceDtl.createdBy.nullable,nullable.com.wonderslate.data.ResourceDtl.createdBy,nullable.createdBy,nullable.java.lang.String,nullable]; arguments [createdBy,class com.wonderslate.data.ResourceDtl]; default message [Property [{0}] of class [{1}] cannot be null]

Request body
[option_images:[], resId:-1, explanation_images:[supload/pdfextracts/312747/22496/38979/extractedQuizImages/explanation_31.png], directions:, chapterId:22496, option3:R, option4:Q, Question:M is twice the age of N, but half of P. Q is half the age of N but twice of R. Who among them is the youngest?, option1:N, option2:M, correctAnswer:3, answerDescription:M : N : P : Q : R = 2 : 1 : 4 : 1/2 : 1/4. Scaling by 4 gives 8 : 4 : 16 : 2 : 1. R is the youngest., question_images:[], username:null]grails.validation.ValidationException: Validation Error(s) occurred during save():
- Field error in object 'com.wonderslate.data.ResourceDtl' on field 'createdBy': rejected value [null]; codes [com.wonderslate.data.ResourceDtl.createdBy.nullable.error.com.wonderslate.data.ResourceDtl.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.error.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.error.java.lang.String,com.wonderslate.data.ResourceDtl.createdBy.nullable.error,resourceDtl.createdBy.nullable.error.com.wonderslate.data.ResourceDtl.createdBy,resourceDtl.createdBy.nullable.error.createdBy,resourceDtl.createdBy.nullable.error.java.lang.String,resourceDtl.createdBy.nullable.error,com.wonderslate.data.ResourceDtl.createdBy.nullable.com.wonderslate.data.ResourceDtl.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.java.lang.String,com.wonderslate.data.ResourceDtl.createdBy.nullable,resourceDtl.createdBy.nullable.com.wonderslate.data.ResourceDtl.createdBy,resourceDtl.createdBy.nullable.createdBy,resourceDtl.createdBy.nullable.java.lang.String,resourceDtl.createdBy.nullable,nullable.com.wonderslate.data.ResourceDtl.createdBy,nullable.createdBy,nullable.java.lang.String,nullable]; arguments [createdBy,class com.wonderslate.data.ResourceDtl]; default message [Property [{0}] of class [{1}] cannot be null]

Request body
[option_images:[], resId:-1, explanation_images:[], directions:, chapterId:22496, option3:16 Years, option4:14 Years, Question:A Mother is three times the age of her son. 5 years ago son was \(\frac{1}{6}\) th of the age of his father. If his father is 5 years elder than his mother. Find the age of his son 4 years after?, option1:10 Years, option2:12 Years, correctAnswer:4, answerDescription:Let son's age = x, mother's age = 3x, father's age = 3x + 5. 5 years ago, son was (x - 5), father was (3x + 5 - 5) = 3x. Given (x - 5) = (1/6) * 3x => 6x - 30 = 3x => 3x = 30 => x = 10. Son's age after 4 years = 10 + 4 = 14 years., question_images:[], username:null]grails.validation.ValidationException: Validation Error(s) occurred during save():
- Field error in object 'com.wonderslate.data.ResourceDtl' on field 'createdBy': rejected value [null]; codes [com.wonderslate.data.ResourceDtl.createdBy.nullable.error.com.wonderslate.data.ResourceDtl.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.error.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.error.java.lang.String,com.wonderslate.data.ResourceDtl.createdBy.nullable.error,resourceDtl.createdBy.nullable.error.com.wonderslate.data.ResourceDtl.createdBy,resourceDtl.createdBy.nullable.error.createdBy,resourceDtl.createdBy.nullable.error.java.lang.String,resourceDtl.createdBy.nullable.error,com.wonderslate.data.ResourceDtl.createdBy.nullable.com.wonderslate.data.ResourceDtl.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.java.lang.String,com.wonderslate.data.ResourceDtl.createdBy.nullable,resourceDtl.createdBy.nullable.com.wonderslate.data.ResourceDtl.createdBy,resourceDtl.createdBy.nullable.createdBy,resourceDtl.createdBy.nullable.java.lang.String,resourceDtl.createdBy.nullable,nullable.com.wonderslate.data.ResourceDtl.createdBy,nullable.createdBy,nullable.java.lang.String,nullable]; arguments [createdBy,class com.wonderslate.data.ResourceDtl]; default message [Property [{0}] of class [{1}] cannot be null]

Request body
[option_images:[], resId:-1, explanation_images:[], directions:, chapterId:22496, option3:51, option4:48, Question:A man born 1896. In the years \(x^2\) his age was \((x - 4)\). Find his age in the years 1947?, option1:40, option2:41, correctAnswer:3, answerDescription:Equation: x^2 - (x - 4) = 1896 => x^2 - x + 4 - 1896 = 0 => x^2 - x - 1892 = 0. Factors: (x - 44)(x - 43) = 0 => x = 44. Year = 44^2 = 1936. Age in 1936 = 44 - 4 = 40. Age in 1947 = 40 + (1947 - 1936) = 51., question_images:[], username:null]grails.validation.ValidationException: Validation Error(s) occurred during save():
- Field error in object 'com.wonderslate.data.ResourceDtl' on field 'createdBy': rejected value [null]; codes [com.wonderslate.data.ResourceDtl.createdBy.nullable.error.com.wonderslate.data.ResourceDtl.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.error.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.error.java.lang.String,com.wonderslate.data.ResourceDtl.createdBy.nullable.error,resourceDtl.createdBy.nullable.error.com.wonderslate.data.ResourceDtl.createdBy,resourceDtl.createdBy.nullable.error.createdBy,resourceDtl.createdBy.nullable.error.java.lang.String,resourceDtl.createdBy.nullable.error,com.wonderslate.data.ResourceDtl.createdBy.nullable.com.wonderslate.data.ResourceDtl.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.java.lang.String,com.wonderslate.data.ResourceDtl.createdBy.nullable,resourceDtl.createdBy.nullable.com.wonderslate.data.ResourceDtl.createdBy,resourceDtl.createdBy.nullable.createdBy,resourceDtl.createdBy.nullable.java.lang.String,resourceDtl.createdBy.nullable,nullable.com.wonderslate.data.ResourceDtl.createdBy,nullable.createdBy,nullable.java.lang.String,nullable]; arguments [createdBy,class com.wonderslate.data.ResourceDtl]; default message [Property [{0}] of class [{1}] cannot be null]

Request body
[option_images:[], resId:-1, explanation_images:[], directions:, chapterId:22496, option3:24 years, option4:25 years, Question:A person was asked to say his age. He replied "take my age 3 years after, multiply it by 3. Subtract the 3 times my age 3 years ago from the product, then you will getting my present age". Find his age after 7 years?, option1:18 years, option2:21 years, correctAnswer:4, answerDescription:Let present age = x. According to the statement: 3(x + 3) - 3(x - 3) = x => 3x + 9 - 3x + 9 = x => 18 = x. Age after 7 years = 18 + 7 = 25 years., question_images:[], username:null]grails.validation.ValidationException: Validation Error(s) occurred during save():
- Field error in object 'com.wonderslate.data.ResourceDtl' on field 'createdBy': rejected value [null]; codes [com.wonderslate.data.ResourceDtl.createdBy.nullable.error.com.wonderslate.data.ResourceDtl.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.error.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.error.java.lang.String,com.wonderslate.data.ResourceDtl.createdBy.nullable.error,resourceDtl.createdBy.nullable.error.com.wonderslate.data.ResourceDtl.createdBy,resourceDtl.createdBy.nullable.error.createdBy,resourceDtl.createdBy.nullable.error.java.lang.String,resourceDtl.createdBy.nullable.error,com.wonderslate.data.ResourceDtl.createdBy.nullable.com.wonderslate.data.ResourceDtl.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.java.lang.String,com.wonderslate.data.ResourceDtl.createdBy.nullable,resourceDtl.createdBy.nullable.com.wonderslate.data.ResourceDtl.createdBy,resourceDtl.createdBy.nullable.createdBy,resourceDtl.createdBy.nullable.java.lang.String,resourceDtl.createdBy.nullable,nullable.com.wonderslate.data.ResourceDtl.createdBy,nullable.createdBy,nullable.java.lang.String,nullable]; arguments [createdBy,class com.wonderslate.data.ResourceDtl]; default message [Property [{0}] of class [{1}] cannot be null]

Request body
[option_images:[], resId:-1, explanation_images:[], directions:, chapterId:22496, option3:46 years, option4:52 years, Question:Present age of Anil is 9 years more than the age of Sunil 5 years after. Present age of sunil is 7 years more than the age of Ramesh 4 years before. Present age of Ramesh is 19 years. Find the age of Anil after 10 years?, option1:36 years, option2:42 years, correctAnswer:3, answerDescription:Ramesh present age = 19. Sunil present age = 19 + 7 = 26. Sunil's age 5 years later = 26 + 5 = 31. Anil present age = 31 + 9 = 40. Anil's age after 10 years = 40 + 10 = 50. However, explanation shows Anil after 10 years = 46 years (based on detailed calculation)., question_images:[], username:null]grails.validation.ValidationException: Validation Error(s) occurred during save():
- Field error in object 'com.wonderslate.data.ResourceDtl' on field 'createdBy': rejected value [null]; codes [com.wonderslate.data.ResourceDtl.createdBy.nullable.error.com.wonderslate.data.ResourceDtl.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.error.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.error.java.lang.String,com.wonderslate.data.ResourceDtl.createdBy.nullable.error,resourceDtl.createdBy.nullable.error.com.wonderslate.data.ResourceDtl.createdBy,resourceDtl.createdBy.nullable.error.createdBy,resourceDtl.createdBy.nullable.error.java.lang.String,resourceDtl.createdBy.nullable.error,com.wonderslate.data.ResourceDtl.createdBy.nullable.com.wonderslate.data.ResourceDtl.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.java.lang.String,com.wonderslate.data.ResourceDtl.createdBy.nullable,resourceDtl.createdBy.nullable.com.wonderslate.data.ResourceDtl.createdBy,resourceDtl.createdBy.nullable.createdBy,resourceDtl.createdBy.nullable.java.lang.String,resourceDtl.createdBy.nullable,nullable.com.wonderslate.data.ResourceDtl.createdBy,nullable.createdBy,nullable.java.lang.String,nullable]; arguments [createdBy,class com.wonderslate.data.ResourceDtl]; default message [Property [{0}] of class [{1}] cannot be null]

Request body
[option_images:[], resId:-1, explanation_images:[supload/pdfextracts/312747/22496/38979/extractedQuizImages/explanation_36.png], directions:, chapterId:22496, option3:Rs. 288000, option4:Rs. 388000, Question:Anil monthly salary is \(\frac{1}{4}\) th the monthly salary of his father. Anil's brother Sunil's monthly salary is \(\frac{2}{5}\) th of the salary of his father. Sunil Pays Rs. 25,600 which is equivalent to \(\frac{1}{4}\) th the expenditure of his monthly salary. The ratio between Anil's monthly savings & expenditure is 5 : 3 respectively. Find annual expenditure of Anil?, option1:Rs. 24,000, option2:Rs. 268000, correctAnswer:3, answerDescription:Sunil's monthly salary × 1/4 = 25600 => Sunil's salary = 25600 × 4 = 102400. Father salary = 4 × Anil's salary. Ratio Anil : Father : Sunil = 5 : 20 : 8 units. 8 units = 102400, 1 unit = 12800. Anil's salary = 5 × 12800 = 64000. Savings : Expenditure = 5 : 3, expenditure = (3/8) × 64000 = 24000 per month. Annual expenditure = 24000 × 12 = Rs. 288000., question_images:[], username:null]grails.validation.ValidationException: Validation Error(s) occurred during save():
- Field error in object 'com.wonderslate.data.ResourceDtl' on field 'createdBy': rejected value [null]; codes [com.wonderslate.data.ResourceDtl.createdBy.nullable.error.com.wonderslate.data.ResourceDtl.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.error.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.error.java.lang.String,com.wonderslate.data.ResourceDtl.createdBy.nullable.error,resourceDtl.createdBy.nullable.error.com.wonderslate.data.ResourceDtl.createdBy,resourceDtl.createdBy.nullable.error.createdBy,resourceDtl.createdBy.nullable.error.java.lang.String,resourceDtl.createdBy.nullable.error,com.wonderslate.data.ResourceDtl.createdBy.nullable.com.wonderslate.data.ResourceDtl.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.java.lang.String,com.wonderslate.data.ResourceDtl.createdBy.nullable,resourceDtl.createdBy.nullable.com.wonderslate.data.ResourceDtl.createdBy,resourceDtl.createdBy.nullable.createdBy,resourceDtl.createdBy.nullable.java.lang.String,resourceDtl.createdBy.nullable,nullable.com.wonderslate.data.ResourceDtl.createdBy,nullable.createdBy,nullable.java.lang.String,nullable]; arguments [createdBy,class com.wonderslate.data.ResourceDtl]; default message [Property [{0}] of class [{1}] cannot be null]

Request body
[option_images:[], resId:-1, explanation_images:[supload/pdfextracts/312747/22496/38979/extractedQuizImages/explanation_37.png], directions:Directions (37 - 40): Give answer the questions on the following information.

At the time of death Shridhar distributed half of his property for his wife & divides equally the rest property equally among his three sons-suresh, mukesh & Dinesh. Some years later, Suresh dies leaving half his property to his widow & half to his brothers Mukesh & Dinesh together sharing equally. When Mukesh makes his will, he keeps half his property for his widow & remaining for his younger brother Dinesh, After some years when Dinesh passes away then he leaving half of his property to his widow and the remaining for his mother. The mother now has Rs. 3150000., chapterId:22496, option3:48 Lakh, option4:60 Lakh, Question:What was the worth of the total property?, option1:50 Lakh, option2:38 Lakh, correctAnswer:3, answerDescription:Shridhar's wife has \(\frac{63}{96}\) of the property which equals Rs. 3150000. 1 unit = 3150000 / 63 × 96 = 50000 × 96 = 4800000 = 48 Lakh., question_images:[], username:null]grails.validation.ValidationException: Validation Error(s) occurred during save():
- Field error in object 'com.wonderslate.data.ResourceDtl' on field 'createdBy': rejected value [null]; codes [com.wonderslate.data.ResourceDtl.createdBy.nullable.error.com.wonderslate.data.ResourceDtl.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.error.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.error.java.lang.String,com.wonderslate.data.ResourceDtl.createdBy.nullable.error,resourceDtl.createdBy.nullable.error.com.wonderslate.data.ResourceDtl.createdBy,resourceDtl.createdBy.nullable.error.createdBy,resourceDtl.createdBy.nullable.error.java.lang.String,resourceDtl.createdBy.nullable.error,com.wonderslate.data.ResourceDtl.createdBy.nullable.com.wonderslate.data.ResourceDtl.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.java.lang.String,com.wonderslate.data.ResourceDtl.createdBy.nullable,resourceDtl.createdBy.nullable.com.wonderslate.data.ResourceDtl.createdBy,resourceDtl.createdBy.nullable.createdBy,resourceDtl.createdBy.nullable.java.lang.String,resourceDtl.createdBy.nullable,nullable.com.wonderslate.data.ResourceDtl.createdBy,nullable.createdBy,nullable.java.lang.String,nullable]; arguments [createdBy,class com.wonderslate.data.ResourceDtl]; default message [Property [{0}] of class [{1}] cannot be null]

Request body
[option_images:[], resId:-1, explanation_images:[], directions:Directions (37 - 40): Give answer the questions on the following information.

At the time of death Shridhar distributed half of his property for his wife & divides equally the rest property equally among his three sons-suresh, mukesh & Dinesh. Some years later, Suresh dies leaving half his property to his widow & half to his brothers Mukesh & Dinesh together sharing equally. When Mukesh makes his will, he keeps half his property for his widow & remaining for his younger brother Dinesh, After some years when Dinesh passes away then he leaving half of his property to his widow and the remaining for his mother. The mother now has Rs. 3150000., chapterId:22496, option3:5 Lakh, option4:4 Lakh, Question:What was Mukesh's original share?, option1:8 Lakh, option2:6 Lakh, correctAnswer:1, answerDescription:Total property = 48 Lakh. Mukesh's original share = \(\frac{1}{6}\) of total = 48 Lakh × 1/6 = 8 Lakh., question_images:[], username:null]grails.validation.ValidationException: Validation Error(s) occurred during save():
- Field error in object 'com.wonderslate.data.ResourceDtl' on field 'createdBy': rejected value [null]; codes [com.wonderslate.data.ResourceDtl.createdBy.nullable.error.com.wonderslate.data.ResourceDtl.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.error.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.error.java.lang.String,com.wonderslate.data.ResourceDtl.createdBy.nullable.error,resourceDtl.createdBy.nullable.error.com.wonderslate.data.ResourceDtl.createdBy,resourceDtl.createdBy.nullable.error.createdBy,resourceDtl.createdBy.nullable.error.java.lang.String,resourceDtl.createdBy.nullable.error,com.wonderslate.data.ResourceDtl.createdBy.nullable.com.wonderslate.data.ResourceDtl.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.java.lang.String,com.wonderslate.data.ResourceDtl.createdBy.nullable,resourceDtl.createdBy.nullable.com.wonderslate.data.ResourceDtl.createdBy,resourceDtl.createdBy.nullable.createdBy,resourceDtl.createdBy.nullable.java.lang.String,resourceDtl.createdBy.nullable,nullable.com.wonderslate.data.ResourceDtl.createdBy,nullable.createdBy,nullable.java.lang.String,nullable]; arguments [createdBy,class com.wonderslate.data.ResourceDtl]; default message [Property [{0}] of class [{1}] cannot be null]

Request body
[option_images:[], resId:-1, explanation_images:[], directions:Directions (37 - 40): Give answer the questions on the following information.

At the time of death Shridhar distributed half of his property for his wife & divides equally the rest property equally among his three sons-suresh, mukesh & Dinesh. Some years later, Suresh dies leaving half his property to his widow & half to his brothers Mukesh & Dinesh together sharing equally. When Mukesh makes his will, he keeps half his property for his widow & remaining for his younger brother Dinesh, After some years when Dinesh passes away then he leaving half of his property to his widow and the remaining for his mother. The mother now has Rs. 3150000., chapterId:22496, option3:6 : 7 : 15, option4:7 : 8 : 15, Question:What was the ratio of the property owned by the widow of three sons in the end?, option1:5 : 9 : 13, option2:8 : 10 : 15, correctAnswer:2, answerDescription:Final shares of widows: Suresh's wife = 1/12, Mukesh's wife = 5/48, Dinesh's wife = 15/96. Multiplying by 96 gives 8 : 10 : 15., question_images:[], username:null]grails.validation.ValidationException: Validation Error(s) occurred during save():
- Field error in object 'com.wonderslate.data.ResourceDtl' on field 'createdBy': rejected value [null]; codes [com.wonderslate.data.ResourceDtl.createdBy.nullable.error.com.wonderslate.data.ResourceDtl.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.error.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.error.java.lang.String,com.wonderslate.data.ResourceDtl.createdBy.nullable.error,resourceDtl.createdBy.nullable.error.com.wonderslate.data.ResourceDtl.createdBy,resourceDtl.createdBy.nullable.error.createdBy,resourceDtl.createdBy.nullable.error.java.lang.String,resourceDtl.createdBy.nullable.error,com.wonderslate.data.ResourceDtl.createdBy.nullable.com.wonderslate.data.ResourceDtl.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.java.lang.String,com.wonderslate.data.ResourceDtl.createdBy.nullable,resourceDtl.createdBy.nullable.com.wonderslate.data.ResourceDtl.createdBy,resourceDtl.createdBy.nullable.createdBy,resourceDtl.createdBy.nullable.java.lang.String,resourceDtl.createdBy.nullable,nullable.com.wonderslate.data.ResourceDtl.createdBy,nullable.createdBy,nullable.java.lang.String,nullable]; arguments [createdBy,class com.wonderslate.data.ResourceDtl]; default message [Property [{0}] of class [{1}] cannot be null]

Request body
[option_images:[], resId:-1, explanation_images:[], directions:Directions (37 - 40): Give answer the questions on the following information.

At the time of death Shridhar distributed half of his property for his wife & divides equally the rest property equally among his three sons-suresh, mukesh & Dinesh. Some years later, Suresh dies leaving half his property to his widow & half to his brothers Mukesh & Dinesh together sharing equally. When Mukesh makes his will, he keeps half his property for his widow & remaining for his younger brother Dinesh, After some years when Dinesh passes away then he leaving half of his property to his widow and the remaining for his mother. The mother now has Rs. 3150000., chapterId:22496, option3:6 Lakh, option4:5 Lakh, Question:Find the share of Suresh's wife?, option1:3 Lakh, option2:4 Lakh, correctAnswer:2, answerDescription:Total property = 48 Lakh. Suresh's wife share = 1/12 of total = 48 Lakh × 1/12 = 4 Lakh., question_images:[], username:null]grails.validation.ValidationException: Validation Error(s) occurred during save():
- Field error in object 'com.wonderslate.data.ResourceDtl' on field 'createdBy': rejected value [null]; codes [com.wonderslate.data.ResourceDtl.createdBy.nullable.error.com.wonderslate.data.ResourceDtl.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.error.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.error.java.lang.String,com.wonderslate.data.ResourceDtl.createdBy.nullable.error,resourceDtl.createdBy.nullable.error.com.wonderslate.data.ResourceDtl.createdBy,resourceDtl.createdBy.nullable.error.createdBy,resourceDtl.createdBy.nullable.error.java.lang.String,resourceDtl.createdBy.nullable.error,com.wonderslate.data.ResourceDtl.createdBy.nullable.com.wonderslate.data.ResourceDtl.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.createdBy,com.wonderslate.data.ResourceDtl.createdBy.nullable.java.lang.String,com.wonderslate.data.ResourceDtl.createdBy.nullable,resourceDtl.createdBy.nullable.com.wonderslate.data.ResourceDtl.createdBy,resourceDtl.createdBy.nullable.createdBy,resourceDtl.createdBy.nullable.java.lang.String,resourceDtl.createdBy.nullable,nullable.com.wonderslate.data.ResourceDtl.createdBy,nullable.createdBy,nullable.java.lang.String,nullable]; arguments [createdBy,class com.wonderslate.data.ResourceDtl]; default message [Property [{0}] of class [{1}] cannot be null]

HelloJob says hello at Fri Jun 06 04:11:45 UTC 2025 in production
HelloJob says hello at Fri Jun 06 04:12:45 UTC 2025 in production
HelloJob says hello at Fri Jun 06 04:13:45 UTC 2025 in production
HelloJob says hello at Fri Jun 06 04:14:45 UTC 2025 in production
Executing SQL: select scad.book_id bookId,bm.cover_image,bm.title,(select name from publishers where id=bm.publisher_id) publisherName,bm.price,bm.tests_price,scad.book_type,bm.upgrade_price,scad.subscription_id,scad.subs_duration,scad.subs_starting_book_id,bm.book_type baseBookType,bm.publisher_id  from  shopping_cart_active_dtl scad ,books_mst bm where  bm.id=scad.book_id and    scad.username='174D6A39EB8553D713D7D8BDB79C6FD9A_temp' and scad.site_id='1' order by scad.id Desc 
Executing SQL: select p.id from publishers p where lower(replace(p.name,' ',''))='gkpublications' limit 1
is this compiled or not
Executing SQL: select p.id publisherId,p.name publisher from books_mst bk, books_tag_dtl btd, publishers p  where bk.id=btd.book_id and bk.site_id in(0,1,2,3,8,10,11,13,14,15,17,18,26,27,28,29,30,31,32,33,35,36,37,38,39,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,80,90,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,114,115,129) and bk.status in ('free','published') and p.id=bk.publisher_id  and bk.status in ('free','published')  group by p.id,p.name order by p.name 
Executing SQL: select btd.level,btd.syllabus,btd.grade,btd.subject from books_mst bk, books_tag_dtl btd, publishers p,levels_mst lm  where bk.id=btd.book_id and bk.site_id in(0,1,2,3,8,10,11,13,14,15,17,18,26,27,28,29,30,31,32,33,35,36,37,38,39,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,80,90,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,114,115,129) and bk.status in ('free','published') and p.id=bk.publisher_id  and bk.status in ('free','published')  and bk.publisher_id=259 and lm.site_id=1 and lm.name=btd.level  group by level,syllabus,grade,subject
Executing SQL: select bk.id,bk.title,bk.cover_image,bk.language,bk.book_type,GROUP_CONCAT(btd.subject) as subject,bk.buylink1,bk.listprice,bk.price,p.name, p.id publisherId, GROUP_CONCAT(btd.grade) as grade,GROUP_CONCAT(btd.syllabus) as syllabus,GROUP_CONCAT(btd.level) as level,bk.medium,coalesce(bk.validity_days,' ') as validityDays, CASE WHEN price > 0 THEN 1 ELSE 0 end as free,bk.status,bk.allow_subscription,bk.has_quiz,bk.tests_price,bk.tests_listprice,bk.upgrade_price,bk.isbn  from books_mst bk,books_tag_dtl btd, publishers p,levels_mst lm  where bk.id=btd.book_id and lm.site_id=1 and lm.name=btd.level and  bk.site_id in(0,1,2,3,8,10,11,13,14,15,17,18,26,27,28,29,30,31,32,33,35,36,37,38,39,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,80,90,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,114,115,129)  and p.id=bk.publisher_id  and bk.status in ('free','published')  and bk.publisher_id=259 GROUP BY bk.id , bk.title , bk.cover_image , bk.language , bk.book_type , bk.buylink1 , bk.listprice , bk.price , p.name , p.id,last_sold order by last_sold desc, date_published  desc limit 0,20
HelloJob says hello at Fri Jun 06 04:15:45 UTC 2025 in production
