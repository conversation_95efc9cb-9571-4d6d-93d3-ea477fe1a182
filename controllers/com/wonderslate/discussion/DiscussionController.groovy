package com.wonderslate.discussion


import com.wonderslate.WsLibrary.WsLibraryService
import com.wonderslate.data.SiteMst
import com.wonderslate.data.SubjectMst
import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.ChaptersMst
import com.wonderslate.data.UtilService
import com.wonderslate.discussions.InstituteSubjectDtl

import com.wonderslate.usermanagement.AuthenticationToken
import com.wonderslate.usermanagement.User
import com.wonderslate.usermanagement.UserManagementService
import grails.converters.JSON
import grails.plugin.springsecurity.annotation.Secured
import grails.transaction.Transactional
//import org.bson.types.ObjectId
class DiscussionController {
    DiscussionService discussionService
    def springSecurityService
    DataProviderService dataProviderService
    UserManagementService userManagementService
    UtilService utilService
    WsLibraryService wsLibraryService
    def redisService
    def index() { }

    @Secured(['ROLE_USER'])
    @Transactional
    def addDiscussionQuestion(){
        final MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
        String title = params.title, question = params.question, siteIdStr = params.siteId, resIdStr = params.resId
        if(title == null) title = "";
        int siteId = Integer.parseInt(siteIdStr)
        long bookId = 0 ,chapterId = 0
        if(params.bookId != null) bookId = Long.parseLong(params.bookId)
        if(params.chapterId != null) chapterId = Long.parseLong(params.chapterId)
        String tags = null
        if(params.tags != null) {
            tags = params.tags
        }
        else tags = ""
        MultipartFile file = multiRequest.getFile("file")
        Boolean free = params.free, image = params.image
        SiteMst sm = dataProviderService.getSiteMst(siteId)
        def clientName=grailsApplication.config.grails.appServer.default=="eutkarsh"?grailsApplication.config.grails.appServer.siteName:sm.clientName
        def siteName=grailsApplication.config.grails.appServer.default=="eutkarsh"?grailsApplication.config.grails.appServer.default:sm.siteName
        String serverUrl = request.getScheme()+"://"+request.getServerName()+
                ("http".equals(request.getScheme()) && request.getServerPort() == 80 ||
                        "https".equals(request.getScheme()) && request.getServerPort() == 443 ? "" : ":" +
                        request.getServerPort())
        def json = ['discussionQuestion':discussionService.addDiscussionQuestions(params,title,question,siteId,bookId,chapterId,tags,file,free,image,clientName,siteName,serverUrl)]
        render json as JSON
    }

    @Secured(['ROLE_USER'])
    @Transactional
    def addAnswer(){
        final MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
        int siteId = Integer.parseInt(params.siteId)
        Long qId = Long.parseLong(params.qId)
//        short processIdentifier = Short.parseShort(params.processIdentifier)
        String answerText = params.answerText
        MultipartFile file = multiRequest.getFile("file")
        Boolean image = params.image
        SiteMst sm = dataProviderService.getSiteMst(siteId)
        def clientName=grailsApplication.config.grails.appServer.default=="eutkarsh"?grailsApplication.config.grails.appServer.siteName:sm.clientName
        def siteName=grailsApplication.config.grails.appServer.default=="eutkarsh"?grailsApplication.config.grails.appServer.default:sm.siteName
        String questionLink = request.getScheme()+"://"+request.getServerName()+
                ("http".equals(request.getScheme()) && request.getServerPort() == 80 ||
                        "https".equals(request.getScheme()) && request.getServerPort() == 443 ? "" : ":" +
                        request.getServerPort()) +"/doubts?qId="+qId+"&open=questionExplorer"
        def json = ['discussionQuestion':discussionService.addAnswer(params,qId, answerText, siteId, file, image,clientName,siteName,questionLink)]
        render json as JSON
    }

    @Secured(['ROLE_USER'])
    @Transactional
    def getMyAnswers(){
        def res = discussionService.getMyAnswers(params,Integer.parseInt(params.batchIndex))
        def user = ['profilepic':dataProviderService.getUserMst(springSecurityService.currentUser.username).profilepic,
                    'id':dataProviderService.getUserMst(springSecurityService.currentUser.username).id,
                    'userId':dataProviderService.getUserMst(springSecurityService.currentUser.username).username,
                    'name':dataProviderService.getUserMst(springSecurityService.currentUser.username).name]
        def json = ['discussionQuestion':res['questions'],'count':res['count'],'user':user]
        render json as JSON
    }

    @Secured(['ROLE_WS_CONTENT_ADMIN'])
    @Transactional
    def moderateQuestion(){
        SiteMst sm = dataProviderService.getSiteMst(Integer.parseInt(request.JSON.siteId))
        def clientName=grailsApplication.config.grails.appServer.default=="eutkarsh"?grailsApplication.config.grails.appServer.siteName:sm.clientName
        def siteName=grailsApplication.config.grails.appServer.default=="eutkarsh"?grailsApplication.config.grails.appServer.default:sm.siteName
        String serverUrl = request.getScheme()+"://"+request.getServerName()+
                ("http".equals(request.getScheme()) && request.getServerPort() == 80 ||
                        "https".equals(request.getScheme()) && request.getServerPort() == 443 ? "" : ":" +
                        request.getServerPort())
        def json = ['discussionQuestion':discussionService.moderateQuestion(params,request, clientName, siteName, serverUrl)]
        render json as JSON
    }

    @Transactional
    def getMainPageQuestions(){
        String siteId = params.siteId
        String batchIndex = params.batchIndex
        String filter = ""
        boolean isInstituteAdmin = false
        if(params.filter != null) filter = params.filter
        if(params.instituteId !=null && !"".equals(params.instituteId)){
            if(redisService.("isInstituteAdmin_"+springSecurityService.currentUser.username+"_"+new Long(params.instituteId))==null){
                dataProviderService.isInstituteAdmin(springSecurityService.currentUser.username,+new Long(params.instituteId))
            }
            if(redisService.("isInstituteAdmin_"+springSecurityService.currentUser.username+"_"+new Long(params.instituteId))=="true"){
                isInstituteAdmin=true
            }
        }
        def count
        if(filter.equals("all")) count = redisService.("recentAnswerCount_"+Integer.parseInt(siteId))
        else if(filter.equals("answered")) count = redisService.("recentAnswerFilterCount_"+Integer.parseInt(siteId))
        else if(filter.equals("unanswered")) count = redisService.("recentUnAnswerFilterCount_"+Integer.parseInt(siteId))
        def json = ['discussionQuestion':discussionService.getMainPageQuestions(params,Integer.parseInt(siteId),Integer.parseInt(batchIndex),filter),'count':count,isInstituteAdmin:isInstituteAdmin]
        render json as JSON
    }

    @Secured(['ROLE_WS_CONTENT_ADMIN'])
    @Transactional
    def getUnAnsweredDiscussionQuestions(){
        String siteId = params.siteId
        String batchIndex = params.batchIndex
        def json = ['discussionQuestion':discussionService.getUnAnsweredDiscussionQuestion(Integer.parseInt(siteId),Integer.parseInt(batchIndex)),'count':redisService.("unAnsweredQuestionsCount_"+siteId)]
        render json as JSON
    }

    @Secured(['ROLE_WS_CONTENT_ADMIN'])
    @Transactional
    def getUnTagedDiscussionQuestions(){
        String siteId = params.siteId
        String batchIndex = params.batchIndex
        def json = ['discussionQuestion':discussionService.getUnTagedDiscussionQuestions(Integer.parseInt(siteId),Integer.parseInt(batchIndex)),'count':redisService.("unTagedQuestionsCount_"+siteId)]
        render json as JSON
    }

    @Secured(['ROLE_WS_CONTENT_ADMIN'])
    @Transactional
    def getAdminAbuseQuestions(){
        String siteId = params.siteId
        String batchIndex = params.batchIndex
        def json = ['discussionQuestion':discussionService.getAbuseDiscussionQuestion(params,Integer.parseInt(siteId),Integer.parseInt(batchIndex)),'count':redisService.("abuseQuestionsCount_"+siteId)]
        render json as JSON
    }

    @Secured(['ROLE_WS_CONTENT_ADMIN'])
    @Transactional
    def getAdminIncorrectQuestions(){
        String siteId = params.siteId
        String batchIndex = params.batchIndex
        def json = ['discussionQuestion':discussionService.getIncorrectDiscussionQuestion(params,Integer.parseInt(siteId),Integer.parseInt(batchIndex)),'count':redisService.("incorrectQuestionsCount_"+siteId)]
        render json as JSON
    }

    @Secured(['ROLE_WS_CONTENT_ADMIN'])
    @Transactional
    def getUnModeratedDiscussionQuestions(){
        String siteId = params.siteId
        String batchIndex = params.batchIndex
        def json = ['discussionQuestion':discussionService.getUnModeratedDiscussionQuestions(Integer.parseInt(siteId),Integer.parseInt(batchIndex)),'count':redisService.("unModeratedQuestionsCount_"+Integer.parseInt(siteId))]
        render json as JSON
    }

//    @Transactional
//    def getUnModeratedDiscussionAnswers(){
//        String siteId = params.siteId
//        def json = ['discussionQuestion':discussionService.getUnModeratedDiscussionAnswers(Integer.parseInt(siteId))]
//        render json as JSON
//    }
//
//    @Transactional
//    def getAdminAbuseAnswers(){
//        String siteId = params.siteId
//        def json = ['discussionQuestion':discussionService.getAdminAbuseAnswers(Integer.parseInt(siteId))]
//        render json as JSON
//    }

    @Secured(['ROLE_WS_CONTENT_ADMIN'])
    @Transactional
    def getAdminAnswersTabList(){
        String siteId = params.siteId
        List questions = discussionService.getAdminAnswersTabList(Integer.parseInt(siteId),params.tab,null,"")
        def json = ['discussionQuestion':questions,'count':questions.size()]
        render json as JSON
    }

    @Transactional
    def getQuestionById(){
        String id
        if(params.qId != null) id = params.qId
        def json = discussionService.getQuestionById(request,id)
        render json as JSON
    }

    @Secured(['ROLE_USER'])
    @Transactional
    def fallowQuestion(){
        def json = ['discussionQuestion':discussionService.fallowQuestion(request)]
        render json as JSON
    }

    @Secured(['ROLE_USER'])
    @Transactional
    def getFallowingDoubts(){
//        int siteId = Integer.parseInt(params.siteId)
        def res = discussionService.getFallowingDoubts(params,Integer.parseInt(params.batchIndex))
        def json = ['discussionQuestion': res['questions'],'count':res['count']]
        render json as JSON
    }

    @Secured(['ROLE_USER'])
    @Transactional
    def upvoteQuestion(){
        def json = ['discussionQuestion':discussionService.upvoteQuestion(params,request)]
        render json as JSON
    }

    @Secured(['ROLE_USER'])
    @Transactional
    def upvoteAnswer(){
        def json = ['discussionQuestion':discussionService.upvoteAnswer(params,request)]
        render json as JSON
    }

    @Secured(['ROLE_USER'])
    @Transactional
    def archiveQuestoin(){
        def json = ['discussionQuestion':discussionService.archiveQuestoin(request)]
        render json as JSON
    }

    @Secured(['ROLE_WS_CONTENT_ADMIN','ROLE_USER'])
    @Transactional
    def editQuestin(){
        def json = ['discussionQuestion':discussionService.editQuestin(request)]
        render json as JSON
    }

    @Secured(['ROLE_WS_CONTENT_ADMIN'])
    @Transactional
    def editQuestinWithImage(){
        final MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
        String  question = params.question, siteIdStr = params.siteId
        int siteId = Integer.parseInt(siteIdStr), timestamp = Integer.parseInt(params.timestamp),machineIdentifier = Integer.parseInt(params.machineIdentifier),counter = Integer.parseInt(params.counter)
        short processIdentifier = Short.parseShort(params.processIdentifier)
        MultipartFile file = multiRequest.getFile("file")
        Boolean image = params.image
        def json = ['discussionQuestion':discussionService.editQuestinWithImage(params,timestamp,machineIdentifier,processIdentifier,counter,question,siteId,file,image)]
        render json as JSON
    }

    @Transactional
    def editAnswer(){
        def json = ['discussionQuestion':discussionService.editAnswer(request)]
        render json as JSON
    }

    @Secured(['ROLE_WS_CONTENT_ADMIN'])
    @Transactional
    def deleteQuestion(){
        SiteMst sm = dataProviderService.getSiteMst(Integer.parseInt(request.JSON.siteId))
        def clientName=grailsApplication.config.grails.appServer.default=="eutkarsh"?grailsApplication.config.grails.appServer.siteName:sm.clientName
        def siteName=grailsApplication.config.grails.appServer.default=="eutkarsh"?grailsApplication.config.grails.appServer.default:sm.siteName
        def json = ['deleted':discussionService.deleteQuestion(params,request,clientName,siteName,session['siteId'])]
        render json as JSON
    }


    @Transactional
    def deleteQuestionByInstituteAdmin(){
        def json
        boolean isInstituteAdmin = false
        if(params.instituteId !=null && !"".equals(params.instituteId)){
            if(redisService.("isInstituteAdmin_"+springSecurityService.currentUser.username+"_"+new Long(params.instituteId))==null){
                dataProviderService.isInstituteAdmin(springSecurityService.currentUser.username,+new Long(params.instituteId))
            }
            if(redisService.("isInstituteAdmin_"+springSecurityService.currentUser.username+"_"+new Long(params.instituteId))=="true"){
                isInstituteAdmin=true
            }
        }
        if(isInstituteAdmin){
            json= ['status':"OK",'deleted':discussionService.deleteQuestionByinstituteAdmin(params,session['siteId'])]
        }else{
            json=['status':"fail"]
        }
        render json as JSON

    }

    @Secured(['ROLE_WS_CONTENT_ADMIN'])
    @Transactional
    def deleteAnswer(){
        SiteMst sm = dataProviderService.getSiteMst(Integer.parseInt(request.JSON.siteId))
        def clientName=grailsApplication.config.grails.appServer.default=="eutkarsh"?grailsApplication.config.grails.appServer.siteName:sm.clientName
        def siteName=grailsApplication.config.grails.appServer.default=="eutkarsh"?grailsApplication.config.grails.appServer.default:sm.siteName
        def json = ['deleted':discussionService.deleteAnswer(params,request,clientName,siteName,session['siteId'])]
        render json as JSON
    }

    @Secured(['ROLE_WS_CONTENT_ADMIN'])
    @Transactional
    def duplicateQuestion(){
        def json = ['deleted':discussionService.duplicateQuestion(params,request)]
        render json as JSON
    }

    @Secured(['ROLE_WS_CONTENT_ADMIN'])
    @Transactional
    def duplicateAnswer(){
        def json = ['deleted':discussionService.duplicateAnswer(params,request)]
        render json as JSON
    }
    @Transactional
    def addAbuse(){
        def json = ['discussionQuestion':discussionService.addAbuse(params,request)]
        render json as JSON
    }

    @Transactional
    def addIncorrect(){
        def json = ['discussionQuestion':discussionService.addIncorrect(params,request)]
        render json as JSON
    }

    @Transactional
    def addAnswerIncorrect(){
        def json = ['discussionQuestion':discussionService.addAnswerIncorrect(params,request)]
        render json as JSON
    }

    @Transactional
    def addAnswerAbuse(){
        def json = ['discussionQuestion':discussionService.addAnswerAbuse(params,request)]
        render json as JSON
    }

    @Secured(['ROLE_WS_CONTENT_ADMIN'])
    @Transactional
    def confirmAnswerIncorrect(){
        def json = ['discussionQuestion':discussionService.confirmAnswerIncorrect(request)]
        render json as JSON
    }

    @Secured(['ROLE_WS_CONTENT_ADMIN'])
    @Transactional
    def removeAnswerIncorrect(){
        def json = ['discussionQuestion':discussionService.removeAnswerIncorrect(request)]
        render json as JSON
    }

    @Secured(['ROLE_WS_CONTENT_ADMIN'])
    @Transactional
    def moderateAnswer(){
        SiteMst sm = dataProviderService.getSiteMst(Integer.parseInt(request.JSON.siteId))
        def clientName=grailsApplication.config.grails.appServer.default=="eutkarsh"?grailsApplication.config.grails.appServer.siteName:sm.clientName
        def siteName=grailsApplication.config.grails.appServer.default=="eutkarsh"?grailsApplication.config.grails.appServer.default:sm.siteName
        String serverUrl = request.getScheme()+"://"+request.getServerName()+
                ("http".equals(request.getScheme()) && request.getServerPort() == 80 ||
                        "https".equals(request.getScheme()) && request.getServerPort() == 443 ? "" : ":" +
                        request.getServerPort())
        def json = ['discussionQuestion':discussionService.moderateAnswer(params,request,clientName,siteName,serverUrl)]
        render json as JSON
    }

    @Secured(['ROLE_WS_CONTENT_ADMIN'])
    @Transactional
    def confirmAnswerAbuse(){
        def json = ['discussionQuestion':discussionService.confirmAnswerAbuse(request)]
        render json as JSON
    }

    @Secured(['ROLE_WS_CONTENT_ADMIN'])
    @Transactional
    def removeAnswerAbuse(){
        def json = ['discussionQuestion':discussionService.removeAnswerAbuse(request)]
        render json as JSON
    }

    @Secured(['ROLE_WS_CONTENT_ADMIN'])
    @Transactional
    def confirmAbuse(){
        def json = ['discussionQuestion':discussionService.confirmAbuse(request.JSON.id,request.JSON.siteId)]
        render json as JSON
    }

    @Secured(['ROLE_WS_CONTENT_ADMIN'])
    @Transactional
    def confirmIncorrect(){
        def json = ['discussionQuestion':discussionService.confirmIncorrect(request)]
        render json as JSON
    }

    @Secured(['ROLE_WS_CONTENT_ADMIN'])
    @Transactional
    def removeAbuse(){
        def json = ['discussionQuestion':discussionService.removeAbuse(params,request.JSON.id,request.JSON.siteId)]
        render json as JSON
    }

    @Secured(['ROLE_WS_CONTENT_ADMIN'])
    @Transactional
    def removeIncorrect(){
        def json = ['discussionQuestion':discussionService.removeIncorrect(params,request.JSON.id,request.JSON.siteId)]
        render json as JSON
    }



    @Transactional
    def questionGlobalSearch(){
        String keyWord = params.query, type = params.type, paramFilter = params.paramFilter
        List tags = null
        if(params.tagsList != null) {
            String tagStr = params.tagsList
            String [] tagsArr =tagStr.split(",")
            tags = Arrays.asList(tagsArr)
        }
        else tags = new ArrayList()
        boolean isInstituteAdmin = false
        if(params.instituteId !=null && !"".equals(params.instituteId)){
            if(redisService.("isInstituteAdmin_"+springSecurityService.currentUser.username+"_"+new Long(params.instituteId))==null){
                dataProviderService.isInstituteAdmin(springSecurityService.currentUser.username,+new Long(params.instituteId))
            }
            if(redisService.("isInstituteAdmin_"+springSecurityService.currentUser.username+"_"+new Long(params.instituteId))=="true"){
                isInstituteAdmin=true
            }
        }
        List questions = discussionService.questionGlobalSearch(params,keyWord.toLowerCase(),Integer.parseInt(params.siteId),type,tags,paramFilter)
        if(questions == null) questions = new ArrayList()
        if(type.equals("search")) {
            def json = ['searchList':questions,'status':  questions.size() > 0 ? "OK" : "Nothing present",isInstituteAdmin:isInstituteAdmin]
            render json as JSON
        }
        else if(type.equals("questions") || type.equals("filter")) {
            def json = ['discussionQuestion':questions,'count':questions.size(),isInstituteAdmin:isInstituteAdmin]
            render json as JSON
        }

    }

    @Transactional
    def getQuestionDetailsById(){
//        def _id = new ObjectId(request.JSON.timestamp,request.JSON.machineIdentifier,(short)request.JSON.processIdentifier,request.JSON.counter)
//        def question = DiscussionQuestions.findById(_id)
//        render question as JSON
    }

    @Secured(['ROLE_USER'])
    @Transactional
    def downVoteQuestion(){
        def json = ['discussionQuestion':discussionService.downVote(params,request)]
        render json as JSON
    }

    @Secured(['ROLE_USER'])
    @Transactional
    def downVoteAnswer(){
        def json = ['discussionQuestion':discussionService.downVoteAnswer(params,request)]
        render json as JSON
    }

    @Secured(['ROLE_USER'])
    @Transactional
    def unFallowQuestion(){
        def json = ['discussionQuestion':discussionService.unFallow(request)]
        render json as JSON
    }

//    @Secured(['ROLE_WS_CONTENT_ADMIN'])
//    @Transactional
//    def getAdminAnswers(){
//        String keyword = params.keyword, type =params.type
//        def json = null
//        json = ['discussionQuestion':discussionService.getAdminAnswers(keyword,type),'user':dataProviderService.getUserMst(springSecurityService.currentUser.username)]
//        render json as JSON
//    }

    @Secured(['ROLE_WS_CONTENT_ADMIN'])
    @Transactional
    def getAdminSearchSuggestion(){
        String siteId = params.siteId, searchKeyWord = params.query, type=params.type
        def json = null
        List questions = null
        if(params.filterValue == "UM" || params.filterValue == "UA" || params.filterValue == "RA" || params.filterValue == "RI" || params.filterValue == "UT") {
            questions = discussionService.unModeratedQuestionSearchSuggestion(params,Integer.parseInt(siteId),searchKeyWord.toLowerCase(),type,params.filterValue)
        }else if(params.filterValue == "UMA" || params.filterValue == "RAA" || params.filterValue == "RIA") questions = discussionService.getAdminAnswersTabList(Integer.parseInt(siteId),params.filterValue,searchKeyWord.toLowerCase(),type)
        if(questions == null) questions = new ArrayList()
        if(type.equals("search")) json = ['searchList':questions,'status':  questions.size() > 0 ? "OK" : "Nothing present"]
        else if (type.equals("questions")) json = ['discussionQuestion':questions,'count':questions.size()]

        render json as JSON
    }

    def getChapterlistByBookId(){
        List chaptersMst = ChaptersMst.findAllByBookId(new Long(params.bookId))
        def chapters = chaptersMst.collect{chapter ->
            return [chapter_name:chapter.name,chapter_id:chapter.id]
        }
        def json =
                [       'chapters':chapters,
                ]
        render json as JSON

    }

    @Secured(['ROLE_WS_CONTENT_ADMIN'])
    @Transactional
    def updateAdminMultipleSelect(){
        SiteMst sm = dataProviderService.getSiteMst(Integer.parseInt(request.JSON.siteId))
        def clientName=grailsApplication.config.grails.appServer.default=="eutkarsh"?grailsApplication.config.grails.appServer.siteName:sm.clientName
        def siteName=grailsApplication.config.grails.appServer.default=="eutkarsh"?grailsApplication.config.grails.appServer.default:sm.siteName
        String serverUrl = request.getScheme()+"://"+request.getServerName()+
                ("http".equals(request.getScheme()) && request.getServerPort() == 80 ||
                        "https".equals(request.getScheme()) && request.getServerPort() == 443 ? "" : ":" +
                        request.getServerPort())
        def json = ['discussionQuestion':discussionService.updateAdminMultipleSelect(params,request,clientName,siteName,serverUrl)]
        render json as JSON
    }

    @Secured(['ROLE_WS_CONTENT_ADMIN'])
    @Transactional
    def deleteMultipleQuestions(){
        def json = ['discussionQuestion':discussionService.deleteMultipleQuestions(params,request)]
        render json as JSON
    }

    @Secured(['ROLE_USER'])
    @Transactional
    def getSubjectDrList(){
        def json = ['subjectsList':discussionService.getSubjectDrList()]
        render json as JSON
    }

    @Transactional
    def updateRecentAnsweredDiscussionQuestionCache(){
        def json = ['discussionQuestion':discussionService.updateRecentAnsweredDiscussionQuestionCache(params,Integer.parseInt(params.siteId))]
        render json as JSON
    }

    @Transactional
    def showDoubtImage(String id, String fileName, String type, String imgType) {
        try {
            if (fileName != null && !"null".equals(fileName) && fileName.length() > 0) {
                String picFileName = fileName.substring(0, fileName.indexOf(".")) + '_' + imgType + fileName.substring(fileName.indexOf("."))
                def file = new File("upload/" + type + "/" + id + "/processed/" + picFileName)

                if (file.exists()) {
                    response.setContentType("APPLICATION/OCTET-STREAM")
                    response.setHeader("Content-Disposition", "Attachment;Filename=\"${fileName}\"")
                    def fileInputStream = new FileInputStream(file)
                    def outputStream = response.getOutputStream()
                    byte[] buffer = new byte[4096];
                    int len;
                    while ((len = fileInputStream.read(buffer)) > 0) {
                        outputStream.write(buffer, 0, len);
                    }

                    outputStream.flush()
                    outputStream.close()
                    fileInputStream.close()
                } else render "";
            } else render "";
        }
        catch (Exception e)
        {
            println("Exception in showDoubtImage "+e.toString())
            render "";

        }
    }

    @Transactional
    def showDoubtRealImage(String id, String fileName, String type, String imgType) {
        try {
            if (fileName != null && !"null".equals(fileName) && fileName.length() > 0) {
//                String picFileName = fileName.substring(0, fileName.indexOf(".")) + '_' + imgType + fileName.substring(fileName.indexOf("."))
                def file = new File("upload/" + type + "/" + id + "/" + fileName)

                if (file.exists()) {
                    response.setContentType("APPLICATION/OCTET-STREAM")
                    response.setHeader("Content-Disposition", "Attachment;Filename=\"${fileName}\"")
                    def fileInputStream = new FileInputStream(file)
                    def outputStream = response.getOutputStream()
                    byte[] buffer = new byte[4096];
                    int len;
                    while ((len = fileInputStream.read(buffer)) > 0) {
                        outputStream.write(buffer, 0, len);
                    }

                    outputStream.flush()
                    outputStream.close()
                    fileInputStream.close()
                } else render "";
            } else render "";
        }
        catch (Exception e)
        {
            println("Exception in showDoubtImage "+e.toString())
            render "";

        }
    }

    @Transactional
    def showAnswerImage(long qId, long ansId, String fileName, String type, String imgType) {
        try {
            def _id = qId
            String id = _id.toString()
            if (fileName != null && !"null".equals(fileName) && fileName.length() > 0) {
                String picFileName = fileName.substring(0, fileName.indexOf(".")) + '_' + imgType + fileName.substring(fileName.indexOf("."))
                def file = new File("upload/" + type + "/" + id + "/answers/"+ansId+"/answers-processed/" + picFileName)

                if (file.exists()) {
                    response.setContentType("APPLICATION/OCTET-STREAM")
                    response.setHeader("Content-Disposition", "Attachment;Filename=\"${fileName}\"")
                    def fileInputStream = new FileInputStream(file)
                    def outputStream = response.getOutputStream()
                    byte[] buffer = new byte[4096];
                    int len;
                    while ((len = fileInputStream.read(buffer)) > 0) {
                        outputStream.write(buffer, 0, len);
                    }

                    outputStream.flush()
                    outputStream.close()
                    fileInputStream.close()
                } else render "";
            } else render "";
        }
        catch (Exception e)
        {
            println("Exception in showAnswerImage "+e.toString())
            render "";

        }
    }

    @Transactional
    def showAnswerRealImage(long qId, long ansId ,String fileName, String type) {
        try {
            def _id = qId
            String id = _id.toString()
            if (fileName != null && !"null".equals(fileName) && fileName.length() > 0) {
                def file = new File("upload/" + type + "/" + id + "/answers/"+ansId +"/"+ fileName)

                if (file.exists()) {
                    response.setContentType("APPLICATION/OCTET-STREAM")
                    response.setHeader("Content-Disposition", "Attachment;Filename=\"${fileName}\"")
                    def fileInputStream = new FileInputStream(file)
                    def outputStream = response.getOutputStream()
                    byte[] buffer = new byte[4096];
                    int len;
                    while ((len = fileInputStream.read(buffer)) > 0) {
                        outputStream.write(buffer, 0, len);
                    }

                    outputStream.flush()
                    outputStream.close()
                    fileInputStream.close()
                } else render "";
            } else render "";
        }
        catch (Exception e)
        {
            println("Exception in showAnswerImage "+e.toString())
            render "";

        }
    }

    @Transactional
    def discussionBoard(){
        User user = null
        String userName = ""
        if(session['siteId']==null) session['siteId'] = new Integer(1)
        if(params.tokenId!=null){
            if(springSecurityService.currentUser==null) {
                String tokenId = params.tokenId
                AuthenticationToken authenticationToken = AuthenticationToken.findByToken(tokenId)
                if(authenticationToken!=null){
                    springSecurityService.reauthenticate(authenticationToken.username)
                }
            }
            session['appType']=params.appType
        }
        List usersInstituteDtl
        if(springSecurityService.currentUser != null) {
            user = dataProviderService.getUserMst(springSecurityService.currentUser.username)
            userName = user.username

             usersInstituteDtl=wsLibraryService.getUsersInstitute(request,session);
            //check librarian
            boolean isLibrarian = false
            if (session.getAttribute("userdetails") == null) {
                session["userdetails"] = User.findByUsername(springSecurityService.currentUser.username)

            }
            user = session["userdetails"]
            if(user.authorities.any {
                it.authority == "ROLE_LIBRARY_ADMIN"
            }) {
                isLibrarian = true
            }
            if(usersInstituteDtl!=null){
                for(int i=0;i<usersInstituteDtl.size();i++){
                    if("false".equals(usersInstituteDtl[i].fullLibraryView)&&!isLibrarian&&"Default".equals(usersInstituteDtl[i].batchName)) usersInstituteDtl.remove(i--)
                }
            }
        }
        if(redisService.("bookCategories_"+session['siteId'])==null) {
            dataProviderService.getBookCategories(session['siteId'])
        }
       List subjectMstList
        if(params.instituteId!=null && !"".equals(params.instituteId)){
            subjectMstList = InstituteSubjectDtl.findAllByInstituteId(new Long(params.instituteId))
        }else {
             subjectMstList = SubjectMst.list()
        }

        ["School":redisService.("bookCategories_"+session['siteId']+"_School"),
         "College":redisService.("bookCategories_"+session['siteId']+"_College"),
         "CompetitiveExams":redisService.("bookCategories_"+session['siteId']+"_CompetitiveExams"),
         "user":userName, "title":"Doubts - Wonderslate","subjects": subjectMstList, commonTemplate:"true",institutes:usersInstituteDtl?usersInstituteDtl:null]
    }

    def pushQuestionCreatedDateIntoQaList(){
        discussionService.pushQuestionCreatedDateIntoQaList()
    }

}
import org.springframework.web.multipart.MultipartFile

import org.springframework.web.multipart.MultipartHttpServletRequest
