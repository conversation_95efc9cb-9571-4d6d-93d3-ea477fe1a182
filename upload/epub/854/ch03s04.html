<?xml version="1.0" encoding="utf-8" ?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">

<html xmlns="http://www.w3.org/1999/xhtml"><head><title>Scanning with DAVID Laserscanner</title><link rel="stylesheet" href="epub.css" type="text/css"/><meta name="generator" content="DocBook XSL Stylesheets V1.75.2"/></head><body id="page"><div class="section" title="Scanning with DAVID Laserscanner"><div class="titlepage"><div><div><h1 class="title"><a id="ch03lvl1sec38"/>Scanning with DAVID Laserscanner</h1></div></div></div><p>In this recipe, we'll learn how to scan an object from multiple views by carefully moving the object around a central point. <a id="id178" class="indexterm"/>Using the free edition of DAVID Laserscanner, we'll save the scans as low-resolution <code class="literal">.obj</code> files.</p><div class="section" title="Getting ready"><div class="titlepage"><div><div><h2 class="title"><a id="ch03lvl2sec78"/>Getting ready</h2></div></div></div><p>The camera should be set up and successfully calibrated. Before we make our first scan, we need to make a minor adjustment in our camera exposure. Position the toy block on its base, as close as possible to the calibration corner. Dim the lights and turn the red-line laser on. Focus the line on the block and adjust the camera exposure until only the line is visible and sharp. Try to keep the background as dark as possible.</p></div><div class="section" title="How to do it..."><div class="titlepage"><div><div><h2 class="title"><a id="ch03lvl2sec79"/>How to do it...</h2></div></div></div><p>We will proceed as follows:</p><div class="orderedlist"><ol class="orderedlist arabic"><li class="listitem">From the side menu, select <span class="strong"><strong>3D Laser Scanning</strong></span>. Under <span class="strong"><strong>Scanning</strong></span>, choose <span class="strong"><strong>Red</strong></span> in the rollout-box for <span class="strong"><strong>Laser Color</strong></span>.</li><li class="listitem">Under <span class="strong"><strong>Result Filtering</strong></span>, keep the default values as shown in the following screenshot:<div class="mediaobject"><img src="graphics/9888OS_03_07.jpg" alt="How to do it..."/></div></li><li class="listitem">From the top menu under <span class="strong"><strong>Visibility</strong></span>, select <span class="strong"><strong>Depth Map</strong></span> from the rollout-box.</li><li class="listitem">Hold the laser above the camera and position the laser line so that it falls across the top of the calibration corner. Try to keep the laser positioned from the same spot. <a id="id179" class="indexterm"/>From the menu, click on <span class="strong"><strong>Start</strong></span>.</li><li class="listitem">Very slowly, tilt the laser line down from the fixed position. The line needs to sweep down across the entire object, as shown in the following image:<div class="mediaobject"><img src="graphics/9888OS_03_08.jpg" alt="How to do it..."/></div></li><li class="listitem">On the screen, you can see a live image of the color depth map of the scanned object. If the areas are not clear, a second pass could help.<div class="mediaobject"><img src="graphics/9888OS_03_09.jpg" alt="How to do it..."/></div></li><li class="listitem">When the scan is complete, click on <span class="strong"><strong>Stop</strong></span>. Save the scan as an <code class="literal">.obj</code> file. With the free edition of the program, a warning window will appear, alerting you that your scan will save at a reduced resolution.</li><li class="listitem">Turn the block<a id="id180" class="indexterm"/> about 45 degrees clockwise. Be careful not to disturb your camera setup. Repeat the scanning process until you have a total of at least 6 scans representing a clear view around the block.</li><li class="listitem">Repeat the process for the coin. Support the coin vertically on its edge, but only make one scan of the surface. We're only concerned about one side of the coin for this exercise.</li></ol></div></div><div class="section" title="How it works..."><div class="titlepage"><div><div><h2 class="title"><a id="ch03lvl2sec80"/>How it works...</h2></div></div></div><p>Before we save each scan, we can alter three variables in <span class="strong"><strong>Result Filtering</strong></span>. The following comparisons will illustrate the changes made when adjusting <span class="strong"><strong>Interpolation</strong></span>, <span class="strong"><strong>Smooth Average</strong></span>, and <span class="strong"><strong>Median Average</strong></span>.</p><p>By adjusting <span class="strong"><strong>Interpolation</strong></span>, the program will fill holes in the scan. The following image shows the <span class="strong"><strong>Interpolation</strong></span> values from <span class="strong"><strong>0</strong></span> to <span class="strong"><strong>2</strong></span>, without the smoothing filters:</p><div class="mediaobject"><img src="graphics/9888OS_03_10.jpg" alt="How it works..."/></div><p>By adjusting <span class="strong"><strong>Smooth Average</strong></span>, the program will smooth the scan. The following image shows the <span class="strong"><strong>Smooth Average</strong></span> values from <span class="strong"><strong>1</strong></span> to <span class="strong"><strong>3</strong></span>:</p><div class="mediaobject"><img src="graphics/9888OS_03_11.jpg" alt="How it works..."/></div><p>By adjusting<a id="id181" class="indexterm"/> the <span class="strong"><strong>Smooth Median</strong></span>, the program will smooth the scan. The following image shows the <span class="strong"><strong>Smooth Median</strong></span> values from <span class="strong"><strong>1</strong></span> to <span class="strong"><strong>3</strong></span>:</p><div class="mediaobject"><img src="graphics/9888OS_03_12.jpg" alt="How it works..."/></div><p>Once we make our final adjustments, we can save the scan as an <code class="literal">.obj</code> file. We'll now have a collection of scans that are essentially shells representing multiple views of our object. Using all the shells, we'll stitch them together to make a complete model. In DAVID Laserscanner, there's a feature called ShapeFusion that provides tools that will clean the scans and merge them together. The free edition will allow access to these tools, but all the work will not be saved. In the following recipes, we'll learn how to stitch the shells together with another program called MeshLab.</p></div><div class="section" title="There's more..."><div class="titlepage"><div><div><h2 class="title"><a id="ch03lvl2sec81"/>There's more...</h2></div></div></div><p>Depending on the hardware you utilize, DAVID Laserscanner is a very versatile system that can be refined for extraordinary results. Changing from an inexpensive red-line laser to a more expensive<a id="id182" class="indexterm"/> green or blue laser will increase the scanning sensitivity. Controlling the laser movement precisely with a stepper motor equipped with a planetary gearbox will also give better results. For illustrative purposes, a comparison between the free version of DAVID that utilizes the inexpensive equipment, covered in the preceding recipes, and the Pro version of DAVID that utilizes a motorized green laser is shown in the following image:</p><div class="mediaobject"><img src="graphics/9888OS_03_13.jpg" alt="There's more..."/></div><p>The fine detail of the coin (remember that it only measures 30 mm across!) is captured with a 1024 x 768 resolution, provided that it is only available in the commercial version. Purchasing the program and upgrading the equipment is worth the investment if your needs require finer detail scanning.</p><div class="mediaobject"><img src="graphics/9888OS_03_14.jpg" alt="There's more..."/></div></div></div></body></html>
