<?xml version="1.0" encoding="utf-8" ?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">

<html xmlns="http://www.w3.org/1999/xhtml"><head><title>Using extruding options in TopMod</title><link rel="stylesheet" href="epub.css" type="text/css"/><meta name="generator" content="DocBook XSL Stylesheets V1.75.2"/></head><body id="page"><div class="section" title="Using extruding options in TopMod"><div class="titlepage"><div><div><h1 class="title"><a id="ch06lvl1sec78"/>Using extruding options in TopMod</h1></div></div></div><p>TopMod <a id="id405" class="indexterm"/>provides<a id="id406" class="indexterm"/> a library of seven different primitives. They are a series of geometric forms that are the simplest objects a 3D program can produce.</p><p>In this <a id="id407" class="indexterm"/>recipe, we're going to make some interesting forms by building them from a dodecahedron. We'll use <a id="id408" class="indexterm"/>two extrude tools: the Doo Sabin Extrude<a id="id409" class="indexterm"/> and<a id="id410" class="indexterm"/> Dome Extrude Modes. They <a id="id411" class="indexterm"/>can be seen respectively in the following circled icons:</p><div class="mediaobject"><img src="graphics/9888OS_06_01.jpg" alt="Using extruding options in TopMod"/></div><div class="section" title="Getting ready"><div class="titlepage"><div><div><h2 class="title"><a id="ch06lvl2sec182"/>Getting ready</h2></div></div></div><p>You'll need to select the dodecahedron from the primitive's icon tray. Zoom to a comfortable work size.</p><p>Before we get started, let's look at the selection tools. These are called Selection Masks. We'll be using three out of the four available masks that are located in the icon tray under the program's menu. They can be seen in the following image:</p><div class="mediaobject"><img src="graphics/9888OS_06_02.jpg" alt="Getting ready"/></div><p>From here, we'll be <a id="id412" class="indexterm"/>able to select three of the major components of a polygon mesh: vertices, faces, and edges, as show in the following image:</p><div class="mediaobject"><img src="graphics/9888OS_06_03.jpg" alt="Getting ready"/></div><p>Click on any <a id="id413" class="indexterm"/>part of the model to select it and click and hold <span class="emphasis"><em>Ctrl</em></span> to deselect it.</p><p>By choosing <span class="strong"><strong>Selection</strong></span> from the menu, we'll be able to choose the options that control and refine our selection process.</p></div><div class="section" title="How to do it..."><div class="titlepage"><div><div><h2 class="title"><a id="ch06lvl2sec183"/>How to do it...</h2></div></div></div><p>We will proceed as follows:</p><div class="orderedlist"><ol class="orderedlist arabic"><li class="listitem">
Choose the <span class="strong"><strong>Select Faces</strong></span> icon [<span class="inlinemediaobject"><img src="graphics/9888OS_06_04.jpg" alt="How to do it..."/></span>].
</li><li class="listitem">Then, select one face as shown in the following image:<div class="mediaobject"><img src="graphics/9888OS_06_05.jpg" alt="How to do it..."/></div></li><li class="listitem">Go to the <span class="strong"><strong>Extrusions Tools</strong></span> tray and select the <span class="strong"><strong>Dome Extrude Mode</strong></span> icon. Take note of the<a id="id414" class="indexterm"/> floating <span class="strong"><strong>Tools Options</strong></span> window. For now, we'll leave the defaults alone. Click on <span class="strong"><strong>Perform Extrusion</strong></span>.</li><li class="listitem">Now, go to the menu and choose <span class="strong"><strong>Selection</strong></span>. From here, choose <span class="strong"><strong>Select Multiple</strong></span> from<a id="id415" class="indexterm"/> the selection menu. Examine the following image. We can see the results of dome extrusion in the image on the left-hand side. Select the faces as shown in the image on the right-hand side.<div class="mediaobject"><img src="graphics/9888OS_06_06.jpg" alt="How to do it..."/></div></li><li class="listitem">
Go to the <span class="strong"><strong>Extrude Mode</strong></span> tray and choose the <span class="strong"><strong>Doo Sabin Extrude Mode</strong></span> icon [<span class="inlinemediaobject"><img src="graphics/9888OS_06_07.jpg" alt="How to do it..."/></span>]
</li><li class="listitem">From the <span class="strong"><strong>Tool Options</strong></span> window, choose <span class="strong"><strong>Extrude Selected Faces</strong></span>, keeping the default values. You should have a model similar to the one shown in the following image:<div class="mediaobject"><img src="graphics/9888OS_06_08.jpg" alt="How to do it..."/></div></li><li class="listitem">Save the model. Let's label the file as <code class="literal">base</code>, as we'll be using it several times in this recipe.</li><li class="listitem">Now, select <a id="id416" class="indexterm"/>the tips of all of the five arms. The tip is shown in the following image on the left-hand side. Change the <span class="strong"><strong>Length</strong></span> field in the <span class="strong"><strong>Tool Options</strong></span> window from <code class="literal">2.000</code> to <code class="literal">.800</code>.<div class="mediaobject"><img src="graphics/9888OS_06_09.jpg" alt="How to do it..."/></div></li><li class="listitem">Click on <span class="strong"><strong>Extrude</strong></span>. The results should look like the preceding image on the right-hand side. <a id="id417" class="indexterm"/>Save the model. We'll be using it later in this chapter.</li><li class="listitem">Now, reload the base model and select all the five tips again.</li><li class="listitem">Set the <span class="strong"><strong>Length</strong></span> field to <code class="literal">.400</code> and select <span class="strong"><strong>Extrude</strong></span>. Save the model. We'll be using it later.</li><li class="listitem">Now, reload the base model again. We'll remesh the geometry to make it smoother. Go to the menu and select <span class="strong"><strong>Remeshing</strong></span>. From <span class="strong"><strong>4-Conversion</strong></span>, choose <span class="strong"><strong>Catmull Clark</strong></span>. Click on <span class="strong"><strong>Perform Remeshing</strong></span> from the <span class="strong"><strong>Tool Options</strong></span> window.</li><li class="listitem">Repeat the remeshing two more times. Now, your model should look like the one shown in the following image:<div class="mediaobject"><img src="graphics/9888OS_06_10.jpg" alt="How to do it..."/></div></li><li class="listitem">Save the model for printing. We'll be using it as a test model later in this chapter.</li></ol></div></div><div class="section" title="How it works..."><div class="titlepage"><div><div><h2 class="title"><a id="ch06lvl2sec184"/>How it works...</h2></div></div></div><p>There are eight <a id="id418" class="indexterm"/>different types of extrusions available. Each of the extrusion modes may have an adjustment for <span class="strong"><strong>Length</strong></span>, <span class="strong"><strong>Twist</strong></span>, <span class="strong"><strong>Rotate</strong></span>, <span class="strong"><strong>Scale</strong></span>, and <span class="strong"><strong>Segments</strong></span>. The <span class="strong"><strong>Icosahedral Extrude Mode</strong></span> and <span class="strong"><strong>Dodecahedral Extrude Mode</strong></span> have additional extrudes for <span class="strong"><strong>Length</strong></span> and also an additional adjustment for <span class="strong"><strong>Angle</strong></span>.</p><p>For example, let's examine <span class="strong"><strong>Icosahedral Extrude Mode</strong></span>. We can see the default setting in the object at the center of the following image. By changing the default extrusion angle's value from <code class="literal">50.0</code> to a lower and higher angle, the extrusion will change.</p><div class="mediaobject"><img src="graphics/9888OS_06_11.jpg" alt="How it works..."/></div><p>More modifications can be made by changing the length value to <code class="literal">5.000</code> in <span class="strong"><strong>Length 1</strong></span> (the default is <code class="literal">.5000</code>), <span class="strong"><strong>Length 2</strong></span> (the default is <code class="literal">.7000</code>), and <span class="strong"><strong>Length 3</strong></span> (the default is <code class="literal">.8000</code>). We can see the results in the following image:</p><div class="mediaobject"><img src="graphics/9888OS_06_12.jpg" alt="How it works..."/></div><p>Making extrusions with some settings may produce forms with intersecting triangles and create other <a id="id419" class="indexterm"/>nonmanifold issues. The preceding image on the right-hand side has nonmanifold issues as well as the model that was extruded with a 25.0 angle extrusion.</p><p>These models will require repair if they are to be 3D printed; we'll learn how to fix these types of issues in <a class="link" href="ch08.html" title="Chapter&#xA0;8.&#xA0;Troubleshooting Issues in 3D Modeling">Chapter 8</a>, <span class="emphasis"><em>Troubleshooting Issues in 3D Modeling</em></span>.</p></div></div></body></html>
