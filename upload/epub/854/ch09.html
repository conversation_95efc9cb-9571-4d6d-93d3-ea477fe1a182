<?xml version="1.0" encoding="utf-8" ?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">

<html xmlns="http://www.w3.org/1999/xhtml"><head><title>Chapter&#xA0;9.&#xA0;Troubleshooting Issues in 3D Printing</title><link rel="stylesheet" href="epub.css" type="text/css"/><meta name="generator" content="DocBook XSL Stylesheets V1.75.2"/></head><body id="page"><div class="chapter" title="Chapter&#xA0;9.&#xA0;Troubleshooting Issues in 3D Printing"><div class="titlepage"><div><div><h1 class="title"><a id="ch09"/>Chapter&#xA0;9.&#xA0;Troubleshooting Issues in 3D Printing</h1></div></div></div><p>In this chapter, we will cover the following recipes:</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem" style="list-style-type: disc">Leveling the build platform</li><li class="listitem" style="list-style-type: disc">Taking proper care of the build platform surface</li><li class="listitem" style="list-style-type: disc">Troubleshooting issues with the heat bed</li><li class="listitem" style="list-style-type: disc">Troubleshooting issues with the extruder</li><li class="listitem" style="list-style-type: disc">Troubleshooting issues with the hot end</li><li class="listitem" style="list-style-type: disc">Troubleshooting issues with the x, y, and z axes</li><li class="listitem" style="list-style-type: disc">Troubleshooting issues with the motor system</li></ul></div><div class="section" title="Introduction"><div class="titlepage"><div><div><h1 class="title"><a id="ch09lvl1sec113"/>Introduction</h1></div></div></div><p>If you have experience with 3D printing using a RepRap printer, it will come as no surprise that occasionally, something will go wrong. You may find that it's not the result of a poor slicer profile or a poorly modeled object but a failure of the mechanical function of your printer. You might observe some strange behavior that quite possibly degrades the results of your print, or your printer may not work at all.</p><p>Usually, this is the result of a part or connection wiggling loose or the tension of a belt becoming slack. Sometimes, it's as simple as a screw or bolt falling off. If the printer is moved about often, there's a chance for it to be knocked out of alignment, or even worse, a part might be unknowingly damaged. After all, if you're using a true RepRap-designed printer, it's a bare-bones contraption, without the shielding of a hard case. It's very easy to have a wire snag on something and in turn, create a possible malfunction in the printer's activities.</p><p>This chapter will focus on the basic components of a 3D printer system that is constructed with the <span class="emphasis"><em>x</em></span>, <span class="emphasis"><em>y</em></span>, and <span class="emphasis"><em>z</em></span> axes, which move using belts and threaded rods. Many of the concepts can be applied to printer systems that are different but have similar mechanical operations. For example, there are many different hot end extruder designs available in the 3D printer market today, but most operate with similar mechanical and electronic systems. Even most of the new and popular delta printers use belts and pulleys.</p><p>There are too many different 3D printer designs for this book to cover any particular problem in specific detail. This book is a RepRap Cookbook. Its focus is on one of the most prolific designs called the <span class="strong"><strong>Prusa Mendel</strong></span><a id="id625" class="indexterm"/>. This was the type of printer that was used for printing the recipe exercises in this book.</p><p>The two basic variations can be seen in the following image:</p><div class="mediaobject"><img src="graphics/9888OS_09_01.jpg" alt="Introduction"/></div><p>On the left-hand side of the preceding image is a classic Prusa Mendel 3D printer designed by Josef Pr&#x16F;&#x161;a. It was constructed by sourced parts, and it follows the build instructions written by Gary Hodgson. The printer on the right-hand side of the preceding image is a RepRapPro Mendel. It was designed by Adrian Bowyer and his team, and it was purchased as a kit from his online store.</p><div class="note" title="Note" style=""><div class="inner"><h3 class="title"><a id="note26"/>Note</h3><p>A list of suppliers<a id="id626" class="indexterm"/> for the Prusa Mendel can be found at <a class="ulink" href="http://www.reprap.org/wiki/Prusa_Mendel_Buyers_Guide">http://www.reprap.org/wiki/Prusa_Mendel_Buyers_Guide</a>.</p><p>The Prusa Mendel build manual<a id="id627" class="indexterm"/> can be found at <a class="ulink" href="http://garyhodgson.com/reprap/prusa-mendel-visual-instructions/">http://garyhodgson.com/reprap/prusa-mendel-visual-instructions/</a>.</p><p>The RepRapPro Mendel kit<a id="id628" class="indexterm"/> is sold at <a class="ulink" href="https://reprappro.com/">https://reprappro.com/</a>.</p></div></div><p>This chapter's focus is not intended to be an overview of the basic problems arising from printing flaws due to poor slicer adjustments. Neither will this chapter focus on the printing flaws encountered by poor modeling. The previous chapters in this book offer most of the information needed for these types of problems. This chapter will focus on the common issues that tend to recur over and over again in regards to the mechanical functions of the printer.</p><div class="note" title="Note" style=""><div class="inner"><h3 class="title"><a id="note27"/>Note</h3><p>More troubleshooting information can be found at <a class="ulink" href="http://reprap.org/wiki/Print_Troubleshooting_Pictorial_Guide">http://reprap.org/wiki/Print_Troubleshooting_Pictorial_Guide</a>. It surveys not only the mechanical issues, but also the issues that can be corrected by firmware or slicer adjustments.</p></div></div></div></div></body></html>
