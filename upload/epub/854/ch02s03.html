<?xml version="1.0" encoding="utf-8" ?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">

<html xmlns="http://www.w3.org/1999/xhtml"><head><title>Plane alignment with Meshmixer</title><link rel="stylesheet" href="epub.css" type="text/css"/><meta name="generator" content="DocBook XSL Stylesheets V1.75.2"/></head><body id="page"><div class="section" title="Plane alignment with Meshmixer"><div class="titlepage"><div><div><h1 class="title"><a id="ch02lvl1sec24"/>Plane alignment with Meshmixer</h1></div></div></div><p>In a 3D software program, we have to be able to orient ourselves in this virtual space to be able to work in it. This is <a id="id90" class="indexterm"/>typically achieved by modeling on a plane in<a id="id91" class="indexterm"/> the workspace. Meshmixer generates a grid plane and a model is then positioned somewhere on or within it. We can see this illustrated in the following image:</p><div class="mediaobject"><img src="graphics/9888OS_02_03.jpg" alt="Plane alignment with Meshmixer"/></div><p>Meshmixer imports the rabbit sitting on the plane and imports the sphere with the plane bisecting it. Depending on how a model is positioned in the Cartesian space of another 3D program, the positioning, when importing into Meshmixer, can be anywhere in relation to this plane.</p><p>Meshmixer orients the <span class="emphasis"><em>y</em></span> axis going up from the surface of the plane. You'll find that the other programs in this book will not have this configuration. The <span class="emphasis"><em>z</em></span> axis will travel up from the plane. This isn't a problem when sculpting the model in Meshmixer, but it does make a difference when using a modification tool, which depends on the plane for orientation.</p><p>In this recipe, we'll learn<a id="id92" class="indexterm"/> how to make a manual change in this alignment.</p><div class="section" title="Getting ready"><div class="titlepage"><div><div><h2 class="title"><a id="ch02lvl2sec41"/>Getting ready</h2></div></div></div><p>You'll need <a id="id93" class="indexterm"/>one of the models from <a class="link" href="ch01.html" title="Chapter&#xA0;1.&#xA0;Getting Started with 3D Printing">Chapter 1</a>, <span class="emphasis"><em>Getting Started with 3D Printing</em></span>. In this recipe, the pyramid model will serve as the example.</p></div><div class="section" title="How to do it..."><div class="titlepage"><div><div><h2 class="title"><a id="ch02lvl2sec42"/>How to do it...</h2></div></div></div><p>We will proceed as follows:</p><div class="orderedlist"><ol class="orderedlist arabic"><li class="listitem">Select <span class="strong"><strong>Import+</strong></span> to import the model into Meshmixer. In the following image, we can see how the pyramid is positioned in relation to Meshmixer's grid plane:<div class="mediaobject"><img src="graphics/9888OS_02_04.jpg" alt="How to do it..."/></div></li><li class="listitem">Go to <span class="strong"><strong>Edit</strong></span>. From the pop-up window, choose <span class="strong"><strong>Align</strong></span>. The workspace will now show another plane with the model. From here, manual movements can be made by clicking, holding, and dragging the blue arrow and arc controls. For now, let's use the alignment controls in the <span class="strong"><strong>Properties</strong></span> window.<div class="mediaobject"><img src="graphics/9888OS_02_05.jpg" alt="How to do it..."/></div></li><li class="listitem">From the <span class="strong"><strong>Align</strong></span> properties window,<a id="id94" class="indexterm"/> you'll have<a id="id95" class="indexterm"/> a choice in alignment by choosing an axis. Choose the axis that will orient your model on the plane. In this case, it's the <span class="emphasis"><em>y</em></span> axis. We can see the alignment change in the following image:<div class="mediaobject"><img src="graphics/9888OS_02_06.jpg" alt="How to do it..."/></div></li><li class="listitem">When you're happy with the orientation, click on <span class="strong"><strong>Accept</strong></span> in the <span class="strong"><strong>Align</strong></span> properties window.</li></ol></div></div><div class="section" title="How it works..."><div class="titlepage"><div><div><h2 class="title"><a id="ch02lvl2sec43"/>How it works...</h2></div></div></div><p>Aligning the<a id="id96" class="indexterm"/> model in relation to Meshmixer's plane is helpful when working in Meshmixer, but before the model is exported as an STL for <a id="id97" class="indexterm"/>slicing and printing, we'll have to realign the model back to its former position. If we don't, then the model will be positioned on its side for printing.</p><div class="note" title="Note" style=""><div class="inner"><h3 class="title"><a id="tip07"/>Tip</h3><p>Meshmixer allows for an automatic flip of the <span class="emphasis"><em>z</em></span> and <span class="emphasis"><em>y</em></span> axis during import and export. This is found in the menu under <span class="strong"><strong>View</strong></span> and then <span class="strong"><strong>Config</strong></span>.</p></div></div></div><div class="section" title="There's more..."><div class="titlepage"><div><div><h2 class="title"><a id="ch02lvl2sec44"/>There's more...</h2></div></div></div><p>The red ball on the plane in Meshmixer is a marker that shows the origin of the coordinate system. This can be seen in the following image by comparing the top view of the pyramid in another 3D program:</p><div class="mediaobject"><img src="graphics/9888OS_02_07.jpg" alt="There's more..."/></div><p>The image on the left shows a view that is seen by looking down the <span class="emphasis"><em>z</em></span> axis onto the grid where the model is positioned. The origin is marked by the red circle on the grid that represents the <span class="emphasis"><em>xy</em></span> plane. The image on the right shows the Meshmixer grid with its origin marked by a small red ball. Knowing where the origin is located in the virtual space is important. It always provides a point of reference no matter which 3D program your model is imported.</p></div></div></body></html>
