<?xml version="1.0" encoding="utf-8" ?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">

<html xmlns="http://www.w3.org/1999/xhtml"><head><title>Changing mesh resolutions with 123D Catch</title><link rel="stylesheet" href="epub.css" type="text/css"/><meta name="generator" content="DocBook XSL Stylesheets V1.75.2"/></head><body id="page"><div class="section" title="Changing mesh resolutions with 123D Catch"><div class="titlepage"><div><div><h1 class="title"><a id="ch01lvl1sec15"/>Changing mesh resolutions with 123D Catch</h1></div></div></div><p>There are three <a id="id33" class="indexterm"/>different mesh resolutions<a id="id34" class="indexterm"/> available for our scene. The program's default setting produces a low-resolution draft. In this recipe, we will learn how to change the mesh resolutions of our scene for higher quality.</p><div class="section" title="Getting ready"><div class="titlepage"><div><div><h2 class="title"><a id="ch01lvl2sec20"/>Getting ready</h2></div></div></div><p>It is good practice to make a backup of your folder containing the Photo Scene data files and photos. Keep all the backups of the originals together in a folder. In the following recipe, multiple<a id="id35" class="indexterm"/> resolutions will be generated for each scene. Keeping all of your data files organized is very important!</p></div><div class="section" title="How to do it&#x2026;"><div class="titlepage"><div><div><h2 class="title"><a id="ch01lvl2sec21"/>How to do it&#x2026;</h2></div></div></div><p>Open 123D Catch and sign in. Open an existing capture and proceed as follows:</p><div class="orderedlist"><ol class="orderedlist arabic"><li class="listitem">
Click on the <span class="strong"><strong>Generate Mesh</strong></span> icon [<span class="inlinemediaobject"><img src="graphics/9888OS_01_11.jpg" alt="How to do it&#x2026;"/></span>] on the toolbar. A window opens, giving us three choices for the output quality of our mesh. Let's begin by choosing <span class="strong"><strong>Mobile</strong></span>.
</li><li class="listitem">A new window will open, informing us that the scene will now close. Click on <span class="strong"><strong>OK</strong></span>.</li><li class="listitem">A <span class="strong"><strong>Create New Capture</strong></span> window will open. Here, you can rename your model. It is a good idea to attach a tag to designate that it's a mobile mesh, as we'll be experimenting with all three mesh resolutions for all the four models. When you have renamed the model, choose the e-mail notification for processing it.</li><li class="listitem">Repeat the preceding procedure, and save the model as a <span class="strong"><strong>Standard</strong></span> and <span class="strong"><strong>Maximum</strong></span> resolution file. Keep in mind that Autodesk will close your window each time after you request a resolution update. You will have to reload your<a id="id36" class="indexterm"/> initial scene for each save.</li><li class="listitem">Repeat these steps for the remaining scenes.</li></ol></div></div><div class="section" title="How it works&#x2026;"><div class="titlepage"><div><div><h2 class="title"><a id="ch01lvl2sec22"/>How it works&#x2026;</h2></div></div></div><p>When Autodesk first processed your photos, it generated a very low-resolution draft of your scene. This draft is just a little lower in resolution than a <span class="strong"><strong>Mobile</strong></span> resolution export. The following chart compares the resolution of a typical scene saved in all three resolutions:</p><div class="mediaobject"><img src="graphics/9888OS_01_38.jpg" alt="How it works&#x2026;"/></div><p>We would typically assume that picking the highest resolution would be the best for 3D printing. However,<a id="id37" class="indexterm"/> by experimenting with<a id="id38" class="indexterm"/> the various resolutions with each model, you may find that a lower resolution is either better or more interesting than a higher-resolution export.</p></div></div></body></html>
