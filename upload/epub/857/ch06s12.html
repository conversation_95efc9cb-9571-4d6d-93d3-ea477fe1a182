<?xml version="1.0" encoding="utf-8" ?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">

<html xmlns="http://www.w3.org/1999/xhtml"><head><title>Support options with Slic3r</title><link rel="stylesheet" href="epub.css" type="text/css"/><meta name="generator" content="DocBook XSL Stylesheets V1.75.2"/></head><body id="page"><div class="section" title="Support options with Slic3r"><div class="titlepage"><div><div><h1 class="title"><a id="ch06lvl1sec88"/>Support options with Slic3r</h1></div></div></div><p>In this recipe, <a id="id469" class="indexterm"/>we're going to examine some<a id="id470" class="indexterm"/> support options in Slic3r that will adjust the physical contact of the support material to the model. This is important <a id="id471" class="indexterm"/>in order to tune a profile that will provide not only good support but also a support material that will easily be removed.</p><div class="section" title="Getting ready"><div class="titlepage"><div><div><h2 class="title"><a id="ch06lvl2sec212"/>Getting ready</h2></div></div></div><p>You'll need the jellyfish model.</p></div><div class="section" title="How to do it..."><div class="titlepage"><div><div><h2 class="title"><a id="ch06lvl2sec213"/>How to do it...</h2></div></div></div><p>We will proceed as follows:</p><div class="orderedlist"><ol class="orderedlist arabic"><li class="listitem">Under <span class="strong"><strong>Print Settings</strong></span>, select <span class="strong"><strong>Support material</strong></span>. Check the <span class="strong"><strong>Generate support material</strong></span> box.</li><li class="listitem">Change the default setting for <span class="strong"><strong>Pattern</strong></span> from <code class="literal">honeycomb</code> to <code class="literal">rectangular</code>.</li><li class="listitem">Now, choose <span class="strong"><strong>Pattern spacing</strong></span> and change the default value from <code class="literal">0</code> to <code class="literal">2.5</code>.</li><li class="listitem">We'll test the other support settings at the default values. Slice and save the G-code.</li><li class="listitem">Print the model.</li><li class="listitem">Now, choose <span class="strong"><strong>Pattern spacing</strong></span> and change the value from <code class="literal">2.5</code> to <code class="literal">5.0</code>. Slice and save the G-code.</li><li class="listitem">Print the model.</li></ol></div></div><div class="section" title="How it works..."><div class="titlepage"><div><div><h2 class="title"><a id="ch06lvl2sec214"/>How it works...</h2></div></div></div><p>With <span class="strong"><strong>Pattern spacing</strong></span>, we're making the same adjustment we made in Skeinforge with <span class="strong"><strong>Interface Density Infill (ratio)</strong></span>. We increased the space between support walls. This is clearly represented in the following image:</p><div class="mediaobject"><img src="graphics/9888OS_06_76.jpg" alt="How it works..."/></div><p>We changed the<a id="id472" class="indexterm"/> honeycomb pattern to rectangular because it's generally too difficult to remove. The rectangular pattern is similar to the pattern that Skeinforge generates (when cross hatch is deactivated).</p></div><div class="section" title="There's more..."><div class="titlepage"><div><div><h2 class="title"><a id="ch06lvl2sec215"/>There's more...</h2></div></div></div><p>Slic3r also <a id="id473" class="indexterm"/>has <a id="id474" class="indexterm"/>a method of weakening the bond between the support material and the model by creating an interface layer.</p><p>By changing the default value from <code class="literal">0</code> to <code class="literal">1</code> or more in interface layers, we can activate the function and instruct the slicer to add one or more interface layers. By increasing the interface pattern spacing, we can even make it weaker between the support material and the model by spreading the extrusion further apart.</p></div></div></body></html>
