<g:render template="/books/navheader_new"></g:render>
<script>
    $('link[data-role="baseline"]').attr('href', '');
</script>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<section class="page-main-wrapper mdl-js pb-5 edit-profile">
     <div class="container mt-3 mt-md-0 py-4 pt-md-5">
         <div class="profile-wrapper col-12 col-lg-10 d-flex justify-content-between align-items-center flex-wrap flex-lg-nowrap">
             <div class="profile d-flex mt-3 align-items-center flex-wrap flex-lg-nowrap">
                 <form class="update-user" enctype="multipart/form-data" role="form" name="uploadProfileImage" id="uploadProfileImage" action="/creation/uploadProfileImage" method="post">
                     <div class="image-wrapper">
                         <input type="hidden" name="type" value="user">
                         <input type="hidden" name="source" value="editprofile">
                         <input type="hidden" name="sourceController" value="usermanagement">

                             <%if(session['userdetails'].profilepic!=null){%>
                             <img src="/funlearn/showProfileImage?id=${session['userdetails'].id}&fileName=${session['userdetails'].profilepic}&type=user&imgType=passport" class="img-responsive align-self-start mr-3 rounded-circle edit-img">
                             <%}else if("24"=="${session['siteId']}") {%>
                             <img src="${assetPath(src: 'ebouquet/user-icon.png')}" class="img-responsive align-self-start mr-3 rounded-circle edit-img" alt="">
                             <%}else {%> <img src="${assetPath(src: 'wonderslate/user-icon.png')}" class="img-responsive align-self-start mr-3 rounded-circle edit-img" alt=""><%}%>
                             <input id="fileoption1" class="d-none" name="file" type="file"  accept="image/png, image/jpeg, image/gif ,image/svg" onchange="updateProfile();"/>
                             <a href="javascript:updateImage();" class="rounded-circle edit-btn" style="bottom:75px;"><i class="material-icons">edit</i></a>
%{--                             <p id="file-error" style="font-size: 12px;--}%
%{--                             margin-top: 0.5rem;--}%
%{--                             text-align: center;">Please Upload Image below 2mb</p>--}%
                         </div>
                      <div class="mt-2 ml-4">
                         <h4 class="user-name">
                             <span id="firstname">${name}</span>
%{--                             <span id="secondname" class="ml-2">P</span>--}%
                         </h4>
                         <p class="mt-2 text-left">${mobile}</p>
                      </div>
             </div>
                 </form>
                 <div class="mt-4 mt-lg-0 margin-reset-auto">
                 <button class="btn btn-makepoint d-lg-flex" data-toggle="modal" data-target="#morePoints">How can I make more points? <i class="material-icons">info</i> </button>
             </div>
         </div>
    <form class="mt-4" id="edit-user-form" action="updateUserProfile" >
        <div class="row col-12 col-lg-10 justify-content-center m-auto">
        <div class="col-12 col-lg-6">
                <div class="form-group">
                    <label for="who">Who are you?</label>
                    <select class="form-control" id="who" name="who">
                        <option value="student" selected>Student</option>
                        <option value="parent" >Parent</option>
                        <option value="teacher" >Teacher</option>
                    </select>
                </div>
            <div class="form-group">
                <label for="name">Your Name</label>
                <input type="text" class="form-control" id="name" value="${name}" placeholder="Enter password" name="name">
            </div>
            <div class="form-group">
                    <label for="pwd">Your Password</label>
                    <input type="password" class="form-control" id="pwd" value="${password}" placeholder="Enter password" name="pswd">
                </div>
                <div class="form-group">
                    <label for="edit-email">Your Email</label>
                    <g:if test="${userNameType == 'email'}">
                        <input type="email" class="form-control" value="${email}" id="edit-email" placeholder="" name="email" disabled>
                    </g:if>
                    <g:else>
                        <input type="email" class="form-control" value="${email}" id="edit-email" placeholder="" name="email" >
                    </g:else>
                </div>
            <div class="form-group">
                <label for="mobile">Your Mobile Number</label>

                <g:if test="${userNameType == 'mobile'}">
                    <input type="text" class="form-control" id="mobile" value="${mobile}" placeholder="Enter Your Mobile Number" name="mobile" disabled>
                </g:if>
                <g:else>
                    <input type="text" class="form-control" id="mobile" value="${mobile}" placeholder="Enter Your Mobile Number" name="mobile"
                           pattern="^\(?([0-9]{3})\)?[-. ]?([0-9]{3})[-. ]?([0-9]{4})$" oninput="numberOnly(this.id);" minlength="10" maxlength="10">
                </g:else>
            </div>
            <div class="form-group">
                <label class="form-check-label">
                    Your phone number, if changed, <span>will be verified via OTP</span>.
                </label>
            </div>
            </div>
            <div class="col-12 col-lg-6">
                <div class="form-group">
                    <label for="selectCountry">Country</label>
                    <select class="form-control" id="selectCountry" name="selectCountry">
                        <option value="" selected="selected">Select Country</option>
                    </select>
                </div>
                <div id="divState" class="form-group">
                    <label for="stateSelect">Select Your State</label>
                    <select class="form-control" id="stateSelect" name="stateSelect">
                        <option value="" selected="selected">Select State</option>
                    </select>
                </div>
                <div id="divDistrict" class="form-group">
                    <label for="districtSelect">Select Your City</label>
                    <select class="form-control" id="districtSelect" name="districtSelect">
                        <option value="" selected="selected">Select City</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="pincode">Pincode</label>
                    <input type="text" class="form-control" id="pincode" value="${pincode}" placeholder="Enter Pincode" name="pincode"
                           pattern="^\(?([0-9]{3})\)?[-. ]?([0-9]{3})[-. ]?([0-9]{4})$" oninput="numberOnly(this.id);">
                </div>
            </div>
            <div class="col-12">
                <div class="form-group">
                    <label for="schoolname">School/College Name</label>
                    <input type="text" class="form-control" id="schoolname" value="${schoolOrCollege}" placeholder="Enter School/College" name="schoolname">
                </div>
             <div class="text-center">
                <input type="button" onclick="generateOtp()" class="btn btn-submit mt-4" value="Save & Submit">
             </div>
            </div>
        </div>
</form>
     </div>

    <div class="modal modal-modifier fade" id="otp-modal" data-backdrop="static">
        <div class="modal-dialog modal-dialog-modifier modal-dialog-centered">
            <div class="modal-content modal-content-modifier">
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">x</span>
                </button>

                <!-- Modal body -->
                <div class="modal-body modal-body-modifier text-left">
                    <h6 class="mt-3 mb-4" id="OtpTextMessage"></h6>
                    <div class="form-group form-group-modifier">
                        <input type="text" id="otp" class="form-control form-control-modifier border-bottom" placeholder="Enter OTP">
                    </div>
                    <p id="errorOTP" class="form-text form-text-modifier text-danger hidden"></p>

                    <div class="d-flex justify-content-center py-3 mt-3">
                        <button type="button" class="btn btn-lg btn-outline-secondary btn-outline-secondary-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect col-5 col-md-4 mr-2" data-dismiss="modal" aria-label="Close">Cancel</button>
                        <button type="button" class="btn btn-lg btn-success btn-success-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect border-0 col-5 col-md-4 ml-2" onclick="javascript:checkOtp();">Save & Submit</button>
                    </div>
                </div>

            </div>
        </div>
    </div>

    <div class="modal" id="morePoints">
        <div class="modal-dialog">
            <div class="modal-content">

                <!-- Modal Header -->
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                </div>

                <!-- Modal body -->
                <div class="modal-body d-flex align-items-center justify-content-center">
                <div class="carousel-item active tutor">
                        <h2>Earn more Points!</h2>
                    <p class="mt-4">You'll earn more points by<br>
                    completing your profile.</p>
                    <p class="mt-2">Every detail you fill,will earn you <strong>+5 Points.</strong></p>

                    <p class="mt-4"><strong>More the points</strong> higher your rank will be!</p>
                    <div class="d-flex align-items-center justify-content-center mt-4">
                        <p id="grade-rating"><i class="material-icons">grade</i></p>
                        <span class="wonderwonk">Wonder Wonk Star (1000 points)</span>
                    </div>
                    <div class="d-flex align-items-center justify-content-center mt-2">
                        <p id="grade-rating"><i class="material-icons">grade</i><i class="material-icons">grade</i></p>
                        <span class="wonderwonk">Wonder Wonk Supreme (3000 points)</span>
                    </div>
                    <div class="d-flex align-items-center justify-content-center mt-2">
                        <p id="grade-rating"><i class="material-icons">grade</i><i class="material-icons">grade</i> <i class="material-icons">grade</i></p>
                        <span class="wonderwonk">Wonder Wonk Ultimate (5000 points)</span>
                    </div>



                </div>
                </div>

                <!-- Modal footer -->
                <div class="modal-footer">

                </div>

            </div>
        </div>
    </div>
</section>

<g:render template="/books/footer_new"></g:render>

<script>
    var country = "${country}", who = "${who}";
    if(who == "") who = "student";
    $("#who").val(who);
    var countryList = [
        {name: 'Afghanistan', code: 'AF'},
        {name: 'Åland Islands', code: 'AX'},
        {name: 'Albania', code: 'AL'},
        {name: 'Algeria', code: 'DZ'},
        {name: 'American Samoa', code: 'AS'},
        {name: 'AndorrA', code: 'AD'},
        {name: 'Angola', code: 'AO'},
        {name: 'Anguilla', code: 'AI'},
        {name: 'Antarctica', code: 'AQ'},
        {name: 'Antigua and Barbuda', code: 'AG'},
        {name: 'Argentina', code: 'AR'},
        {name: 'Armenia', code: 'AM'},
        {name: 'Aruba', code: 'AW'},
        {name: 'Australia', code: 'AU'},
        {name: 'Austria', code: 'AT'},
        {name: 'Azerbaijan', code: 'AZ'},
        {name: 'Bahamas', code: 'BS'},
        {name: 'Bahrain', code: 'BH'},
        {name: 'Bangladesh', code: 'BD'},
        {name: 'Barbados', code: 'BB'},
        {name: 'Belarus', code: 'BY'},
        {name: 'Belgium', code: 'BE'},
        {name: 'Belize', code: 'BZ'},
        {name: 'Benin', code: 'BJ'},
        {name: 'Bermuda', code: 'BM'},
        {name: 'Bhutan', code: 'BT'},
        {name: 'Bolivia', code: 'BO'},
        {name: 'Bosnia and Herzegovina', code: 'BA'},
        {name: 'Botswana', code: 'BW'},
        {name: 'Bouvet Island', code: 'BV'},
        {name: 'Brazil', code: 'BR'},
        {name: 'British Indian Ocean Territory', code: 'IO'},
        {name: 'Brunei Darussalam', code: 'BN'},
        {name: 'Bulgaria', code: 'BG'},
        {name: 'Burkina Faso', code: 'BF'},
        {name: 'Burundi', code: 'BI'},
        {name: 'Cambodia', code: 'KH'},
        {name: 'Cameroon', code: 'CM'},
        {name: 'Canada', code: 'CA'},
        {name: 'Cape Verde', code: 'CV'},
        {name: 'Cayman Islands', code: 'KY'},
        {name: 'Central African Republic', code: 'CF'},
        {name: 'Chad', code: 'TD'},
        {name: 'Chile', code: 'CL'},
        {name: 'China', code: 'CN'},
        {name: 'Christmas Island', code: 'CX'},
        {name: 'Cocos (Keeling) Islands', code: 'CC'},
        {name: 'Colombia', code: 'CO'},
        {name: 'Comoros', code: 'KM'},
        {name: 'Congo', code: 'CG'},
        {name: 'Congo, The Democratic Republic of the', code: 'CD'},
        {name: 'Cook Islands', code: 'CK'},
        {name: 'Costa Rica', code: 'CR'},
        {name: 'Cote D\'Ivoire', code: 'CI'},
        {name: 'Croatia', code: 'HR'},
        {name: 'Cuba', code: 'CU'},
        {name: 'Cyprus', code: 'CY'},
        {name: 'Czech Republic', code: 'CZ'},
        {name: 'Denmark', code: 'DK'},
        {name: 'Djibouti', code: 'DJ'},
        {name: 'Dominica', code: 'DM'},
        {name: 'Dominican Republic', code: 'DO'},
        {name: 'Ecuador', code: 'EC'},
        {name: 'Egypt', code: 'EG'},
        {name: 'El Salvador', code: 'SV'},
        {name: 'Equatorial Guinea', code: 'GQ'},
        {name: 'Eritrea', code: 'ER'},
        {name: 'Estonia', code: 'EE'},
        {name: 'Ethiopia', code: 'ET'},
        {name: 'Falkland Islands (Malvinas)', code: 'FK'},
        {name: 'Faroe Islands', code: 'FO'},
        {name: 'Fiji', code: 'FJ'},
        {name: 'Finland', code: 'FI'},
        {name: 'France', code: 'FR'},
        {name: 'French Guiana', code: 'GF'},
        {name: 'French Polynesia', code: 'PF'},
        {name: 'French Southern Territories', code: 'TF'},
        {name: 'Gabon', code: 'GA'},
        {name: 'Gambia', code: 'GM'},
        {name: 'Georgia', code: 'GE'},
        {name: 'Germany', code: 'DE'},
        {name: 'Ghana', code: 'GH'},
        {name: 'Gibraltar', code: 'GI'},
        {name: 'Greece', code: 'GR'},
        {name: 'Greenland', code: 'GL'},
        {name: 'Grenada', code: 'GD'},
        {name: 'Guadeloupe', code: 'GP'},
        {name: 'Guam', code: 'GU'},
        {name: 'Guatemala', code: 'GT'},
        {name: 'Guernsey', code: 'GG'},
        {name: 'Guinea', code: 'GN'},
        {name: 'Guinea-Bissau', code: 'GW'},
        {name: 'Guyana', code: 'GY'},
        {name: 'Haiti', code: 'HT'},
        {name: 'Heard Island and Mcdonald Islands', code: 'HM'},
        {name: 'Holy See (Vatican City State)', code: 'VA'},
        {name: 'Honduras', code: 'HN'},
        {name: 'Hong Kong', code: 'HK'},
        {name: 'Hungary', code: 'HU'},
        {name: 'Iceland', code: 'IS'},
        {name: 'India', code: 'IN'},
        {name: 'Indonesia', code: 'ID'},
        {name: 'Iran, Islamic Republic Of', code: 'IR'},
        {name: 'Iraq', code: 'IQ'},
        {name: 'Ireland', code: 'IE'},
        {name: 'Isle of Man', code: 'IM'},
        {name: 'Israel', code: 'IL'},
        {name: 'Italy', code: 'IT'},
        {name: 'Jamaica', code: 'JM'},
        {name: 'Japan', code: 'JP'},
        {name: 'Jersey', code: 'JE'},
        {name: 'Jordan', code: 'JO'},
        {name: 'Kazakhstan', code: 'KZ'},
        {name: 'Kenya', code: 'KE'},
        {name: 'Kiribati', code: 'KI'},
        {name: 'Korea, Democratic People\'S Republic of', code: 'KP'},
        {name: 'Korea, Republic of', code: 'KR'},
        {name: 'Kuwait', code: 'KW'},
        {name: 'Kyrgyzstan', code: 'KG'},
        {name: 'Lao People\'S Democratic Republic', code: 'LA'},
        {name: 'Latvia', code: 'LV'},
        {name: 'Lebanon', code: 'LB'},
        {name: 'Lesotho', code: 'LS'},
        {name: 'Liberia', code: 'LR'},
        {name: 'Libyan Arab Jamahiriya', code: 'LY'},
        {name: 'Liechtenstein', code: 'LI'},
        {name: 'Lithuania', code: 'LT'},
        {name: 'Luxembourg', code: 'LU'},
        {name: 'Macao', code: 'MO'},
        {name: 'Macedonia, The Former Yugoslav Republic of', code: 'MK'},
        {name: 'Madagascar', code: 'MG'},
        {name: 'Malawi', code: 'MW'},
        {name: 'Malaysia', code: 'MY'},
        {name: 'Maldives', code: 'MV'},
        {name: 'Mali', code: 'ML'},
        {name: 'Malta', code: 'MT'},
        {name: 'Marshall Islands', code: 'MH'},
        {name: 'Martinique', code: 'MQ'},
        {name: 'Mauritania', code: 'MR'},
        {name: 'Mauritius', code: 'MU'},
        {name: 'Mayotte', code: 'YT'},
        {name: 'Mexico', code: 'MX'},
        {name: 'Micronesia, Federated States of', code: 'FM'},
        {name: 'Moldova, Republic of', code: 'MD'},
        {name: 'Monaco', code: 'MC'},
        {name: 'Mongolia', code: 'MN'},
        {name: 'Montserrat', code: 'MS'},
        {name: 'Morocco', code: 'MA'},
        {name: 'Mozambique', code: 'MZ'},
        {name: 'Myanmar', code: 'MM'},
        {name: 'Namibia', code: 'NA'},
        {name: 'Nauru', code: 'NR'},
        {name: 'Nepal', code: 'NP'},
        {name: 'Netherlands', code: 'NL'},
        {name: 'Netherlands Antilles', code: 'AN'},
        {name: 'New Caledonia', code: 'NC'},
        {name: 'New Zealand', code: 'NZ'},
        {name: 'Nicaragua', code: 'NI'},
        {name: 'Niger', code: 'NE'},
        {name: 'Nigeria', code: 'NG'},
        {name: 'Niue', code: 'NU'},
        {name: 'Norfolk Island', code: 'NF'},
        {name: 'Northern Mariana Islands', code: 'MP'},
        {name: 'Norway', code: 'NO'},
        {name: 'Oman', code: 'OM'},
        {name: 'Pakistan', code: 'PK'},
        {name: 'Palau', code: 'PW'},
        {name: 'Palestinian Territory, Occupied', code: 'PS'},
        {name: 'Panama', code: 'PA'},
        {name: 'Papua New Guinea', code: 'PG'},
        {name: 'Paraguay', code: 'PY'},
        {name: 'Peru', code: 'PE'},
        {name: 'Philippines', code: 'PH'},
        {name: 'Pitcairn', code: 'PN'},
        {name: 'Poland', code: 'PL'},
        {name: 'Portugal', code: 'PT'},
        {name: 'Puerto Rico', code: 'PR'},
        {name: 'Qatar', code: 'QA'},
        {name: 'Reunion', code: 'RE'},
        {name: 'Romania', code: 'RO'},
        {name: 'Russian Federation', code: 'RU'},
        {name: 'RWANDA', code: 'RW'},
        {name: 'Saint Helena', code: 'SH'},
        {name: 'Saint Kitts and Nevis', code: 'KN'},
        {name: 'Saint Lucia', code: 'LC'},
        {name: 'Saint Pierre and Miquelon', code: 'PM'},
        {name: 'Saint Vincent and the Grenadines', code: 'VC'},
        {name: 'Samoa', code: 'WS'},
        {name: 'San Marino', code: 'SM'},
        {name: 'Sao Tome and Principe', code: 'ST'},
        {name: 'Saudi Arabia', code: 'SA'},
        {name: 'Senegal', code: 'SN'},
        {name: 'Serbia and Montenegro', code: 'CS'},
        {name: 'Seychelles', code: 'SC'},
        {name: 'Sierra Leone', code: 'SL'},
        {name: 'Singapore', code: 'SG'},
        {name: 'Slovakia', code: 'SK'},
        {name: 'Slovenia', code: 'SI'},
        {name: 'Solomon Islands', code: 'SB'},
        {name: 'Somalia', code: 'SO'},
        {name: 'South Africa', code: 'ZA'},
        {name: 'South Georgia and the South Sandwich Islands', code: 'GS'},
        {name: 'Spain', code: 'ES'},
        {name: 'Sri Lanka', code: 'LK'},
        {name: 'Sudan', code: 'SD'},
        {name: 'Suriname', code: 'SR'},
        {name: 'Svalbard and Jan Mayen', code: 'SJ'},
        {name: 'Swaziland', code: 'SZ'},
        {name: 'Sweden', code: 'SE'},
        {name: 'Switzerland', code: 'CH'},
        {name: 'Syrian Arab Republic', code: 'SY'},
        {name: 'Taiwan, Province of China', code: 'TW'},
        {name: 'Tajikistan', code: 'TJ'},
        {name: 'Tanzania, United Republic of', code: 'TZ'},
        {name: 'Thailand', code: 'TH'},
        {name: 'Timor-Leste', code: 'TL'},
        {name: 'Togo', code: 'TG'},
        {name: 'Tokelau', code: 'TK'},
        {name: 'Tonga', code: 'TO'},
        {name: 'Trinidad and Tobago', code: 'TT'},
        {name: 'Tunisia', code: 'TN'},
        {name: 'Turkey', code: 'TR'},
        {name: 'Turkmenistan', code: 'TM'},
        {name: 'Turks and Caicos Islands', code: 'TC'},
        {name: 'Tuvalu', code: 'TV'},
        {name: 'Uganda', code: 'UG'},
        {name: 'Ukraine', code: 'UA'},
        {name: 'United Arab Emirates', code: 'AE'},
        {name: 'United Kingdom', code: 'GB'},
        {name: 'United States', code: 'US'},
        {name: 'United States Minor Outlying Islands', code: 'UM'},
        {name: 'Uruguay', code: 'UY'},
        {name: 'Uzbekistan', code: 'UZ'},
        {name: 'Vanuatu', code: 'VU'},
        {name: 'Venezuela', code: 'VE'},
        {name: 'Viet Nam', code: 'VN'},
        {name: 'Virgin Islands, British', code: 'VG'},
        {name: 'Virgin Islands, U.S.', code: 'VI'},
        {name: 'Wallis and Futuna', code: 'WF'},
        {name: 'Western Sahara', code: 'EH'},
        {name: 'Yemen', code: 'YE'},
        {name: 'Zambia', code: 'ZM'},
        {name: 'Zimbabwe', code: 'ZW'}
    ];
    var stateObject = {
        "Andhra Pradesh": [
            "Anantapur",
            "Chittoor",
            "East Godavari",
            "Guntur",
            "Krishna",
            "Kurnool",
            "Nellore",
            "Prakasam",
            "Srikakulam",
            "Visakhapatnam",
            "Vizianagaram",
            "West Godavari",
            "YSR Kadapa"
        ],
        "Andaman and Nicobar Island(UT)":[
            "South Andaman",
            "North and Middle Andaman",
            "Nicobar",
        ],
        "Arunachal Pradesh": [
            "Tawang",
            "West Kameng",
            "East Kameng",
            "Papum Pare",
            "Kurung Kumey",
            "Kra Daadi",
            "Lower Subansiri",
            "Upper Subansiri",
            "West Siang",
            "East Siang",
            "Siang",
            "Upper Siang",
            "Lower Siang",
            "Lower Dibang Valley",
            "Dibang Valley",
            "Anjaw",
            "Lohit",
            "Namsai",
            "Changlang",
            "Tirap",
            "Longding"
        ],
        "Assam": [
            "Baksa",
            "Barpeta",
            "Biswanath",
            "Bongaigaon",
            "Cachar",
            "Charaideo",
            "Chirang",
            "Darrang",
            "Dhemaji",
            "Dhubri",
            "Dibrugarh",
            "Goalpara",
            "Golaghat",
            "Hailakandi",
            "Hojai",
            "Jorhat",
            "Kamrup Metropolitan",
            "Kamrup",
            "Karbi Anglong",
            "Karimganj",
            "Kokrajhar",
            "Lakhimpur",
            "Majuli",
            "Morigaon",
            "Nagaon",
            "Nalbari",
            "Dima Hasao",
            "Sivasagar",
            "Sonitpur",
            "South Salmara-Mankachar",
            "Tinsukia",
            "Udalguri",
            "West Karbi Anglong"
        ],
        "Bihar": [
            "Araria",
            "Arwal",
            "Aurangabad",
            "Banka",
            "Begusarai",
            "Bhagalpur",
            "Bhojpur",
            "Buxar",
            "Darbhanga",
            "East Champaran (Motihari)",
            "Gaya",
            "Gopalganj",
            "Jamui",
            "Jehanabad",
            "Kaimur (Bhabua)",
            "Katihar",
            "Khagaria",
            "Kishanganj",
            "Lakhisarai",
            "Madhepura",
            "Madhubani",
            "Munger (Monghyr)",
            "Muzaffarpur",
            "Nalanda",
            "Nawada",
            "Patna",
            "Purnia (Purnea)",
            "Rohtas",
            "Saharsa",
            "Samastipur",
            "Saran",
            "Sheikhpura",
            "Sheohar",
            "Sitamarhi",
            "Siwan",
            "Supaul",
            "Vaishali",
            "West Champaran"
        ],
        "Chandigarh (UT)": [
            "Chandigarh"
        ],
        "Chhattisgarh": [
            "Balod",
            "Baloda Bazar",
            "Balrampur",
            "Bastar",
            "Bemetara",
            "Bijapur",
            "Bilaspur",
            "Dantewada (South Bastar)",
            "Dhamtari",
            "Durg",
            "Gariyaband",
            "Janjgir-Champa",
            "Jashpur",
            "Kabirdham (Kawardha)",
            "Kanker (North Bastar)",
            "Kondagaon",
            "Korba",
            "Korea (Koriya)",
            "Mahasamund",
            "Mungeli",
            "Narayanpur",
            "Raigarh",
            "Raipur",
            "Rajnandgaon",
            "Sukma",
            "Surajpur  ",
            "Surguja"
        ],
        "Dadra and Nagar Haveli (UT)": [
            "Dadra & Nagar Haveli"
        ],
        "Daman and Diu (UT)": [
            "Daman",
            "Diu"
        ],
        "Delhi (NCT)": [
            "Central Delhi",
            "East Delhi",
            "New Delhi",
            "North Delhi",
            "North East  Delhi",
            "North West  Delhi",
            "Shahdara",
            "South Delhi",
            "South East Delhi",
            "South West  Delhi",
            "West Delhi"
        ],
        "Goa": [
            "North Goa",
            "South Goa"
        ],
        "Gujarat": [
            "Ahmedabad",
            "Amreli",
            "Anand",
            "Aravalli",
            "Banaskantha (Palanpur)",
            "Bharuch",
            "Bhavnagar",
            "Botad",
            "Chhota Udepur",
            "Dahod",
            "Dangs (Ahwa)",
            "Devbhoomi Dwarka",
            "Gandhinagar",
            "Gir Somnath",
            "Jamnagar",
            "Junagadh",
            "Kachchh",
            "Kheda (Nadiad)",
            "Mahisagar",
            "Mehsana",
            "Morbi",
            "Narmada (Rajpipla)",
            "Navsari",
            "Panchmahal (Godhra)",
            "Patan",
            "Porbandar",
            "Rajkot",
            "Sabarkantha (Himmatnagar)",
            "Surat",
            "Surendranagar",
            "Tapi (Vyara)",
            "Vadodara",
            "Valsad"
        ],
        "Haryana": [
            "Ambala",
            "Bhiwani",
            "Charkhi Dadri",
            "Faridabad",
            "Fatehabad",
            "Gurgaon",
            "Hisar",
            "Jhajjar",
            "Jind",
            "Kaithal",
            "Karnal",
            "Kurukshetra",
            "Mahendragarh",
            "Mewat",
            "Palwal",
            "Panchkula",
            "Panipat",
            "Rewari",
            "Rohtak",
            "Sirsa",
            "Sonipat",
            "Yamunanagar"
        ],
        "Himachal Pradesh": [
            "Bilaspur",
            "Chamba",
            "Hamirpur",
            "Kangra",
            "Kinnaur",
            "Kullu",
            "Lahaul &amp; Spiti",
            "Mandi",
            "Shimla",
            "Sirmaur (Sirmour)",
            "Solan",
            "Una"
        ],
        "Jammu and Kashmir": [
            "Anantnag",
            "Bandipore",
            "Baramulla",
            "Budgam",
            "Doda",
            "Ganderbal",
            "Jammu",
            "Kathua",
            "Kishtwar",
            "Kulgam",
            "Kupwara",
            "Poonch",
            "Pulwama",
            "Rajouri",
            "Ramban",
            "Reasi",
            "Samba",
            "Shopian",
            "Srinagar",
            "Udhampur"
        ],
        "Jharkhand": [
            "Bokaro",
            "Chatra",
            "Deoghar",
            "Dhanbad",
            "Dumka",
            "East Singhbhum",
            "Garhwa",
            "Giridih",
            "Godda",
            "Gumla",
            "Hazaribag",
            "Jamtara",
            "Khunti",
            "Koderma",
            "Latehar",
            "Lohardaga",
            "Pakur",
            "Palamu",
            "Ramgarh",
            "Ranchi",
            "Sahibganj",
            "Seraikela-Kharsawan",
            "Simdega",
            "West Singhbhum"
        ],
        "Karnataka": [
            "Bagalkot",
            "Ballari (Bellary)",
            "Belagavi (Belgaum)",
            "Bengaluru (Bangalore) Rural",
            "Bengaluru (Bangalore) Urban",
            "Bidar",
            "Chamarajanagar",
            "Chikballapur",
            "Chikkamagaluru (Chikmagalur)",
            "Chitradurga",
            "Dakshina Kannada",
            "Davangere",
            "Dharwad",
            "Gadag",
            "Hassan",
            "Haveri",
            "Kalaburagi (Gulbarga)",
            "Kodagu",
            "Kolar",
            "Koppal",
            "Mandya",
            "Mysuru (Mysore)",
            "Raichur",
            "Ramanagara",
            "Shivamogga (Shimoga)",
            "Tumakuru (Tumkur)",
            "Udupi",
            "Uttara Kannada (Karwar)",
            "Vijayapura (Bijapur)",
            "Yadgir"
        ],
        "Kerala": [
            "Alappuzha",
            "Ernakulam",
            "Idukki",
            "Kannur",
            "Kasaragod",
            "Kollam",
            "Kottayam",
            "Kozhikode",
            "Malappuram",
            "Palakkad",
            "Pathanamthitta",
            "Thiruvananthapuram",
            "Thrissur",
            "Wayanad"
        ],
        "Ladakh (UT)":[
            "Leh",
            "Kargil"
        ],
        "Lakshadweep (UT)": [
            "Agatti",
            "Amini",
            "Androth",
            "Bithra",
            "Chethlath",
            "Kavaratti",
            "Kadmath",
            "Kalpeni",
            "Kilthan",
            "Minicoy"
        ],
        "Madhya Pradesh": [
            "Agar Malwa",
            "Alirajpur",
            "Anuppur",
            "Ashoknagar",
            "Balaghat",
            "Barwani",
            "Betul",
            "Bhind",
            "Bhopal",
            "Burhanpur",
            "Chhatarpur",
            "Chhindwara",
            "Damoh",
            "Datia",
            "Dewas",
            "Dhar",
            "Dindori",
            "Guna",
            "Gwalior",
            "Harda",
            "Hoshangabad",
            "Indore",
            "Jabalpur",
            "Jhabua",
            "Katni",
            "Khandwa",
            "Khargone",
            "Mandla",
            "Mandsaur",
            "Morena",
            "Narsinghpur",
            "Neemuch",
            "Panna",
            "Raisen",
            "Rajgarh",
            "Ratlam",
            "Rewa",
            "Sagar",
            "Satna",
            "Sehore",
            "Seoni",
            "Shahdol",
            "Shajapur",
            "Sheopur",
            "Shivpuri",
            "Sidhi",
            "Singrauli",
            "Tikamgarh",
            "Ujjain",
            "Umaria",
            "Vidisha"
        ],
        "Maharashtra": [
            "Ahmednagar",
            "Akola",
            "Amravati",
            "Aurangabad",
            "Beed",
            "Bhandara",
            "Buldhana",
            "Chandrapur",
            "Dhule",
            "Gadchiroli",
            "Gondia",
            "Hingoli",
            "Jalgaon",
            "Jalna",
            "Kolhapur",
            "Latur",
            "Mumbai City",
            "Mumbai Suburban",
            "Nagpur",
            "Nanded",
            "Nandurbar",
            "Nashik",
            "Osmanabad",
            "Palghar",
            "Parbhani",
            "Pune",
            "Raigad",
            "Ratnagiri",
            "Sangli",
            "Satara",
            "Sindhudurg",
            "Solapur",
            "Thane",
            "Wardha",
            "Washim",
            "Yavatmal"
        ],
        "Manipur": [
            "Bishnupur",
            "Chandel",
            "Churachandpur",
            "Imphal East",
            "Imphal West",
            "Jiribam",
            "Kakching",
            "Kamjong",
            "Kangpokpi",
            "Noney",
            "Pherzawl",
            "Senapati",
            "Tamenglong",
            "Tengnoupal",
            "Thoubal",
            "Ukhrul"
        ],
        "Meghalaya": [
            "East Garo Hills",
            "East Jaintia Hills",
            "East Khasi Hills",
            "North Garo Hills",
            "Ri Bhoi",
            "South Garo Hills",
            "South West Garo Hills ",
            "South West Khasi Hills",
            "West Garo Hills",
            "West Jaintia Hills",
            "West Khasi Hills"
        ],
        "Mizoram": [
            "Aizawl",
            "Champhai",
            "Kolasib",
            "Lawngtlai",
            "Lunglei",
            "Mamit",
            "Saiha",
            "Serchhip"
        ],
        "Nagaland": [
            "Dimapur",
            "Kiphire",
            "Kohima",
            "Longleng",
            "Mokokchung",
            "Mon",
            "Peren",
            "Phek",
            "Tuensang",
            "Wokha",
            "Zunheboto"
        ],
        "Odisha": [
            "Angul",
            "Balangir",
            "Balasore",
            "Bargarh",
            "Bhadrak",
            "Boudh",
            "Cuttack",
            "Deogarh",
            "Dhenkanal",
            "Gajapati",
            "Ganjam",
            "Jagatsinghapur",
            "Jajpur",
            "Jharsuguda",
            "Kalahandi",
            "Kandhamal",
            "Kendrapara",
            "Kendujhar (Keonjhar)",
            "Khordha",
            "Koraput",
            "Malkangiri",
            "Mayurbhanj",
            "Nabarangpur",
            "Nayagarh",
            "Nuapada",
            "Puri",
            "Rayagada",
            "Sambalpur",
            "Sonepur",
            "Sundargarh"
        ],
        "Puducherry (UT)": [
            "Karaikal",
            "Mahe",
            "Pondicherry",
            "Yanam"
        ],
        "Punjab": [
            "Amritsar",
            "Barnala",
            "Bathinda",
            "Faridkot",
            "Fatehgarh Sahib",
            "Fazilka",
            "Ferozepur",
            "Gurdaspur",
            "Hoshiarpur",
            "Jalandhar",
            "Kapurthala",
            "Ludhiana",
            "Mansa",
            "Moga",
            "Muktsar",
            "Nawanshahr (Shahid Bhagat Singh Nagar)",
            "Pathankot",
            "Patiala",
            "Rupnagar",
            "Sahibzada Ajit Singh Nagar (Mohali)",
            "Sangrur",
            "Tarn Taran"
        ],
        "Rajasthan": [
            "Ajmer",
            "Alwar",
            "Banswara",
            "Baran",
            "Barmer",
            "Bharatpur",
            "Bhilwara",
            "Bikaner",
            "Bundi",
            "Chittorgarh",
            "Churu",
            "Dausa",
            "Dholpur",
            "Dungarpur",
            "Hanumangarh",
            "Jaipur",
            "Jaisalmer",
            "Jalore",
            "Jhalawar",
            "Jhunjhunu",
            "Jodhpur",
            "Karauli",
            "Kota",
            "Nagaur",
            "Pali",
            "Pratapgarh",
            "Rajsamand",
            "Sawai Madhopur",
            "Sikar",
            "Sirohi",
            "Sri Ganganagar",
            "Tonk",
            "Udaipur"
        ],
        "Sikkim": [
            "East Sikkim",
            "North Sikkim",
            "South Sikkim",
            "West Sikkim"
        ],
        "Tamil Nadu": [
            "Ariyalur",
            "Chennai",
            "Coimbatore",
            "Cuddalore",
            "Dharmapuri",
            "Dindigul",
            "Erode",
            "Kanchipuram",
            "Kanyakumari",
            "Karur",
            "Krishnagiri",
            "Madurai",
            "Nagapattinam",
            "Namakkal",
            "Nilgiris",
            "Perambalur",
            "Pudukkottai",
            "Ramanathapuram",
            "Salem",
            "Sivaganga",
            "Thanjavur",
            "Theni",
            "Thoothukudi (Tuticorin)",
            "Tiruchirappalli",
            "Tirunelveli",
            "Tiruppur",
            "Tiruvallur",
            "Tiruvannamalai",
            "Tiruvarur",
            "Vellore",
            "Viluppuram",
            "Virudhunagar"
        ],
        "Telangana": [
            "Adilabad",
            "Bhadradri Kothagudem",
            "Hyderabad",
            "Jagtial",
            "Jangaon",
            "Jayashankar Bhoopalpally",
            "Jogulamba Gadwal",
            "Kamareddy",
            "Karimnagar",
            "Khammam",
            "Komaram Bheem Asifabad",
            "Mahabubabad",
            "Mahabubnagar",
            "Mancherial",
            "Medak",
            "Medchal",
            "Nagarkurnool",
            "Nalgonda",
            "Nirmal",
            "Nizamabad",
            "Peddapalli",
            "Rajanna Sircilla",
            "Rangareddy",
            "Sangareddy",
            "Siddipet",
            "Suryapet",
            "Vikarabad",
            "Wanaparthy",
            "Warangal (Rural)",
            "Warangal (Urban)",
            "Yadadri Bhuvanagiri"
        ],
        "Tripura": [
            "Dhalai",
            "Gomati",
            "Khowai",
            "North Tripura",
            "Sepahijala",
            "South Tripura",
            "Unakoti",
            "West Tripura"
        ],
        "Uttarakhand": [
            "Almora",
            "Bageshwar",
            "Chamoli",
            "Champawat",
            "Dehradun",
            "Haridwar",
            "Nainital",
            "Pauri Garhwal",
            "Pithoragarh",
            "Rudraprayag",
            "Tehri Garhwal",
            "Udham Singh Nagar",
            "Uttarkashi"
        ],
        "Uttar Pradesh": [
            "Agra",
            "Aligarh",
            "Allahabad",
            "Ambedkar Nagar",
            "Amethi (Chatrapati Sahuji Mahraj Nagar)",
            "Amroha (J.P. Nagar)",
            "Auraiya",
            "Azamgarh",
            "Baghpat",
            "Bahraich",
            "Ballia",
            "Balrampur",
            "Banda",
            "Barabanki",
            "Bareilly",
            "Basti",
            "Bhadohi",
            "Bijnor",
            "Budaun",
            "Bulandshahr",
            "Chandauli",
            "Chitrakoot",
            "Deoria",
            "Etah",
            "Etawah",
            "Faizabad",
            "Farrukhabad",
            "Fatehpur",
            "Firozabad",
            "Gautam Buddha Nagar",
            "Ghaziabad",
            "Ghazipur",
            "Gonda",
            "Gorakhpur",
            "Hamirpur",
            "Hapur (Panchsheel Nagar)",
            "Hardoi",
            "Hathras",
            "Jalaun",
            "Jaunpur",
            "Jhansi",
            "Kannauj",
            "Kanpur Dehat",
            "Kanpur Nagar",
            "Kanshiram Nagar (Kasganj)",
            "Kaushambi",
            "Kushinagar (Padrauna)",
            "Lakhimpur - Kheri",
            "Lalitpur",
            "Lucknow",
            "Maharajganj",
            "Mahoba",
            "Mainpuri",
            "Mathura",
            "Mau",
            "Meerut",
            "Mirzapur",
            "Moradabad",
            "Muzaffarnagar",
            "Pilibhit",
            "Pratapgarh",
            "RaeBareli",
            "Rampur",
            "Saharanpur",
            "Sambhal (Bhim Nagar)",
            "Sant Kabir Nagar",
            "Shahjahanpur",
            "Shamali (Prabuddh Nagar)",
            "Shravasti",
            "Siddharth Nagar",
            "Sitapur",
            "Sonbhadra",
            "Sultanpur",
            "Unnao",
            "Varanasi"
        ],
        "West Bengal": [
            "Alipurduar",
            "Bankura",
            "Birbhum",
            "Burdwan (Bardhaman)",
            "Cooch Behar",
            "Dakshin Dinajpur (South Dinajpur)",
            "Darjeeling",
            "Hooghly",
            "Howrah",
            "Jalpaiguri",
            "Kalimpong",
            "Kolkata",
            "Malda",
            "Murshidabad",
            "Nadia",
            "North 24 Parganas",
            "Paschim Medinipur (West Medinipur)",
            "Purba Medinipur (East Medinipur)",
            "Purulia",
            "South 24 Parganas",
            "Uttar Dinajpur (North Dinajpur)"
        ]

    };
    var countrySel = document.getElementById("selectCountry");
    for(var c=0; c < countryList.length; c++){
        countrySel.options[countrySel.options.length] = new Option(countryList[c].name, countryList[c].code);
    }
   countrySel.onchange = function (ev) {
        if(ev.target.value == "IN"){
            var htmlStr = "<label for=\"stateSelect\">Select Your State</label>\n" +
                " <select class=\"form-control\" id=\"stateSelect\" name=\"stateSelect\">\n" +
                " <option value=\"\" selected=\"selected\">Select State</option>\n" +
                " </select>";
            $('#divState').html(htmlStr);
            htmlStr = " <label for=\"districtSelect\">Select Your City</label>\n" +
                " <select class=\"form-control\" id=\"districtSelect\" name=\"districtSelect\">\n" +
                " <option value=\"\" selected=\"selected\">Select City</option>\n" +
                " </select>";
            $('#divDistrict').html(htmlStr);
            populateStateAndCity()
        }else {
            districtAndStateAsInputs();
        }
   };

    function districtAndStateAsInputs() {
        var htmlStr = "<label for=\"stateSelect\">Enter Your State</label>\n" +
            " <input id='stateSelect' type=\"text\" class=\"form-control\" placeholder=\"Enter State\" name=\"stateSelect\">";
        $('#divState').html(htmlStr);
        htmlStr = "<label for=\"districtSelect\">Enter Your City</label>\n" +
            " <input id='districtSelect' type=\"text\" class=\"form-control\" placeholder=\"Enter City\" name=\"districtSelect\">";
        $('#divDistrict').html(htmlStr);
    }

    function populateStateAndCity() {
        var _state = "${state}", _district = "${district}";
        
        var stateSel = document.getElementById("stateSelect"),
            districtSel = document.getElementById("districtSelect");
        for (var state in stateObject) {
            stateSel.options[stateSel.options.length] = new Option(state, state);
        }
        $("#stateSelect").val(_state);
        stateSel.onchange = function () {
            districtSel.length = 1; // remove all options bar first
            if (this.selectedIndex < 1) return; // done
            var district = stateObject[stateSel.value];
            for (var i = 0; i < district.length; i++) {
                districtSel.options[districtSel.options.length] = new Option(district[i], district[i]);
            }
            $("#districtSelect").val(_district);
        }
        stateSel.onchange(); // reset in case page is reloaded
        stateSel.onload = function () {
            districtSel.length = 1; // remove all options bar first
            if (this.selectedIndex < 1) return; // done
            var district = stateObject[stateSel.value];
            for (var i = 0; i < district.length; i++) {
                districtSel.options[districtSel.options.length] = new Option(district[i], district[i]);
            }
        }
        stateSel.onload(); // reset in case page is reloaded
        if(state != undefined && state != null && state != ""){
            $("#stateSelect").trigger("change");
        }
    }
    if(country != undefined && country != null && country != ""){
        $("#selectCountry").val(country);
        $("#selectCountry").trigger("change");
    }

    function updateImage() {
        $('#fileoption1').trigger('click');
    }

    function updateProfile(){
        var oFile = document.getElementById("fileoption1").files[0]; // <input type="file" id="fileUpload" accept=".jpg,.png,.gif,.jpeg"/>

        if(oFile.name.search(/[ <>{}#/\\?:!&$^*%`|+"]/g)>-1){
            alert("File Name must not contain any space and special characters \n < > { } # / \\ ? : ! & $ ^ * % ` | + ")
            return
        }

        if (oFile.size > 2097152) // 2 mb for bytes.
        {
            $('#file-error').css('color','red');
            return;
        }
        else {
            // var filename=getFile(fileoption1.value);
            // filename +='test';
            // oFile.value=filename;
            document.uploadProfileImage.submit();
        }
    }

    function generateOtp() {
        $('.loading-icon').removeClass('hidden');
        var email = "${email}", mobile = "${mobile}";
        email = $("<textarea/>").html(email).text();
        var type="${userNameType}",contact = "";
        // alert(type);
        if($('#mobile').val().length!=10) {
            alert("Please enter 10 digit mobile number.");
            $('.loading-icon').addClass('hidden');
        } else if(type == "email" && mobile != $('#mobile').val()){
                contact = $("#mobile").val();
            <g:remoteFunction controller="usermanagement" action="generateOtp" params="'contact='+contact" onSuccess="openOtpModal(data)"  />;
            var sanitizedMobile = contact[0] + "*".repeat(contact.length - 3) + contact.slice(-2);
            document.getElementById("OtpTextMessage").innerHTML = "OTP has been sent to your mobile number <br class='d-sm-none'>+91-"+sanitizedMobile+".";
            }else if(type == "mobile" && email != $("#edit-email").val()) {
                contact = $("#edit-email").val();
            $('.loading-icon').removeClass('hidden');
            <g:remoteFunction controller="usermanagement" action="generateOtp" params="'contact='+contact" onSuccess="openOtpModal(data)"  />;
            var parts = contact.split("@"), len = parts[0].length;
            if(len >= 5){
                var sanitizedEmail = contact.replace(parts[0].slice(2,-2), "*".repeat(5));
                document.getElementById("OtpTextMessage").innerHTML = "OTP has been sent to your email "+sanitizedEmail;
            } else {
                document.getElementById("OtpTextMessage").innerHTML = "OTP has been sent to your email "+contact;
            }

            }else {
            submitEditForm({'status':"OK"});
        }
    }

    function openOtpModal(data) {
        $('.loading-icon').addClass('hidden');
        if(data['status'] == "OK") {
           $('#otp-modal').modal('show').on('shown.bs.modal', function(e) {
               $("#otp").focus();
           });
       }else alert("OTP could not be sent");
    }

    function checkOtp() {
        var type="${userNameType}";
        $('.loading-icon').removeClass('hidden');
        if(type == "mobile"){
            var email = $("#edit-email").val(), email_otp = $('#otp').val();
            <g:remoteFunction controller="creation" action="checkOTPForProfileEdit" onSuccess="submitEditForm(data)" params="'email='+email+'&email_otp='+email_otp" />;
        }else if(type == "email"){
            var mobile = $("#mobile").val(), mobile_otp = $('#otp').val();
            <g:remoteFunction controller="creation" action="checkOTPForProfileEdit" onSuccess="submitEditForm(data)" params="'mobile='+mobile+'&mobile_otp='+mobile_otp" />;
        }
    }

    function submitEditForm(data){
        if(data.status == "OK") $('#edit-user-form').submit();
        else if(data.status == "username error") {
            $('.loading-icon').addClass('hidden');
            var type="${userNameType}", contact = "";
            if(type == "mobile"){
                var contact = $("#edit-email").val();
            }else if(type == "email"){
                var contact = $("#mobile").val();
            }
            alert("Contact can not be changed to " + contact +". It is used as a login User Name");
        }else {
            $('.loading-icon').addClass('hidden');
            $("#errorOTP").text("OTP verification failed.").removeClass("hidden");
            $("#otp").focus();
        }
    }

    function numberOnly(id) {
        var element = document.getElementById(id);
        var regex = /[^0-9]/gi;
        element.value = element.value.replace(regex, "");
    }

    $(document).ready(function(){
        $("#otp").keyup(function () {
            $("#errorOTP").addClass('hidden');
        });
    });
</script>
