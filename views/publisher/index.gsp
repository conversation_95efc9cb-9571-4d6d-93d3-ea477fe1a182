<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:javascript src="moment.min.js"/>
<script>
    var loggedIn = false;
</script>

<sec:ifLoggedIn>
    <script>
        loggedIn = true;
    </script>
</sec:ifLoggedIn>

<div class="container-fluid publish-management" style="min-height: calc(100vh - 160px);">
    <div class="row">
        <div class="col-md-9 main" style=" margin: 40px auto; float: none; padding: 15px;">
            <h4 for="publisherId" style="display: block;">Add/Update Publisher</h4>

            <div class=" dflex_pub">
                <input type="text" class="form-control hidden" id="id" value="${pub.id}"/>
                <input type="text" class="form-control hidden" id="urls" value="${puburls.join('~')}"/>
            </div>

            <div class="dflex_pub">
                <label for="pubName"></label>
                <input type="text" class="form-control" id="pubName" value="${pub.name}" onchange="onPublisherNameChanged()"
                       placeholder="Publisher Name"/>
            </div>

            <div class="dflex_pub">
                <label for="website"></label>
                <input type="text" class="form-control" id="website" value="${pub.website}"
                       placeholder="Publisher's Website URL"/>
            </div>

            <div class="dflex_pub">
                <label for="contactPerson"></label>
                <input type="text" class="form-control" id="contactPerson"
                       value="${pub.contactPerson}"
                       placeholder="Contact Person Name"/>
            </div>

            <div class="dflex_pub">
                <label for="email"></label>
                <input type="text" class="form-control" id="email" value="${pub.email}"
                       placeholder="Contact Person Email"/>
            </div>

            <div class="dflex_pub">
                <label for="mobile"></label>
                <input type="text" class="form-control" id="mobile" value="${pub.mobile}"
                       placeholder="Contact Person Mobile"/>
            </div>

            <div class="dflex_pub">
                <label for="urlName"></label>
                <input type="text" class="form-control" id="urlName" value="${pub.urlname}" onchange="onUrlNameChanged()"
                       placeholder="URL Name"/>
                <p class="m-4" id="displayUrl"></p>

            </div>
            <div class="dflex_pub">
            </div>
            <div class="dflex_pub">
                <label for="city"></label>
                <input type="text" class="form-control" id="city" value="${pub.city}"
                       placeholder="City"/>
            </div>

            <div class="dflex_pub">
                <label for="state"></label>
                <input type="text" class="form-control" id="state" value="${pub.state}"
                       placeholder="State"/>
            </div>

            <div class="dflex_pub">
                <label for="country"></label>
                <input type="text" class="form-control" name="country" id="country" value="${pub.country}"
                       placeholder="Country"/>
            </div>

            <div class="dflex_pub">
                <button id="updatePub" class="btn btn-primary m-1"
                        onclick="updatePublisherr()">Add/Update Publisher</button>
            </div>
        </div>
    </div>
</div>
<g:render template="/${session['entryController']}/footer_new"></g:render>

<script>




    function updatePublisherr() {
        onUrlNameChanged()
        var id = document.getElementById("id").value.toString().trim()
        var name = document.getElementById("pubName").value.toString().trim()
        var website = document.getElementById("website").value.toString().trim()
        var contactPerson = document.getElementById("contactPerson").value.toString().trim()
        var email = document.getElementById("email").value.toString().trim()
        var mobile = document.getElementById("mobile").value.toString().trim()
        var urlname = document.getElementById("urlName").value.toString().trim()
        var city = document.getElementById("city").value.toString().trim()
        var state = document.getElementById("state").value.toString().trim()
        var country = document.getElementById("country").value.toString().trim()
        if (name == "") {
            alert("Publisher Name cannot be empty.")
            return
        }
        var urlreg = /[A-Z]+/
        if (urlname == "" || urlname.match(urlreg) || urlname.search(/[ <>{}#/\\?:!&$^*%`|+"]/g)>-1) {
            alert("Publisher Url must be all small characters and must not contain any space and special characters \n < > { } # / \\ ? : ! & $ ^ * % ` | + ")
            return
        }
        if(email.length>0 && !validateEmail(email)){
            alert("Enter Valid Email")
            document.getElementById("email").value =""
            return
        }
        if(mobile.length>0 && mobile.length<10 && !parseInt(mobile)){
            alert('Enter Valid mobile')
            document.getElementById("mobile").value =""
            return
        }
        var webreg = /[(http(s)?):\/\/(www\.)?a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)/ig
        if(website.length>0 && !webreg.test(website)){
            alert('Enter Valid Website')
            document.getElementById("website").value =""
            return
        }

        <g:remoteFunction controller="publisherManagement" action="updatePublisher"
            params="'id='+id+'&name='+name+'&website='+website+'&contactPerson='+contactPerson+'&email='+email+'&mobile='+mobile+
            '&urlname='+urlname+'&city='+city+'&state='+state+'&country='+country" onSuccess="publisherUpdated()"/>

    }

    function publisherUpdated() {
        alert("Publisher Updated Successfully")
        window.location.href = "/admin/managePublishers";
    }
    const onPublisherNameChanged=()=>{
        document.getElementById("urlName").value = document
            .getElementById("pubName").value
            .toString()
            .toLowerCase()
            .replace(/ /g, "-")
            .replace(/\./g, "-")
        onUrlNameChanged()
    }
    const onUrlNameChanged=()=>{
        document.getElementById('urls').value.split("~").forEach((u, i)=>{
            if(u==document.getElementById('urlName').value){
                alert("Url Not Available")
                document.getElementById('urlName').value = ""
                document.getElementById('displayUrl').innerText = ""
            }else{
                document.getElementById('displayUrl').innerText = 'Url = /publisherManagement/'+document.getElementById('urlName').value
            }
        })
    }
    function validateEmail(field) {
        var regex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,5}$/;
        return (regex.test(field)) ? true : false;
    }

</script>