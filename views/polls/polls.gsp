<%--
  Created by IntelliJ IDEA.
  User: krishnashastry
  Date: 04/03/21
  Time: 12:08 PM
--%>
<g:render template="/wonderpublish/loginChecker"></g:render>
<% if("sage".equals(session["entryController"])){%>
<g:render template="/${session['entryController']}/navheader"></g:render>
<%}else {%>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<%}%>

<link href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css" rel="stylesheet" type="text/css">
<script>
    var defaultSiteName="${session['entryController']}";
</script>

<style>
body {
    padding: 0 !important;
    margin: 0;
}
.cke_reset_all, .cke_reset_all *, .cke_reset_all a, .cke_reset_all textarea {
    z-index: 999991 !important;
}
.cke_dialog_background_cover {
    z-index: 999991 !important;
}
.sweet-overlay {
    z-index: 99999;
}
#imgPopupModal .close {
    position: absolute;
    right: -10px;
    top: -10px;
    width: 25px;
    height: 25px;
    background: #000;
    border: 2px solid #fff;
    color: #FFF;
    border-radius: 50px;
    font-size: 14px;
    font-weight: normal;
    opacity: 1;
    z-index: 999;
}
#imgPopupModal .close:focus {
    outline: 0;
}
#imgPopupModal .modal-body {
    padding: 7px;
}
#imgPopupModal #originalImage {
    width: 100%;
}
#imgPopupModal #imgCaption {
    text-align: center;
    padding: 7px;
    font-size: 14px;
}
.lightblue_bg {
    background-color: #F3F7FA;
    box-shadow: 0 2px 10px #e9eef5;
}
#pollingTbody button {
    font-size: 13px;
}
#pollingTbody tr td p {
    margin-bottom: 0;
}
#add-poll-modal .modal-lg {
    max-width: 950px;
}
#add-poll-modal form label {
    font-weight: 600;
    /*text-transform: uppercase;*/
    font-size: 14px;
}
#add-poll-modal form .change-img-text {
    color: #007bff;
    padding-left: 7px;
}
#add-poll-modal form input[type="checkbox"] {
    width: 16px;
    height: 16px;
}
#add-poll-modal form .options-img-name, #poll-img-span {
    border-radius: 2px;
    cursor: pointer;
}
#add-poll-modal form .options-img-name:hover, #poll-img-span:hover {
    opacity: 0.7;
}
/*#add-poll-modal form .options-img-name {*/
/*    overflow: hidden;*/
/*    text-overflow: ellipsis;*/
/*    display: -webkit-box;*/
/*    -webkit-line-clamp: 1;*/
/*    -webkit-box-orient: vertical;*/
/*    max-width: 250px;*/
/*    !*height: 24px;*!*/
/*    float: left;*/
/*}*/
#add-poll-modal form .hide-file-input {
    visibility:hidden;
    width:0;
    height:0
}
.img-placeholder {
    position: relative;
    top: 7px;
    cursor: pointer;
    display: flex;
    align-items: center;
}
.img-placeholder img {
    width: 30px;
}
.img-placeholder p {
    font-weight: normal;
    margin: 0;
    padding-left: 10px;
}
.sweet-alert button {
    padding: 7px 30px;
}

.image-upload > input
{
    display: none;
}

.inlineEditor{
    min-height: 40px;
    border-bottom: 1px solid #828180;
}
.cke_textarea_inline
{
    height: 50px;
    overflow: auto;
    border: 1px solid gray;
    -webkit-appearance: textfield;
}

.form-group .cke_textarea_inline
{
    height: 35px;
    overflow: auto;
    border: 1px solid gray;
    -webkit-appearance: textfield;
}

</style>

<div class="loading-icon hidden" style="z-index: 999991;">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>

<div class="polls">
    <div class="container mt-5 mb-4 px-0">
        <div class="d-flex align-items-center">
            <button class="btn btn-sm mr-3" onclick="javascript:history.back()">Back</button>
            <h3 class="mb-0">Polls</h3>
        </div>
    </div>
    <div class="container lightblue_bg border rounded mb-5 p-4">
        <div class="text-left mb-4">
            <h6 class="mb-3"><strong>Chapter : </strong> ${params.chapterName}</h6>
            <h6><strong>Video :</strong> ${params.resTitle}</h6>
        </div>
        <div class="text-left">
            <button class="btn btn-lg btn-primary col-md-2" onclick="openAddModel()" >New Poll</button>
        </div>
        <div class="mt-4">
            <table class="table table-bordered">
                <thead class="thead-light">
                <th width="40%">Name</th>
                <th width="40%">Question</th>
                <th width="20%">Action</th>
                </thead>
                <tbody id="pollingTbody" class="bg-white">

                </tbody>
            </table>
        </div>
    </div>
</div>

<div class="modal fade" id="add-poll-modal">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">

            <!-- Modal body -->
            <div class="modal-body">
          <div id="add-poll-modal-body">    
                    <form id="addpolls">
                        <div class="d-flex pt-3">
                            <div class="form-group col-md-6">
                                <label for="pollName">Poll Title</label>
                                <input id="pollName" name="pollName" class="form-control"/>
                            </div>
%{--                            <div class="form-group col-md-6">--}%
%{--                                <label for="question">Question</label>--}%
%{--                                <input id="question" name="question" class="form-control"/>--}%
%{--                            </div>--}%
                            <div class="form-group col-md-6">
                            <label id="questionLabel">Question </label>
                            <input type="hidden" name="question">
                            <div class="cktext">
                                <div  contenteditable="true"  id="question" class="inlineEditor"></div>
                            </div>
                        </div>

                        </div>
                        <div class="form-group col-md-12">
                            <label for="poll-img">Image<br>
                                <span class="img-placeholder poll-img-file">
                                    <img src="${assetPath(src: 'landingpageImages/img-placeholder.png')}" alt="">
                                    <p id="poll-img-filename"></p>
                                </span>
                            </label>
                            <input id="poll-img" class="hide-file-input" onchange="fileChanged('pollImg','poll-img')" type="file" accept="image/png, image/jpeg, image/gif ,image/svg" style="font-size: 14px;">
                            <div id="poll-img-div" style="display: none"><img id="poll-img-span" src="" alt="" onclick="openImagePopup(this);" width="50" height="50"> <a href="javascript:" class="change-img-text" onclick="removeImgSpan('poll-img')">Change image</a> </div>
                            <hr>
                        </div>
                        <div class="d-flex align-items-end">
                            <div class="form-group col-md-4">

%{--                                <label for="optionText1">Option 1</label>--}%
%{--                                <input id="optionText1" name="optionText1" class="form-control"/>--}%
                                <b>Option 1:</b>
                                <input type="hidden" name="optionText1">
                                <div class="cktext">
                                    <div  contenteditable="true"  id="optionText1" class="inlineEditor"></div>
                                </div>
                            </div>
                            <div class="form-group col-md-2">
                                <input class="option-checkbox" type="checkbox" id="option1" name="option1" value="true">
                                <label for="option1" style="text-transform: capitalize;position: relative;top: -3px;cursor: pointer;">Correct</label>
                            </div>
                            <div class="form-group col-md-6">
                                <label for="poll-option1-img">Option 1 Image<br>
                                    <span class="img-placeholder poll-option1-img-file">
                                        <img src="${assetPath(src: 'landingpageImages/img-placeholder.png')}" alt="">
                                        <p id="poll-option1-img-filename" class="options-img-name"></p>
                                    </span>
                                </label>
                                <input id="poll-option1-img" class="hide-file-input" onchange="fileChanged('option1','poll-option1-img')" type="file" accept="image/png, image/jpeg, image/gif ,image/svg" style="font-size: 14px;">
                                <div id="poll-option1-img-div" style="display: none"><img id="poll-option1-img-span" src="" alt="" onclick="openImagePopup(this);" width="40" height="40" class="options-img-name"> <a href="javascript:" class="change-img-text" onclick="removeImgSpan('poll-option1-img')">Change image</a> </div>
                            </div>
                        </div>
                        <div class="d-flex align-items-end">
                            <div class="form-group col-md-4">
%{--                                <label for="optionText2">Option 2</label>--}%
%{--                                <input id="optionText2" name="optionText2" class="form-control"/>--}%
                                <b>Option 2:</b>
                                <input type="hidden" name="optionText2">
                                <div class="cktext">
                                    <div  contenteditable="true"  id="optionText2" class="inlineEditor"></div>
                                </div>
                            </div>
                            <div class="form-group col-md-2">
                                <input class="option-checkbox" type="checkbox" id="option2" name="option2" value="true">
                                <label for="option2" style="text-transform: capitalize;position: relative;top: -3px;cursor: pointer;">Correct</label>
                            </div>
                            <div class="form-group col-md-6">
                                <label for="poll-option2-img">Option 2 Image<br>
                                    <span class="img-placeholder poll-option2-img-file">
                                        <img src="${assetPath(src: 'landingpageImages/img-placeholder.png')}" alt="">
                                        <p id="poll-option2-img-filename" class="options-img-name"></p>
                                    </span>
                                </label>
                                <input id="poll-option2-img" class="hide-file-input" onchange="fileChanged('option2','poll-option2-img')" type="file" accept="image/png, image/jpeg, image/gif ,image/svg" style="font-size: 14px;">
                                <div id="poll-option2-img-div" style="display: none"><img id="poll-option2-img-span" src="" alt="" onclick="openImagePopup(this);" width="40" height="40" class="options-img-name"> <a href="javascript:" class="change-img-text" onclick="removeImgSpan('poll-option2-img')">Change image</a> </div>
                            </div>
                        </div>
                        <div class="d-flex align-items-end">
                            <div class="form-group col-md-4">
%{--                                <label for="optionText3">Option 3</label>--}%
%{--                                <input id="optionText3" name="optionText3" class="form-control"/>--}%
                                <b>Option 3:</b>
                                <input type="hidden" name="optionText3">
                                <div class="cktext">
                                    <div  contenteditable="true"  id="optionText3" class="inlineEditor"></div>
                                </div>
                            </div>
                            <div class="form-group col-md-2">
                                <input class="option-checkbox" type="checkbox" id="option3" name="option3" value="true">
                                <label for="option3" style="text-transform: capitalize;position: relative;top: -3px;cursor: pointer;">Correct</label>
                            </div>
                            <div class="form-group col-md-6">
                                <label for="poll-option3-img">Option 3 Image<br>
                                    <span class="img-placeholder poll-option3-img-file">
                                        <img src="${assetPath(src: 'landingpageImages/img-placeholder.png')}" alt="">
                                        <p id="poll-option3-img-filename" class="options-img-name"></p>
                                    </span>
                                </label>
                                <input id="poll-option3-img" class="hide-file-input" onchange="fileChanged('option3','poll-option3-img')" type="file" accept="image/png, image/jpeg, image/gif ,image/svg" style="font-size: 14px;">
                                <div id="poll-option3-img-div" style="display: none"><img id="poll-option3-img-span" src="" alt="" onclick="openImagePopup(this);" width="40" height="40" class="options-img-name"> <a href="javascript:" class="change-img-text" onclick="removeImgSpan('poll-option3-img')">Change image</a> </div>
                            </div>
                        </div>
                        <div class="d-flex align-items-end">
                            <div class="form-group col-md-4">
%{--                                <label for="optionText4">Option 4</label>--}%
%{--                                <input id="optionText4" name="optionText4" class="form-control"/>--}%
                                <b>Option 4:</b>
                                <input type="hidden" name="optionText4">
                                <div class="cktext">
                                    <div  contenteditable="true"  id="optionText4" class="inlineEditor"></div>
                                </div>
                            </div>
                            <div class="form-group col-md-2">
                                <input class="option-checkbox" type="checkbox" id="option4" name="option4" value="true">
                                <label for="option4" style="text-transform: capitalize;position: relative;top: -3px;cursor: pointer;">Correct</label>
                            </div>
                            <div class="form-group col-md-6">
                                <label for="poll-option4-img">Option 4 Image<br>
                                    <span class="img-placeholder poll-option4-img-file">
                                        <img src="${assetPath(src: 'landingpageImages/img-placeholder.png')}" alt="">
                                        <p id="poll-option4-img-filename" class="options-img-name"></p>
                                    </span>
                                </label>
                                <input id="poll-option4-img" class="hide-file-input" onchange="fileChanged('option4','poll-option4-img')" type="file" accept="image/png, image/jpeg, image/gif ,image/svg" style="font-size: 14px;">
                                <div id="poll-option4-img-div" style="display: none"><img id="poll-option4-img-span" src="" alt="" onclick="openImagePopup(this);" width="40" height="40" class="options-img-name"> <a href="javascript:" class="change-img-text" onclick="removeImgSpan('poll-option4-img')">Change image</a> </div>
                            </div>
                        </div>
                        <div class="d-flex align-items-center justify-content-center py-4">
                            <button type="button" id="image-modal-close" class="btn btn-default col-md-2 mr-3" data-dismiss="modal">CANCEL</button>
                            <button type="button" class="btn btn-primary col-md-2" onclick="javascript:addPolling()"> SAVE </button>
                        </div>
                    </form>
                </div>
            </div>

        </div>
    </div>
</div>

<div class="modal fade image-popup-modal" id="imgPopupModal" data-keyboard="false" data-backdrop="true" >
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content shadow rounded">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">X</span>
            </button>
            <!-- Modal body -->
            <div class="modal-body">
                <img id="originalImage" src="">
                <div id="imgCaption"></div>
            </div>
        </div>
    </div>
</div>

<% if("sage".equals(session["entryController"])){%>
<g:render template="/${session['entryController']}/footer"></g:render>
<%}else {%>
<g:render template="/${session['entryController']}/footer_new"></g:render>
<%}%>
<script type="text/javascript"
        src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS_HTML-full">
</script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js"></script>
<script src="https://cdn.ckeditor.com/4.7.1/full-all/ckeditor.js"></script>
<script>
    CKEDITOR.inline( 'question', {
        customConfig: '/assets/ckeditor/customConfig.js'

    });
    CKEDITOR.inline( 'optionText1', {
        customConfig: '/assets/ckeditor/customConfig.js'

    });
    CKEDITOR.inline( 'optionText2', {
        customConfig: '/assets/ckeditor/customConfig.js'

    });
    CKEDITOR.inline( 'optionText3', {
        customConfig: '/assets/ckeditor/customConfig.js'

    });
    CKEDITOR.inline( 'optionText4', {
        customConfig: '/assets/ckeditor/customConfig.js'

    });

    if ( CKEDITOR.env.ie && CKEDITOR.env.version == 8 ) {
        document.getElementById( 'ie8-warning' ).className = 'tip alert';
    }

    CKEDITOR.on('dialogDefinition', function (e) {
        var dialog = e.data.definition.dialog;
        dialog.on('show', function () {
            var element = this.getElement();
            var labelledby = element.getAttribute('aria-labelledby');
            var nativeElement = document.querySelector("[aria-labelledby='" + labelledby + "']");
            nativeElement.onclick = function (evt) {
                if ((evt.target.tagName == "input" || evt.target.tagName == "select" || evt.target.tagName == "textarea") &&
                    -1 != evt.target.className.indexOf("cke_dialog_ui_input")) {
                    evt.target.focus();
                }
            }
        });
    });

    $('#add-poll-modal').on('shown.bs.modal', function() {
        $(document).off('focusin.modal');
    });

        var resId = "${resId}",currentPollsId = 0,fileObj={};
    function addPolling() {
        var details = [];
        var questionValue = CKEDITOR.instances.question.getData();
        var pollName = $('#pollName').val(), question = questionValue,valid = true, message="";
        if(pollName == "" || pollName == undefined || pollName == null) {
            valid = false;
            message = "Enter Poll Name.";
        }
        if(question == "" || question == undefined || question == null) {
            valid = false;
            message = message +" Enter Poll Question.";
        }
        for(var i=1;i<=4;i++){
            var optionText = CKEDITOR.instances["optionText"+i].getData();
            if(optionText != "" && optionText != undefined && optionText != null){
                var optionTrue = $("#option"+i).prop("checked");
                if(fileObj['option'+i] != undefined && fileObj['option'+i] != null) details.push({'optionText':optionText,'correctOption':optionTrue,'imgName':fileObj['option'+i].name});
                else if($("#poll-option"+i+"-img-span").attr('src') != "" && $("#poll-option"+i+"-img-span").attr('src') != undefined){
                    var getImgSrc = $("#poll-option"+i+"-img-span").attr('src');
                    var getImgName = new URLSearchParams(getImgSrc);
                    var imgValue = getImgName.get("fileName");
                    //var imgValue = $("#poll-option"+i+"-img-span").text();
                    details.push({'optionText':optionText,'correctOption':optionTrue,'imgName':imgValue});
                }else details.push({'optionText':optionText,'correctOption':optionTrue});
            }
        }
        if(details.length < 2){
            valid = false;
            message = message + " At least 2 options have to be given.";
        }
        if(valid){
            var obj = {
                'name':pollName,
                'question':questionValue,
                'resId':resId,
                'details':details
            };
            if(fileObj['pollImg'] != undefined && fileObj['pollImg'] != null) obj['imgName'] = fileObj['pollImg'].name;
            if(currentPollsId >0){
                obj['id'] = currentPollsId;
                swal({
                    title: "Updated",
                    text: "Poll has been updated successfully!",
                    type: "success",
                    allowOutsideClick: false,
                    showConfirmButton: true,
                    showCancelButton: false,
                    confirmButtonColor: "#231F20",
                    confirmButtonText: "Ok",
                    cancelButtonText: "Cancel",
                    closeOnConfirm: true,
                    closeOnCancel: false,
                    allowEscapeKey: false,
                    customClass: '',
                }, function() {
                    makePostAjaxCall("../polls/updatePolls",obj);
                });
            }else {
                swal({
                    title: "Added",
                    text: "Poll has been added successfully!",
                    type: "success",
                    allowOutsideClick: false,
                    showConfirmButton: true,
                    showCancelButton: false,
                    confirmButtonColor: "#231F20",
                    confirmButtonText: "Ok",
                    cancelButtonText: "Cancel",
                    closeOnConfirm: true,
                    closeOnCancel: false,
                    allowEscapeKey: false,
                    customClass: '',
                }, function() {
                    makePostAjaxCall("../polls/addPolls",obj);
                    //location.reload();
                });
            }
        }else {
            alert(message);
        }
    }

    function resetCache(){

        <g:remoteFunction controller="polls" action="resetPollsCache" params="'resId='+resId"/>
    }

    function getpollsList() {
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="polls" action="getPollingByResId" params="'id='+resId" onSuccess = 'displayPolls(data)'/>
    }

    function displayPolls(data) {
        $('.loading-icon').addClass('hidden');
        if(data.polls.length > 0) {
            document.getElementById("pollingTbody").innerHTML = "";
            for(var i=0; i < data.polls.length; i++){
                var tr = document.createElement("tr");
                document.getElementById("pollingTbody").appendChild(tr);
                var nameTd = document.createElement("td");
                nameTd.innerText = data.polls[i].name;
                tr.appendChild(nameTd);
                var questionTd = document.createElement("td");
                questionTd.innerHTML = data.polls[i].question;
                MathJax.Hub.Queue(["Typeset", MathJax.Hub, questionTd]);
                tr.appendChild(questionTd);

                var editBtnTd = document.createElement("td");
                var editBtn = document.createElement("button");
                editBtn.addEventListener( 'click', function (params) {
                    return  function () {
                        getPollingDetails(params);
                    }
                }(data.polls[i].id));
                editBtn.innerText = "Edit";
                var btnClasses = "btn btn-sm btn-outline-primary mr-2";
                editBtn.classList = btnClasses;
                editBtnTd.appendChild(editBtn);
                tr.appendChild(editBtnTd);

                //var deleteBtnTd = document.createElement("td");
                var deleteBtn = document.createElement("button");
                deleteBtn.addEventListener( 'click', function (params) {
                    return  function () {
                        deletePolling(params);
                    }
                }(data.polls[i].id));
                deleteBtn.innerText = "Delete";
                var btnClasses = "btn btn-sm btn-outline-danger";
                deleteBtn.classList = btnClasses;
                editBtnTd.appendChild(deleteBtn);
                tr.appendChild(editBtnTd);
            }
        } else {
            document.getElementById("pollingTbody").innerHTML = "<tr class='text-center'><td colspan='3'>No polls added yet!</td></tr>";

        }
    }

    function openAddModel(){
        $('#addpolls').trigger("reset");
        CKEDITOR.instances.question.setData('');
        $('#poll-img').show();
        $('#poll-img-div').hide();
        $("#poll-img-span").attr('src', '');
        $("#poll-img-span").attr('alt', '');
        $(".poll-img-file").css("display", "flex");
        $("#poll-img-filename").text("");
        for(var j=1;j<=4;j++) {
            CKEDITOR.instances["optionText"+j].setData('');
            $('#poll-option' + j + '-img').show();
            $("#poll-option" + j + "-img-div").hide();
            $("#poll-option" + j + "-img-span").attr('src', '');
            $("#poll-option" + j + "-img-span").attr('alt', '');
            $(".poll-option" + j + "-img-file").css("display", "flex");
            $("#poll-option" + j + "-img-filename").text("");
        }
        $('#add-poll-modal').modal('show');
    }

    function closeAddModel() {
        currentPollsId = 0;
        $('#add-poll-modal').modal('hide');
    }
    function makePostAjaxCall(url,dataObj) {
        //$('.loading-icon').removeClass('hidden');
        $.ajax({
            type: "POST",
            contentType: "application/json",
            dataType:"json",
            url: url,
            data: JSON.stringify(dataObj),
            success: function (response) {
                currentPollsId = 0;
                getpollsList();
                closeAddModel();
                savePollImages(response.polls.id);
                resetCache();
            }
        });
    }

    function getPollingDetails(rollsId) {
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="polls" action="getPollingDetailsByPollId" params="'id='+rollsId" onSuccess = 'populateAddPolls(data)'/>
    }

    function populateAddPolls(data) {
        $('.loading-icon').addClass('hidden');
        openAddModel();
        var polls = data.polls, details = data.details;
        $($("#pollName")[0]).val(polls.name);
        //$($("#question")[0]).html(polls.question);
        CKEDITOR.instances.question.setData(polls.question);
        var pollImgSrc="";
        var optionImgSrc="";
        if(polls.imgName != undefined && polls.imgName != "") {
            pollImgSrc = "/polls/getPollsImages?resId="+polls.resId+"&fileName="+polls.imgName+"&pollsId="+polls.id+"&imgType=polls";
            $('#poll-img').hide();
            $('#poll-img-div').show();
            $("#poll-img-span").attr('src', pollImgSrc);
            $("#poll-img-span").attr('alt', polls.imgName);
            $(".poll-img-file").css("display", "none");
        }else{
            $('#poll-img').show();
            $('#poll-img-div').hide();
            $("#poll-img-span").attr('src', '');
            $("#poll-img-span").attr('alt', '');
            $(".poll-img-file").css("display", "flex");
        }
        var j =1;
        for(var i=0;i<details.length;i++){
            //$('#optionText'+j).html(details[i].optionText);
            CKEDITOR.instances['optionText'+j].setData(details[i].optionText);
            $("#option"+j).prop("checked",details[i].correctOption);
            if(details[i].imgName != undefined && details[i].imgName != "") {
                optionImgSrc = "/polls/getPollsImages?resId="+polls.resId+"&fileName="+details[i].imgName+"&pollsId="+details[i].pollsId+"&imgType=options";
                $('#poll-option'+j+'-img').hide();
                $("#poll-option"+j+"-img-div").show();
                $("#poll-option"+j+"-img-span").attr('src', optionImgSrc);
                $("#poll-option"+j+"-img-span").attr('alt', details[i].imgName);
                $(".poll-option"+j+"-img-file").css("display", "none");
            }else {
                $('#poll-option'+j+'-img').show();
                $("#poll-option"+j+"-img-div").hide();
                $("#poll-option"+j+"-img-span").attr('src', '');
                $("#poll-option"+j+"-img-span").attr('alt', '');
                $(".poll-option"+j+"-img-file").css("display", "flex");
            }
            j++;
        }
        currentPollsId = polls.id;
    }

    function fileChanged(key,id) {
        var img = document.getElementById(id).files[0];
        var filename = document.getElementById(id).files[0].name;
        if(filename.search(" ")>=0){
            alert("Image name must not contain any space.");
            return;
        }
       if (img && img.size > 2097152){
           document.getElementById(id).value = "";
           alert("Select image less than 2 mb ");
           $('#'+id+"-filename").text("");
        }else if (img.type != "image/jpg" && img.type != "image/jpeg" && img.type != "image/png" && img.type != "image/svg" && img.type != "image/gif"){
           document.getElementById(id).value = "";
           alert("Please upload image formats only.");
           $('#'+id+"-filename").text("");
       } else if (img.size < 2097152){
            fileObj[''+key] = img;
            var reader = new FileReader();
            reader.onload = function(e) {
                // var htmlStr = "<img style='width: 100%' id='ans-preview-modal' src=\"#\" class=\"mt-3\">";
                // $('#image-modal-body').html(htmlStr);
                // $('#ans-image-preview_'+index).attr('src', e.target.result);
                // $('#ans-preview-modal').attr('src', e.target.result);
                // $(".ans-modal-img-remove").html("");
                // $(" <button id='attached-ans-img-cancel-btn' type='button' class=\"btn ans-modal-img-remove cancels\" onclick=\"javascript:removeSelectedAnsImg("+index+");\">Remove Image</button>\n" ).insertBefore("#image-modal-close");
            };
            reader.readAsDataURL(img);
           $('#'+id+"-filename").text(filename);
        }
    }

    function savePollImages(pollsId) {
        var valid = false;
        var formData = new FormData();
        if(fileObj['pollImg'] != undefined && fileObj['pollImg'] != null) {
            valid = true;
            formData.append('file', fileObj['pollImg']);
        }
        if(fileObj['option1'] != undefined && fileObj['option1'] != null) {
            valid = true;
            formData.append('option1', fileObj['option1']);
        }
        if(fileObj['option2'] != undefined && fileObj['option2'] != null) {
            valid = true;
            formData.append('option2', fileObj['option2']);
        }
        if(fileObj['option3'] != undefined && fileObj['option3'] != null) {
            valid = true;
            formData.append('option3', fileObj['option3']);
        }
        if(fileObj['option4'] != undefined && fileObj['option4'] != null) {
            valid = true;
            formData.append('option4', fileObj['option4']);
        }
        if(valid){
            $.ajax({
                type:'POST',
                url: '../polls/savePollsImages?id='+resId+'&pollsId='+pollsId,
                data:formData,
                cache:false,
                contentType: false,
                processData: false,
                success:function(data){
                    $('.loading-icon').addClass('hidden');
                    fileObj={};
                },
                error: function(data){
                    $('.loading-icon').addClass('hidden');
                    console.log("error");
                    fileObj={};
                    console.log(data);
                }
            });
        }else $('.loading-icon').addClass('hidden');
    }

    function removeImgSpan(id) {
        $("#"+id+"-span").text("");
        $('#'+id+"-div").hide();
        $('#'+id).show();
        $('.'+id+"-file").css("display", "flex");
        $('#'+id+"-filename").text("");
    }

    function deletePolling(rollsId) {
        swal({
                title: "Are you sure?",
                text: "Once deleted, you will not be able to recover this Poll.",
                type: "warning",
                allowOutsideClick: true,
                showConfirmButton: true,
                showCancelButton: true,
                confirmButtonColor: "#F83939",
                confirmButtonText: "Yes, delete it!",
                cancelButtonText: "Cancel",
                closeOnConfirm: false,
                closeOnCancel: true,
                allowEscapeKey: true,
                customClass: '',
            },
            function(isConfirm){
                if (isConfirm) {
                    swal({
                        title: "Deleted",
                        text: "Poll deleted successfully!",
                        type: "success",
                        confirmButtonColor: "#231F20",
                        allowEscapeKey: false,
                    }, function() {
                        <g:remoteFunction controller="polls" action="deletePolls" params="'id='+rollsId" onSuccess = 'getpollsList()'/>
                    });
                }
            });
    }

    $("#add-poll-modal").on('hidden.bs.modal', function(){
        currentPollsId = 0;
        fileObj={};
    });

    $('.option-checkbox').on('change', function() {
        $('.option-checkbox').not(this).prop('checked', false);
    });

    getpollsList();

    function openImagePopup(img) {
        var originalImg = document.getElementById("originalImage");
        var imgCaption = document.getElementById("imgCaption");
        originalImg.src = img.src;
        imgCaption.innerHTML = img.alt;
        $("#imgPopupModal").modal("show");
    }

    $('#imgPopupModal').on('hidden.bs.modal', function (e) {
        $('body').addClass('modal-open');
    });

</script>
