<%@ page import="com.wonderslate.usermanagement.User; com.wonderslate.data.ResourceType" %>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title><%= title!=null?title:"All-India Online Test Series and Best books for CBSE | ICSE\n" +
        "ISC | NCERT | CUET | JEE | Olympiads | Competitive\n" +
        "Exams"%></title>
    <%if(seoDesc!=null){%>
    <meta name="description" content="${seoDesc}">
    <%}else{%>
        <% if(description!="" && description!=null){%>
            <meta name="description" content="${description}">
        <%}else{%>
            <meta name="description" content="Buy best school and competitive Exams books and online test series. Best way to prepare for your exams. Find the books from  MTG, Oswaal,Educart, Nirali prakashan and many more.">
        <%}%>

    <%}%>
    <%if(keywords!=null){%>
    <meta name="keywords" content="${keywords}"/>
    <%}else{%>
    <meta name="keywords" content="CBSE Books, CBSE Question Bank, CBSE Sample Paper, CBSE Previous Years Solved Paper, ICSE Books, ICSE Question Bank, ICSE Sample Paper, ICSE Previous Years Solved Paper, ISC Books, ISC Question Bank, ISC Sample Paper, ISC Previous Years Solved Paper, NCERT Books, NCERT Question Bank, CUET Books, CUET Question Bank, CUET Sample Paper, CUET Previous Years Solved Paper, JEE Main Books, JEE Main Sample Paper, JEE Main Previous Years Solved Paper, JEE Advance books, JEE Advance Sample Paper, JEE Advance Previous Years Solved Paper, NEET books, NEET Question Bank, NEET Sample Paper, NEET Previous Years Solved Paper, GATE books, GATE Previous Years Solved Paper, UPSC Books, UPSC Sample Paper, UPSC Previous Years Solved Paper, UGC NET books, UGC NET Question Bank, UGC NET Previous Years Solved Paper, CLAT Books, CLAT Sample Paper, CAT Books, CAT Question Bank, CAT Sample Paper, Olympiad Books, Olympiad Previous Years Solved Paper, Karnataka SSLC books, Karnataka SSLC Question Bank, Karnataka SSLC Sample Paper, Karnataka SSLC Previous Years Solved Paper, Karnataka PUC books, Karnataka PUC Question Bank, Karnataka PUC Sample Paper, Karnataka PUC Previous Years Solved Paper">
   <%}%>
    <link rel="icon"  href="${assetPath(src: 'landingpageImages/favicon.ico')}" type="image/x-icon">
    <link rel="android-touch-icon" href="${assetPath(src: 'landingpageImages/wsmetalogo.png')}"/>
    <link rel="windows-touch-icon" href="icon.png" />
    <meta name="facebook-domain-verification" content="b1ndgtx0vj7jgzgmkhl612wuekczgd" />
    <meta name="theme-color" content="#6F58D8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=2.0" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Quicksand:wght@300;400;500;600;700&family=Poppins:wght@100;200;300;400;500;600;700;800;900&family=Rubik:wght@300;400;500;600;700;800;900&family=DM+Sans:wght@400;500;700&display=swap" rel="stylesheet" async>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Material+Icons|Material+Icons+Outlined|Material+Icons+Two+Tone|Material+Icons+Round|Material+Icons+Sharp&display=swap" async>

    <!-- Minified CSS -->
    <asset:stylesheet href="wonderslate/vendors.min.css" async="true" media="all"/>
    <asset:stylesheet href="wonderslate/headerws.css" async="true" media="all"/>
    <asset:stylesheet href="landingpage/homepageStyle.css" async="true" data-role="baseline" media="all"/>
    <%if("true".equals(commonTemplate)){%>
    <asset:stylesheet href="wonderslate/wsTemplate.css" async="true" media="all"/>
    <% }%>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" referrerpolicy="no-referrer" async/>

    <!-- General styles for admin & user pages -->
    <asset:stylesheet href="wonderslate/ws_general.css" async="true" media="all"/>

    <!-- Footer CSS -->
    <asset:stylesheet href="wonderslate/footer.css" async="true" media="all"/>

    <script>
        function encodeString(String){
            return(btoa(String))
        }
        function decodeString(String){
            return(atob(String))
        }
        function unicodeToChar(text) {
            return text.replace(/\\u[\dA-F]{4}/gi,
                function (match) {
                    return String.fromCharCode(parseInt(match.replace(/\\u/g, ''), 16));
                });
        }
        function htmlDecode( html ) {
            var a = document.createElement( 'a' ); a.innerHTML = html;
            return a.textContent;
        };
    </script>
    <style>
    .mobile-back-button{
        display: none;
    }
    </style>
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-2Y9D9ELSN2" defer></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'G-2Y9D9ELSN2');
    </script>
    <!--end of tag maneger-->
</head>

<body class="wonderslate_main">

<g:render template="/books/signIn"></g:render>

<!-- Header -->
<%if(showHeader==null||"true".equals(showHeader)){%>
<%if(params.tokenId==null&&session["appType"]==null){%>
<div class="back_to_top mdl-js">
    <button type="button" onclick="javascript:backToTop();" id="goTopBtn" class="btn btn-shadow border-0 d-flex justify-content-center align-items-center mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect">
        <i class="material-icons-round">north</i>
    </button>
</div>

<div class="mega_menu__wrapper shadow">
    <button type="button" class="btn btn-sm btn-warning btn-warning-modifier btn-shadow border-0 d-flex justify-content-center align-items-center mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect mega_menu__close_mob d-md-none">Close</button>
    <div id="mega_menu__mainNavbar" class="d-md-none explored"></div>
    <div class="mega_menu__mange_rows position-relative">
        <div class="row m-0">
            <div class="card-columns w-100" id="topMenuItems"></div>
        </div>
    </div>
</div>
<div class="mega_menu__overlay_bg"></div>

<div class="mdl-js-layout">
    <header class="navbar navbar-expand navbar-header container-fluid bg-white px-3 px-xl-5 <%if(session.getAttribute("publisherLogo")!=null && session.getAttribute("publisherLogo")!=""){%>active-publisher<%}%>">
        <div class="navbar-hamburger d-block d-lg-none">
            <button class="mega_menu__hamburger_btn" style="background: transparent;border: none">
                <i class="material-icons-round mega_menu__icon">menu</i>
                <i class="material-icons-round mega_menu__close d-none d-md-block">highlight_off</i>
            </button>
        </div>
        <button class="mobile-back-button d-lg-none" onclick="javascript:history.back();">
            <img loading="lazy" src="${assetPath(src: 'ws/icon-back-arrow-white.svg')}" class="d-lg-none" alt="Back Arrow"> Back
        </button>
        <%if(!"true".equals(session["appInApp"])){%>
        <sec:ifNotLoggedIn>
        <a class="navbar-brand" href="/">
        </sec:ifNotLoggedIn>
        <sec:ifLoggedIn>
        <a class="navbar-brand d-none d-md-block" href="/">
        </sec:ifLoggedIn>
        <img loading="lazy" src="${assetPath(src: 'wonderslate/wonderslate-new-logo.webp')}" width="180" height="47" class="d-none d-md-block" alt="Wonderslate Logo">
        <img loading="lazy" src="${assetPath(src: 'wonderslate/mobile-ws-logo.webp')}" width="30" height="30" class="d-md-none" alt="Wonderslate Logo Icon">
        </a>

        <div class="notify_icon mt-3 ml-3 d-none">
            <a href="#">
                <i class="material-icons-round">notifications_active</i>
                <small>3</small>
            </a>
        </div>
        <ul id="mainNavbar" class="navbar-nav">
            <sec:ifNotLoggedIn>
                <li class="nav-item">
                    <a href="javascript:loginOpen();" class="nav-link" id="myhomeref">My Books</a>
                </li>
            </sec:ifNotLoggedIn>
            <sec:ifLoggedIn>
                <%if(session.getAttribute('instituteLogo')!=null){%>
                <li class="nav-item">
                    <a href="/wsLibrary/myLibrary?instituteId=${session.getAttribute('instituteId')}" class="nav-link">My Home</a>
                </li>
                <%}else{%>
                <li class="nav-item">
                    <a href="/wsLibrary/myLibrary" class="nav-link">My Books</a>
                </li>
                <%}%>


                <sec:ifAnyGranted roles="ROLE_WS_CONTENT_ADMIN,ROLE_BOOK_CREATOR,ROLE_WS_CONTENT_ADMIN,ROLE_FINANCE,ROLE_AFFILIATION_SALES,ROLE_INSTITUTE_ADMIN,
ROLE_INSTITUTE_REPORT_MANAGER,ROLE_LIBRARY_USER_UPLOADER,ROLE_INFORMATION_ADMIN,ROLE_CUSTOMER_SUPPORT,ROLE_WS_SALES_TEAM,ROLE_WS_GROUP_ADMIN">
                    <li class="nav-item">
                        <a href="/books/home" class="nav-link">Admin</a>
                    </li>
                </sec:ifAnyGranted>
                <sec:ifAllGranted roles="ROLE_INSTITUTE_MANAGER">
                    <%if(session["instituteManagerInstituteId"]!=null){%>
                    <li class="nav-item">
                        <a href="/instManager/adminDashboard?instituteId=${session["instituteManagerInstituteId"]}" class="nav-link">Manage Institute</a>
                    </li>
                    <%}%>
                </sec:ifAllGranted>
                <sec:ifAllGranted roles="ROLE_IBOOKGPT_SITE_ADMIN">
                    <li class="nav-item">
                        <a href="/instManager/listInstitutes" class="nav-link">Manage Institutes</a>
                    </li>
                </sec:ifAllGranted>
                <sec:ifAllGranted roles="ROLE_CLIENT_ORDER_MANAGER">
                    <li class="nav-item d-none d-md-block">
                        <a href="/wsshop/orderManagement" class="nav-link">Order Management</a>
                    </li>
                </sec:ifAllGranted>
                <sec:ifAllGranted roles="ROLE_WS_CONTENT_ADMIN">
                    <li class="nav-item">
                        <a href="/publishing-desk" class="nav-link">Publishing Desk</a>
                    </li>
                </sec:ifAllGranted>
                <%if(session["userdetails"]!=null&&session["userdetails"].affliationCd!=null){%>
                <li class="nav-item">
                    <a href="/partner" class="nav-link">Affiliation</a>
                </li>
                <%}%>

            </sec:ifLoggedIn>
            <li class="nav-item">
                <a href="/ebooks" class="nav-link">Store</a>
            </li>

            <sec:ifLoggedIn>
                <li class="nav-item">
                    <a href="/usermanagement/orders" class="nav-link">Orders History</a>
                </li>
            </sec:ifLoggedIn>

            <li class="nav-item">
                <a href="/current-affairs" class="nav-link">Current Affairs</a>
            </li>

        </ul>
        <ul class="navbar-nav ml-auto" id="signupnav">
            <%if(session["userdetails"]==null||session["userdetails"].username.indexOf("1_cookie_")==0){%>
            <li class="nav-item pr-0">
                <a href="javascript:loginOpen();" class="nav-link login-menu-link">
                    Login
                </a>
            </li>
            <li class="nav-item d-none d-md-block">
                <a href="javascript:signupModal();" class="btn nav-link register-menu-btn shadow-sm">
                    Register for Free
                </a>
            </li>
            <%}%>
        </ul>
        <div class="navbar-search ebooks d-none">
            <div class="d-flex justify-content-center align-items-center global-search">
                <form class="form-inline rounded rounded-modifier col-12 p-0">
                    <i class="material-icons-round">search</i>
                    <input type="text" class="form-control form-control-modifier border-0 typeahead w-100" name="search" id="search-book-header" autocomplete="off" placeholder="Search title, subject, author, ISBN, language etc.">
                </form>
            </div>
        </div>

        <div class="navbar_cart px-2 px-md-1 px-lg-2">
                <a href="/wsshop/cart" class="mobile_cart_icon"><i class="material-icons-outlined">shopping_cart</i><span class="cart_count" id="navbarCartCount">0</span></a>
        </div>

        <div class="navbar_search_trigger px-1 px-lg-2">
            <a href="javascript:showSearchForm();" class="d-block" style="padding: 7px"><i class="material-icons-round search-open-icon">search</i><i class="material-icons-round search-close-icon d-none">close</i></a>
        </div>

        <div class="navbar-whatsapp-link">
            <a href="https://wa.me/918088443860" target="_blank" data-toggle="tooltip" title="Chat with us" class="btn d-flex whatsapp-btn pl-0" style="padding: 7px">
                <i class="fab fa-whatsapp"></i>
            </a>
        </div>

        <div class="navbar-user d-flex align-items-center">
            <sec:ifLoggedIn>
                <div class="dropdown dropdown-user">
                    <a class="dropdown-toggle dropdown-user-link" href="#" data-toggle="dropdown" aria-expanded="false">
                        <span class="avatar rounded-circle">
                            <%if(session['userdetails']!=null&&session['userdetails'].username!=null&&session['userdetails'].profilepic!=null){%>
                            <img loading="lazy" src="/funlearn/showProfileImage?id=${session['userdetails'].id}&fileName=${session['userdetails'].profilepic}&type=user&imgType=passport" width="40" height="40" class="rounded-circle" alt="User Avatar Image">
                            <%} else { %> <img loading="lazy" src="${assetPath(src: 'wonderslate/avatar.webp')}" width="40" height="40" alt="" class="rounded-circle" align="Avatar Image">
                            <%}%>
                        </span>
                    </a>
                    <div class="dropdown-menu dropdown-menu-right p-3">
                        <span class="username mb-2 d-block">Hello, <strong><%= session["userdetails"]!=null?session["userdetails"].name:"" %>!</strong></span>
                        <a class="dropdown-item border-top pl-0" href="/usermanagement/editprofile"><i class="material-icons-round mr-2">person_outline</i> Edit Profile</a>
                        <a class="dropdown-item pl-0" href="javascript:logout();"><i class="material-icons-round mr-2">power_settings_new</i> Logout</a>
                    </div>
                </div>
            </sec:ifLoggedIn>
        </div>

        <div class="mobile_menu d-none">
            <button style="background: transparent;border: none" class="d-flex align-items-center mobile_navigation__btn pl-2">
                Menu
                <span class="menu_line">
                    <span class="first_line"></span>
                    <span class="second_line"></span>
                </span>
            </button>
        </div>

        <%}%>
    </header>
    <div class="headerCategoriesMenu">
        <div class="header__categories">
            <ul class="header__categories-list" id="catList"></ul>
        </div>
        <div class="header__submenus" id="headerCategorySubMenus">
            <p class="text-center subMenuTitleText"><strong id="subMenuTitle"></strong></p>
            <div class="submenuLists">
                <ul class="syllabusList"></ul>
                <div class="listScrollArrow">
                    <div class="listScrollArrowBall"></div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="modal-backdrop fade show headerBackdrop d-none" id="categoryMenuBackdrop"></div>
<%}%>
<%}%>
<script>
    var activeCategories = [];
    if("${session["activeCategories_"+session["siteId"]]}" != "") {
        activeCategories = JSON.parse("${session["activeCategories_"+session["siteId"]]}".replace(/&quot;/g, '"').replaceAll('&#92;u0026', '&'));
    }
    if ('${session["activeCategories_"+session["siteId"]]}'){
        var levelTags = JSON.parse("${session["activeCategories_"+session["siteId"]]}".replace(/&quot;/g, '"').replaceAll('&#92;u0026', '&'));
        var syllabusTags = JSON.parse("${session["activeCategoriesSyllabus_"+session["siteId"]]}".replace(/&quot;/g, '"').replaceAll('&#92;u0026', '&'));
    }else{
        var levelTags = "";
        var syllabusTags = "";
    }


    function showSearchForm() {
        $("body").toggleClass('showing-search-form');
        $(".search-open-icon,.search-close-icon,header .navbar-search").toggleClass("d-none");
        if($("body").hasClass("showing-search-form")) {
            $("#search-book-header").focus();
        }
    }

    $(window).scroll(function () {
        if ( $(this).scrollTop() > 300 && !$('body').hasClass('fixed-navbar') ) {
            $('body').addClass('fixed-navbar');
        } else if ( $(this).scrollTop() <= 300 ) {
            $('body').removeClass('fixed-navbar');
        }
    });

    $(".mega_menu__hamburger_btn").click(function(){
        $(".navbar-hamburger").toggleClass("menu-actives");
        $(".mega_menu__overlay_bg").toggleClass("active");
        $(".mega_menu__wrapper").toggleClass("menu-showing");
        if ($(window).width() < 767) {
            $("body").css({
                'overflow-y':'hidden'
            });
        }
    });

    $(".mega_menu__overlay_bg,.mega_menu__close_mob").click(function(){
        $(".navbar-hamburger").removeClass("menu-actives");
        $(".mega_menu__overlay_bg").removeClass("active");
        $(".mega_menu__wrapper").removeClass("menu-showing");
        if ($(window).width() < 767) {
            $("body").css({
                'overflow-y':'unset'
            });
        }
    });

    $(".mobile_navigation__btn").click(function() {
        $("#mainNavbar").toggleClass("mobile-menu-showing").removeClass("d-none");
        $(this).toggleClass("active");
    });

    function backToTop() {
        $('html, body').animate({scrollTop:0}, '300');
    }

    function logout(){
        window.location.href = '/logoff';
    }

    function exploreCategories() {
        $("#topMenuItems").toggleClass("hidden_categories");
        $("#mega_menu__mainNavbar").toggleClass("explored");
    }

    function updateHeaderCategories(){
        if (!levelTags){
            document.querySelector('.header__categories').style.display = 'none';
            return;
        }
        let catItems="";
        const catList = levelTags;
        const catListElement = document.getElementById('catList');

        catList.forEach((cat,index)=>{
            const catName = cat.level.replaceAll(' ','-');
            catItems += "<li class='header__categories-list__item'>" +
                            "<a href='/books/store?level="+catName+"' class='headerLevel' target='_blank'>"+cat.level+"</a>"+
                        "</li>";
        });
        catListElement.innerHTML = catItems;
        const hoverElement = document.querySelectorAll('.headerLevel');
        const showDiv = document.getElementById('headerCategorySubMenus');
        const handleMouseLeave = () => {
            addRemoveBackDrop('hide',showDiv);
        };
        hoverElement.forEach(elem=>{
            elem.addEventListener('mouseover', () => {
                updateSubMenus(elem.textContent)
                addRemoveBackDrop('show',showDiv);
            });
            showDiv.addEventListener('mouseout', handleMouseLeave);
            showDiv.addEventListener('mouseover', ()=>{
                addRemoveBackDrop('show',showDiv);
            });
            document.querySelector('header').addEventListener('mouseover',handleMouseLeave);
            document.addEventListener('click',handleMouseLeave);
        })
    }
    function addRemoveBackDrop(action,showDiv){
        const categoryMenuBackdrop = document.getElementById('categoryMenuBackdrop');
        if(action=='show'){
            showDiv.style.display = 'block';
            showDiv.style.opacity = '1';
            categoryMenuBackdrop.classList.remove('d-none');
        }else if(action=='hide'){
            showDiv.style.display = 'none';
            showDiv.style.opacity = '0';
            categoryMenuBackdrop.classList.add('d-none');
        }
    }
    function updateSubMenus(hoveredText){
        let syllabusListHTML = "";
        const syllabusListDiv =  document.querySelector('.syllabusList');
        const headerCategorySubMenus = document.getElementById('headerCategorySubMenus');
        document.getElementById('subMenuTitle').innerHTML = hoveredText;
        const listScrollArrow = document.querySelector('.listScrollArrow');
        let syllabusCount = 0;
        syllabusTags.forEach(syllabus=>{
            if (syllabus.level === hoveredText){
                let syllabusLink = syllabus.level.replaceAll(" ",'-');
                syllabusLink += "&syllabus="+ syllabus.syllabus.replaceAll(" ",'-');
                syllabusListHTML += "<li><a href='/books/store?level="+syllabusLink+"' target='_blank'>"+syllabus.syllabus+"</a></li>";
                syllabusCount++;
            }
        })
        syllabusListDiv.innerHTML = syllabusListHTML;
        setTimeout(()=>{
            if (headerCategorySubMenus.offsetHeight >= 500 && syllabusCount >= 30){
                listScrollArrow.style.display = 'flex';
            }else{
                listScrollArrow.style.display = 'none';
            }
        })
    }

    updateHeaderCategories();
    $(document).ready(function () {
        if (!levelTags){
            return;
        }
        var divContent = $("#mainNavbar").html();
        let accordionHTMl = "";
        let accID = "";
        accordionHTMl +="<div class='accordion' id='accordion'>";
        levelTags.forEach((cat,index)=>{
            accID = index;
            accordionHTMl += "<div class='card mb-3'>" +
                "<div class='card-header p-2' id='heading-"+index+"'>";
                    if(index==0){
                        accordionHTMl +="<button class='text-dark btn btn-link w-100 d-flex justify-content-between pl-0' data-toggle='collapse' data-target='#collapse-"+index+"' aria-expanded='true' aria-controls='#collapse-"+index+"'>";
                    }else{
                        accordionHTMl +="<button class='text-dark btn btn-link w-100 d-flex justify-content-between pl-0 collapsed' data-toggle='collapse' data-target='#collapse-"+index+"' aria-expanded='true' aria-controls='#collapse-"+index+"'>";
                    }
                    accordionHTMl +="<a href='/books/store?level="+cat.level.replaceAll(" ",'-')+"' target='_blank'><p>"+cat.level+"</p></a>"+
                    "</button>" +
                "</div>";
            if (index==0){
                accordionHTMl +="<div id='collapse-"+accID+"' class='collapse show' aria-labelledby='heading-"+accID+"' data-parent='#accordion'>";
            }else{
                accordionHTMl +="<div id='collapse-"+accID+"' class='collapse' aria-labelledby='heading-"+accID+"' data-parent='#accordion'>";
            }
            accordionHTMl +="<div class='card-body'>";
            syllabusTags.forEach((syllabus,index)=>{
                if (cat.level === syllabus.level){
                    accordionHTMl +="<a href='/books/store?level="+cat.level+"&"+ "&syllabus="+ syllabus.syllabus.replaceAll(" ",'-')+"'  target='_blank'><p>"+syllabus.syllabus+"</p></a>";
                }
            })
            accordionHTMl +="</div>"+
            "</div>"+
           "</div>";
        })
        accordionHTMl+="</div>";
        if ($(window).width() < 767) {
            document.getElementById("mega_menu__mainNavbar").innerHTML = "<div class='card border-0 mega_menu__links'>" +
                "<ul class='list-inline p-0'>"+divContent+"</ul>" +
                accordionHTMl+
                "</div>";
        }
    });
</script>
