<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />
<style>
.datepicker table {
    border-collapse: unset;
}
.datepicker .datepicker-days td, .datepicker .datepicker-days th {
    width: 25px !important;
    height: 25px !important;
}
</style>

<div class="book_details_info">
    <div class="d-flex align-items-center flex-wrap mb-3">
        <h5 class="create-book-title mb-2 mb-md-0">
            <strong>GPT Manager</strong>
            <a href="/autogpt/index?bookId=${params.bookId}" class="btn btn-primary ml-0 ml-md-3" target="_blank"> AutoGPT</a>
            <a href="/excel/exportmcqspage?bookId=${params.bookId}" class="btn btn-primary ml-0 ml-md-3" target="_blank">Export MCQs</a>
            <a href="/questionPaper/listPatterns?bookId=${params.bookId}" class="btn btn-primary ml-0 ml-md-3" target="_blank">Question Paper Pattern</a>

        </h5>
    </div>


    <div class="row">
        <%boolean showNotification=false,showSnapshot=false,showMcq=false,showQa=false, showPdf = true, enableToken = false, enableCompiler = false
        if(booksMst !=null&&"Yes".equals(booksMst.showNotification)) showNotification = true
        if(booksMst !=null&&"true".equals(booksMst.showSnapshot)) showSnapshot = true
        if(booksMst !=null&&"true".equals(booksMst.showMcq)) showMcq = true
        if(booksMst !=null&&"true".equals(booksMst.showQa)) showQa = true
        if(booksMst !=null&&"false".equals(booksMst.showPdf)) showPdf = false
        if(booksMst !=null&&"true".equals(booksMst.enableToken)) enableToken = true
        if(booksMst !=null&&"true".equals(booksMst.enableCompiler)) enableCompiler = true
        %>
        <div class="col-6 col-md-2">
            <div class="form-group">
                <label for="showNotification" style="height: auto !important;">Show Notification</label>
                <label class="clickable-label">
                    <input type="radio" name="showNotification" id="showNotification" value="Yes" <%=showNotification?"checked":"" %> onblur="javascript:bookDtlUpdate(this);">Yes
                </label>
                <label class="clickable-label">
                    <input type="radio" name="showNotification" value="No" <%=!showNotification?"checked":"" %> onblur="javascript:bookDtlUpdate(this);">No
                </label>
            </div>
        </div>
           <div class="col-6 col-md-2">
                <div class="form-group">
                    <label for="showSnapshot" style="height: auto !important;">Show Snapshot</label>
                    <label class="clickable-label">
                        <input type="radio" name="showSnapshot" id="showSnapshot" value="true" <%=showSnapshot?"checked":"" %> onblur="javascript:bookDtlUpdate(this);">Yes
                    </label>
                    <label class="clickable-label">
                        <input type="radio" name="showSnapshot" value="false" <%=!showSnapshot?"checked":"" %> onblur="javascript:bookDtlUpdate(this);">No
                    </label>
                </div>
            </div>
            <div class="col-6 col-md-2">
                <div class="form-group">
                    <label for="showMcq" style="height: auto !important;">Show MCQ</label>
                    <label class="clickable-label">
                        <input type="radio" name="showMcq" id="showMcq" value="true" <%=showMcq?"checked":"" %> onblur="javascript:bookDtlUpdate(this);">Yes
                    </label>
                    <label class="clickable-label">
                        <input type="radio" name="showMcq" value="false" <%=!showMcq?"checked":"" %> onblur="javascript:bookDtlUpdate(this);">No
                    </label>
                </div>
            </div>
            <div class="col-6 col-md-2">
                <div class="form-group">
                    <label for="showQa" style="height: auto !important;">Show QA</label>
                    <label class="clickable-label">
                        <input type="radio" name="showQa" id="showQa" value="true" <%=showQa?"checked":"" %> onblur="javascript:bookDtlUpdate(this);">Yes
                    </label>
                    <label class="clickable-label">
                        <input type="radio" name="showQa" value="false" <%=!showQa?"checked":"" %> onblur="javascript:bookDtlUpdate(this);">No
                    </label>
                </div>
            </div>

            <div class="col-6 col-md-2">
                <div class="form-group">
                    <label for="showQa" style="height: auto !important;">Show PDF</label>
                    <label class="clickable-label">
                        <input type="radio" name="showPdf" id="showPdf" value="true" <%=showPdf?"checked":"" %> onblur="javascript:bookDtlUpdate(this);">Yes
                    </label>
                    <label class="clickable-label">
                        <input type="radio" name="showPdf" value="false" <%=!showPdf?"checked":"" %> onblur="javascript:bookDtlUpdate(this);">No
                    </label>
                </div>
            </div>

            <div class="col-6 col-md-2">
                <div class="form-group">
                    <label for="enableToken" style="height: auto !important;">Enable Token</label>
                    <label class="clickable-label">
                        <input type="radio" name="enableToken" id="enableToken" value="true" <%=enableToken?"checked":"" %> onblur="javascript:bookDtlUpdate(this);">Yes
                    </label>
                    <label class="clickable-label">
                        <input type="radio" name="enableToken" value="false" <%=!enableToken?"checked":"" %> onblur="javascript:bookDtlUpdate(this);">No
                    </label>
                </div>
            </div>

            <div class="col-6 col-md-2">
                <div class="form-group">
                    <label for="enableCompiler" style="height: auto !important;">Enable Compiler</label>
                    <label class="clickable-label">
                        <input type="radio" name="enableCompiler" id="enableCompiler" value="true" <%=enableCompiler?"checked":"" %> onblur="javascript:bookDtlUpdate(this);">Yes
                    </label>
                    <label class="clickable-label">
                        <input type="radio" name="enableCompiler" value="false" <%=!enableCompiler?"checked":"" %> onblur="javascript:bookDtlUpdate(this);">No
                    </label>
                </div>
            </div>

        <div class="col-12 col-md-12">
            <div class="row align-items-center publisher-select">
                <div class="form-group col-6">
                    <label>Chapters</label>
                    <g:select  class="form-control" id="chapterId" name="chapterId" from="${chaptersMst}" optionKey="id" optionValue="name"
                               title="Select Chapter"  noSelection="['':'Select']" onChange="getChapterGPTDetails()"/>
                </div>
                <div id="managePage" class="col-6" style="gap: 20px"></div>
                <div class="invalid-feedback col" id="publisher-error" style="display: none;">Please select publisher.</div>
            </div>
        </div>
     </div>
    <div class="row">
        <div class="col-12 col-md-7">
        </div>
    </div>


    <div class="row mx-0 mb-3" style="overflow-x: scroll;" id="gptResources">

    </div>
</div>

<script>
    function getChapterGPTDetails(){
        $('.loading-icon').removeClass('hidden');
        var chapterId = document.getElementById("chapterId")[document.getElementById("chapterId").selectedIndex].value
        <g:remoteFunction controller="prompt" action="getChapterGPTResources" params="'chapterId='+chapterId" onSuccess="displayGPTResources(data)"></g:remoteFunction>
    }

    function deleteGPTResource(resId){
         if(confirm("Are you sure you want to delete this GPT resource?")){
             $('.loading-icon').removeClass('hidden');
            <g:remoteFunction controller="prompt" action="deleteGPTResource" params="'resId='+resId" onSuccess="getChapterGPTDetails()"></g:remoteFunction>
        }
    }

    function displayGPTResources(data){
        var gptResources =  data.resources;
        var htmlStr = "No GPT results are created yet."
        if(gptResources.length==0){
            document.getElementById("gptResources").innerHTML=htmlStr;
        }else{
            htmlStr = " <table class='table table-bordered col-12 col-md-10' >";
            for(var i=0;i<gptResources.length;i++){
                htmlStr +="<tr><td>"+gptResources[i].resourceName+"</td>"+
                    "<td><a href='javascript:editGPTResource("+gptResources[i].id+")'>Edit</a></td>"+
                    "<td><a href='javascript:deleteGPTResource("+gptResources[i].id+")'>Delete</a></td>"+
                    "</tr>";
            }
            htmlStr +="</table>";
             document.getElementById("gptResources").innerHTML=htmlStr;
        }
        const readingMaterials = data.readingMaterials;
        if(readingMaterials.length>0){
            document.getElementById("managePage").innerHTML="<a href='/admin/bookgptPromptAdmin?bookId=${params.bookId}&chapterId="+readingMaterials[0].chapterId+"&resId="+readingMaterials[0].id+"' target='_blank'>GPT Manager</a>" +
                "&nbsp;&nbsp;<a href='/prompt/manualGpt?resId="+readingMaterials[0].id+"' target='_blank' style='margin-left;20px;'>Manual</a>"+
                "&nbsp;&nbsp;<a href='/prompt/bookgpt?bookId=${params.bookId}&chapterId="+readingMaterials[0].chapterId+"&resId="+readingMaterials[0].id+"' target='_blank' style='margin-left;20px;'>User view</a>"+
                "&nbsp;&nbsp;<a href='/prompt/showGptLogsForResId?resId="+readingMaterials[0].id+"' target='_blank' style='margin-left;20px;'>Logs</a>";

        }
        $('.loading-icon').addClass('hidden');
    }

    function editGPTResource(resId){
        var chapterId = document.getElementById("chapterId")[document.getElementById("chapterId").selectedIndex].value
        window.open("/wonderpublish/notescreator?page=notes&chapterId="+chapterId+"&bookId="+bookId+"&resourceType=Notes&useType=notes&mode=edit&id="+resId,'_blank');
    }
</script>
