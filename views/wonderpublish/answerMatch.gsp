<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader"></g:render>


<div class="container-fluid answer-match-container">

  <div class="row">
    <div class="col-md-12 answer-match-wrapper">
      <div class="answer-wrapper">
        <p class="label-answer">Ideal Answer</p>
        <textarea id="idealAnswer" name="idealAnswer" class="answer-input-textarea" rows="5" placeholder="Write the ideal answer here">My name is <PERSON>. I was born in Bangalore. I did my engineering in Mysore. I started my career in Novell India. I like the solving technical problems.</textarea>
      </div>

      <div class="answer-wrapper">
        <p class="label-answer">User Answer</p>
        <textarea id="userAnswer" name="userAnswer" class="answer-input-textarea" rows="5" placeholder="Write the user answer here"><PERSON> started his career in Novell. His is interested in hiking and swimming. He was born in Bangalore and did his engineering in Mysore.</textarea>
      </div>

      <div class="compare-btn-wrapper">
        <a href="javascript:submitForm();" class="answer-compare-btn waves-effect waves-ripple">Compare</a>
      </div>
      
      <p class="label-answer">Complete answer match</p>
      <div class="progress answer-match-progress-wrapper">
        <div class="progress-bar progress-bar-success answer-match-progress-success" id="similarityBar" role="progressbar" aria-valuenow="90" aria-valuemin="0" aria-valuemax="100" style="width: 0%">
        </div>
      </div>
      <div class="answer-match-accuracy" id="totalSimilarity">0%</div>

      <div class="show-more-accuracy-details" id="show-accuracy-table-wrapper" style="display: none;">
        <a class="show-accuracy-table" id="show-accuracy-table" role="button" data-toggle="collapse" href="#resultBlock" aria-expanded="false" aria-controls="resultBlock">Show More Details <i class="icon-chevron"></i></a>
      </div>

      <div id="resultBlock" class="collapse">
        <div class="answer-phrase-match-table-wrapper">
          <table class="table table-responsive answer-phrase-match-table">
            <thead>
              <tr>
                <th>Ideal answer phrases</th>
                <th>user answer phrases</th>
                <th>match %</th>
              </tr>
            </thead>
            <tbody id="resultTable"></tbody>
          </table>
        </div>
        <div class="answer-phrase-match-table-wrapper">
         <table class="table table-responsive answer-phrase-match-table">
           <thead>
             <tr>
               <th>Ignored Phrases</th>
             </tr>
           </thead>
           <tbody id="ignored"></tbody>
         </table>
       </div>
     </div>
   </div>
 </div>
</div>
<script>
function submitForm(){
  var idealAnswer = document.getElementById("idealAnswer").value;
  var userAnswer =  document.getElementById("userAnswer").value;
  idealAnswer = idealAnswer.replace(/[^a-zA-Z ]/g, "");
  userAnswer = userAnswer.replace(/[^a-zA-Z ]/g, "");
  console.log("userAnswer="+userAnswer);
  <g:remoteFunction controller="wonderpublish" action="evaluateAnswers"  onSuccess='processResults(data);' params="'idealAnswer='+idealAnswer+'&userAnswer='+userAnswer" />
}

function processResults(data){
  console.log("highScores="+data.highScores.length);
  var highScores=data.highScores;
  var closeAnswers=data.closeAnswers;
  var failSentences=data.failSentences;
  var idealAnswer=data.idealAnswer;
  var userAnswer=data.userAnswer;
  
  $('#show-accuracy-table-wrapper').show();

  var htmlStr="";
  for(i=0;i<idealAnswer.length;i++){
    htmlStr+=" <tr>" +
    "              <td>"+idealAnswer[i]+"</td>" +
    "              <td>"+closeAnswers[i]+"</td>" +
    "              <td>"+(highScores[i]*100).toFixed(2)+"%</td>\n" +
    "            </tr>"
  }
  document.getElementById("resultTable").innerHTML=htmlStr;
  document.getElementById("totalSimilarity").innerHTML=(data.totalSimilarity*100/idealAnswer.length).toFixed(2)+"%";
  document.getElementById("similarityBar").style.width=(data.totalSimilarity*100/idealAnswer.length).toFixed(2)+"%";
  
  $('#totalSimilarity').each(function () {
    $(this).prop('Counter',0).animate({
        Counter: $(this).text()
    }, {
        duration: 600,
        easing: 'swing',
        step: function (now) {
            $(this).text((now*100/100).toFixed(2)+"%");
        }
      });
  });

  htmlStr="";
  for(i=0;i<failSentences.length;i++){
    if(failSentences[i]==null||failSentences[i]=="null") continue;
    htmlStr+=" <tr>" +
    "              <td>"+failSentences[i]+"</td>" +
    "            </tr>"
  }
  document.getElementById("ignored").innerHTML=htmlStr;

}

$('#show-accuracy-table').click(function() {
  $(this).html($(this).html() == 'Show More Details <i class="icon-chevron"></i>' ? 'Hide More Details <i class="icon-chevron is-rotated"></i>' : 'Show More Details <i class="icon-chevron"></i>');
});

</script>



<g:render template="/${session['entryController']}/footer"></g:render>