<%@ page import="javax.servlet.http.Cookie" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/privatelabel/navheader_new"></g:render>

<style>
    .db-main{
        min-height: 70vh;
    }
</style>
<script>
    $('link[data-role="baseline"]').attr('href', '');
</script>

<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>

<section class="page-main-wrapper mdl-js pb-5 pt-4 db-main">

    <!-- General -->

    <!-- Publishing -->
    <%if(showPublisherControls || wsAdmin || showPublisherAdminControls || informationAdmin){%>
    <div class="container mt-4">
        <div class="db-section-title my-3">
            <h5 class="text-secondary text-secondary-modifier"><strong>Publishing</strong></h5>
        </div>
        <div class="card card-modifier card-shadow border-0 db-common-info db-general db-publish col-12 py-4 px-2 px-md-4">
            <div class="row mx-0">
                <%if(showPublisherControls || wsAdmin){%>
                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="/publishing-desk">
                        <asset:image src="/ws/icon-db-publishing-desk.svg"></asset:image>
                        <p>Publishing Desk</p>
                    </a>
                </div>
                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="/wonderpublish/manageTabs">
                        <asset:image src="/ws/icon-db-manage-tags.svg"></asset:image>
                        <p>Manage Tags</p>
                    </a>
                </div>
                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="/wonderpublish/manageExams">
                        <asset:image src="/ws/icon-db-manage-templates.svg"></asset:image>
                        <p>Manage MCQs Templates</p>
                    </a>
                </div>
                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="/wonderpublish/bannerManagement">
                        <asset:image src="/ws/icon-db-content-admin.svg"></asset:image>
                        <p>Banner Management</p>
                    </a>
                </div>
                <%}%>
                <sec:ifAllGranted roles="ROLE_WS_CONTENT_ADMIN">
                    <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                        <a href="/admin/priceList">
                            <asset:image src="/ws/icon-db-price-list.svg"></asset:image>
                            <p>Price List</p>
                        </a>
                    </div>
                    <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                        <a href="/admin/managePublishers">
                            <asset:image src="/ws/icon-db-publisher-management.svg"></asset:image>
                            <p>Publisher Management</p>
                        </a>
                    </div>
                    <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                        <a href="/excel/fileUploader">
                            <asset:image src="/ws/icon-db-price-list.svg"></asset:image>
                            <p>File Uploader</p>
                        </a>
                    </div>
                    <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                        <a href="/wonderpublish/quizcreatorbulkinput?page=notes&resSubType=Current Affairs&resourceType=Multiple Choice Questions&useType=quiz&mode=create">
                            <asset:image src="/ws/icon-db-info-admin.svg"></asset:image>
                            <p>Bulk Upload</p>
                        </a>
                    </div>
                </sec:ifAllGranted>
                <sec:ifAllGranted roles="ROLE_SUPPORT_MANAGER">
                    <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                        <a href="/support/addUser">
                            <asset:image src="/ws/icon-db-eclass-plus.svg"></asset:image>
                            <p>Demo User Management</p>
                        </a>
                    </div>
                </sec:ifAllGranted>
                <%if(wsAdmin){%>
                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="/log/quizissues">
                        <asset:image src="/ws/icon-db-quiz-issues.svg"></asset:image>
                        <p>Quiz Issues</p>
                    </a>
                </div>
                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="/discussionBoardAdmin">
                        <asset:image src="/ws/icon-db-doubts-admin.svg"></asset:image>
                        <p>Doubts Admin</p>
                    </a>
                </div>
                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="/admin/contentModeration">
                        <asset:image src="/ws/icon-db-content-admin.svg"></asset:image>
                        <p>Content Admin</p>
                    </a>
                </div>

                <%}%>

                <% if(informationAdmin){%>
                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="/admin/informationType">
                        <asset:image src="/ws/icon-db-info-admin.svg"></asset:image>
                        <p>Information Admin</p>
                    </a>
                </div>
                <%}%>

                <% if(wsAdmin){%>
                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="/publisherManagement/addPublisher?pubId=${session.getAttribute("userdetails").publisherId}">
                        <asset:image src="/ws/icon-db-publisher-avatar.svg"></asset:image>
                        <p>Publisher Profile</p>
                    </a>
                </div>
                <%}%>
                <%if(wsAdmin|| informationAdmin){%>
                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="/publishTests">
                        <asset:image src="/ws/icon-db-mcqs.svg"></asset:image>
                        <p>MCQs / Tests</p>
                    </a>
                </div>
                <%if(wsAdmin){%>
                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="/wsshop/manageShopSpecials">
                        <asset:image src="/ws/discount.png"></asset:image>
                        <p>Store Specials</p>
                    </a>
                </div>
                <%}}%>

                <sec:ifAnyGranted roles="ROLE_PDF_EXTRACTOR, ROLE_GPT_MANAGER">
                    <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                        <a href="/pdfExtractor/createSolution" target="_blank">
                            <asset:image src="/ws/icon-db-content-admin.svg"></asset:image>
                            <p>Create Solution</p>
                        </a>
                    </div>
                </sec:ifAnyGranted>
            </div>

        </div>
    </div>
    <%}%>
    <!-- Admin -->
    <%if(wsAdmin|| instituteAdmin || showPublisherAdminControls || salesSupport||libraryUserUploader||externalSalesAccess||libraryAdmin||masterLibraryAdmin){%>
    <div class="container mt-5">
        <div class="db-section-title my-3">
            <h5 class="text-secondary text-secondary-modifier"><strong>Admin Controls</strong></h5>
        </div>
        <div class="card card-modifier card-shadow border-0 db-common-info db-admin col-12 py-4 px-2 px-md-4">
            <div class="row mx-0">
                <%if(hasSalesAccess){%>
                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="/publishing-sales?reportType=printbooks">
                        <asset:image src="/ws/icon-db-sales.svg"></asset:image>
                        <p>Manage Print Orders</p>
                    </a>
                </div>
                <%}%>
                <%if(externalSalesAccess){%>
                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="/reports/externalReportInput">
                        <asset:image src="/ws/icon-db-sales.svg"></asset:image>
                        <p>Integration Sales</p>
                    </a>
                </div>
                <%}%>
                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="/privatelabel/pageManager">
                        <asset:image src="/ws/icon-db-manage-templates.svg"></asset:image>
                        <p>Page Manager</p>
                    </a>
                </div>
                <sec:ifAllGranted roles="ROLE_INSTITUTE_ADMIN">
                    <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                        <a href="/institute/admin">
                            <asset:image src="/ws/icon-db-eclass-plus.svg"></asset:image>
                            <p>eClass+</p>
                        </a>
                    </div>
                </sec:ifAllGranted>
                <%if(iBookGPTSiteAdmin){%>
                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="/instManager/listInstitutes">
                        <asset:image src="/ws/icon-db-library-management.svg"></asset:image>
                        <p>Institutes Management</p>
                    </a>
                </div>

                <%}%>
                <%if(wsAdmin||showPublisherAdminControls||masterLibraryAdmin){%>
                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="/institute/libAdmin">
                        <asset:image src="/ws/icon-db-library-management.svg"></asset:image>
                        <p>Library Management</p>
                    </a>
                </div>

                <%}%>
                <%if(wsAdmin||showPublisherAdminControls){%>
                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="/admin/discountManager">
                        <asset:image src="/ws/discount.png"></asset:image>
                        <p>Discount Manager</p>
                    </a>
                </div>
                <%}%>

                <%if(salesSupport){%>
                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="/books/directSales">
                        <asset:image src="/ws/icon-db-sales.svg"></asset:image>
                        <p>Direct Sales</p>
                    </a>
                </div>
                <%}%>
                <%if(libraryUserUploader){%>
                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="/institute/libraryUserUploader">
                        <asset:image src="/ws/icon-db-library-management.svg"></asset:image>
                        <p>Library User Management</p>
                    </a>
                </div>
                <%}%>
                <%if(libraryAdmin){%>
                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="/institute/userManagement">
                        <asset:image src="/ws/icon-db-library-management.svg"></asset:image>
                        <p>Library Management</p>
                    </a>
                </div>
                <%}%>

            </div>
        </div>
    </div>
    <%}%>
    <!-- Report -->
    <%if(showPublisherAdminControls || hasSalesAccess || wsAdmin||libraryAdmin||masterLibraryAdmin){%>
    <div class="container mt-5">
        <div class="db-section-title my-3">
            <h5 class="text-secondary text-secondary-modifier"><strong>Reports</strong></h5>
        </div>
        <div class="card card-modifier card-shadow border-0 db-common-info db-admin db-report col-12 py-4 px-2 px-md-4">
            <div class="row mx-0">
                <%if(wsAdmin||showPublisherAdminControls){%>
                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="/publisherManagement/publisherReport">
                        <asset:image src="/ws/icon-db-migrate-user.svg"></asset:image>
                        <p>Publisher Reports</p>
                    </a>
                </div>
                <%}%>
                <%if(hasSalesAccess){%>
                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="/publishing-sales">
                        <asset:image src="/ws/icon-db-sales.svg"></asset:image>
                        <p>Sales</p>
                    </a>
                </div>
                <%}%>
                <%if(wsAdmin||masterLibraryAdmin){%>
                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="/institute/usageReportInstituteAdmin">
                        <asset:image src="/ws/icon-db-sales.svg"></asset:image>
                        <p>Institute Report</p>
                    </a>
                </div>
                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="/sdk/sdkIntegrationReport">
                        <asset:image src="/ws/icon-db-sales.svg"></asset:image>
                        <p>Integration Report</p>
                    </a>
                </div>

                <%}%>
                <%if(libraryAdmin){%>
                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="/institute/usageReportInstituteAdmin">
                        <asset:image src="/ws/icon-db-sales.svg"></asset:image>
                        <p>Usage Statistics Report</p>
                    </a>
                </div>
                <%}%>
                <%if((wsAdmin||showPublisherAdminControls)&&"true".equals(session["showAccessCode"])){%>
                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="/admin/accessCodeUsage">
                        <asset:image src="/ws/icon-db-migrate-user.svg"></asset:image>
                        <p>Scratch Card Usage Reports</p>
                    </a>
                </div>
                <%}%>
                <%if((wsAdmin||showPublisherAdminControls)&&"true".equals(session["showAccessCode"])){%>
                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="/reports/scratchCardReport">
                        <asset:image src="/ws/icon-db-migrate-user.svg"></asset:image>
                        <p>Scratch Card Creation Reports</p>
                    </a>
                </div>
                <%}%>

            </div>
        </div>
    </div>
    <%}%>
    <!-- Customer Support -->
    <%if(customerSupport){%>
    <div class="container mt-5">
        <div class="db-section-title my-3">
            <h5 class="text-secondary text-secondary-modifier"><strong>Customer Support Controls</strong></h5>
        </div>
        <div class="card card-modifier card-shadow border-0 db-common-info db-admin db-support col-12 py-4 px-2 px-md-4">
            <div class="row mx-0">
                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="/admin/externalOrders">
                        <asset:image src="/ws/icon-db-external-orders.svg"></asset:image>
                        <p>External Orders</p>
                    </a>
                </div>
                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="/log/userManagement">
                        <asset:image src="/ws/icon-db-force-logout.svg"></asset:image>
                        <p>Enable Force Logout</p>
                    </a>
                </div>
                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="/admin/userBooks">
                        <asset:image src="/ws/icon-db-external-orders.svg"></asset:image>
                        <p>User Purchase History</p>
                    </a>
                </div>
            </div>
        </div>
    </div>
    <%}%>
    <%if(wsAdmin){%>
    <!-- Chat & Notification -->
    <div class="container mt-5">
        <div class="db-section-title my-3">
            <h5 class="text-secondary text-secondary-modifier"><strong>Chat & Notification</strong></h5>
        </div>
        <div class="card card-modifier card-shadow border-0 db-common-info db-chat col-12 py-4 px-2 px-md-4">
            <div class="row mx-0">
                <sec:ifAllGranted roles="ROLE_NOTIFICATION">
                    <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                        <a href="/log/notification">
                            <asset:image src="/ws/icon-db-webchat.svg"></asset:image>
                            <p>Notification</p>
                        </a>
                    </div>
                </sec:ifAllGranted>
                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="/comments/chatLogin">
                        <asset:image src="/ws/icon-db-webchat.svg"></asset:image>
                        <p>Live Web Chat</p>
                    </a>
                </div>
                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="/comments/downloadWebChat">
                        <asset:image src="/ws/icon-db-download-chat.svg"></asset:image>
                        <p>Download Web Chat</p>
                    </a>
                </div>
                <sec:ifAllGranted roles="ROLE_NOTIFICATION">
                    <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                        <a href="/log/notificationManagement">
                            <asset:image src="/ws/icon-db-notification-management.svg"></asset:image>
                            <p>Notification Management</p>
                        </a>
                    </div>
                </sec:ifAllGranted>
                <sec:ifAllGranted roles="ROLE_ENQUIRY_SALES">
                    <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                        <a href="/log/enquiryDetails">
                            <asset:image src="/ws/icon-db-webchat.svg"></asset:image>
                            <p>Enquiry Details</p>
                        </a>
                    </div>
                </sec:ifAllGranted>
                <sec:ifAllGranted roles="ROLE_SEARCH_ACCESS">
                    <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                        <a href="/admin/searchDetails">
                            <asset:image src="/ws/icon-db-webchat.svg"></asset:image>
                            <p>Search Details</p>
                        </a>
                    </div>
                </sec:ifAllGranted>
                <sec:ifAllGranted roles="ROLE_DELETE_USER">
                    <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                        <a href="/admin/deleteuser">
                            <asset:image src="/ws/icon-db-webchat.svg"></asset:image>
                            <p>Delete User</p>
                        </a>
                    </div>
                </sec:ifAllGranted>
                <sec:ifAllGranted roles="ROLE_APP_ADMIN">
                    <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                        <a href="/log/appVersionManagement">
                            <asset:image src="/ws/icon-db-webchat.svg"></asset:image>
                            <p>App Version Management</p>
                        </a>
                    </div>
                </sec:ifAllGranted>
                <sec:ifAllGranted roles="ROLE_GPT_MANAGER">
                    <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                        <a href="/questionTypes/list">
                            <asset:image src="/ws/icon-db-manage-templates.svg"></asset:image>
                            <p>Question Types</p>
                        </a>
                    </div>
                </sec:ifAllGranted>
                <sec:ifAllGranted roles="ROLE_WS_EDITOR">
                    <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                        <a href="/wonderpublish/wseditor">
                            <asset:image src="/ws/icon-db-webchat.svg"></asset:image>
                            <p>WS Editor</p>
                        </a>
                    </div>
                </sec:ifAllGranted>
                <sec:ifAllGranted roles="ROLE_ACCESS_CONTROLL">
                    <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                        <a href="/log/userAccess">
                            <asset:image src="/ws/icon-db-webchat.svg"></asset:image>
                            <p>User Access</p>
                        </a>
                    </div>
                </sec:ifAllGranted>
            </div>
        </div>
    </div>
    <%}%>
    <!-- Customer Support -->
    <%if(supportManager){%>
    <div class="container mt-5">
        <div class="db-section-title my-3">
            <h5 class="text-secondary text-secondary-modifier"><strong>Customer Support</strong></h5>
        </div>
        <div class="card card-modifier card-shadow border-0 db-common-info db-admin db-support col-12 py-4 px-2 px-md-4">
            <div class="row mx-0">
                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="/support/findScratchCode">
                        <asset:image src="/ws/icon-db-external-orders.svg"></asset:image>
                        <p>Scratch Code Usage</p>
                    </a>
                </div>

            </div>
        </div>
    </div>
    <%}%>
</section>

<g:render template="/privatelabel/footer_new"></g:render>


<script>
    $(document).ready(function () {
        $(".loading-icon").removeClass("hidden");
        $(".db-common-info").each(function () {
            var cardsCount = $(this).find('.card').size();
            if(cardsCount === 1) {
                $(this).removeClass("col-12").addClass("col-6 col-md-4 col-lg-3 col-xl-2 single-card");
                $(this).find(".card").removeClass("col-6 col-md-4 col-lg-3 col-xl-2").addClass("col-12");
            } else if (cardsCount === 2) {
                $(this).addClass("col-md-8 col-lg-6 col-xl-4 two-cards");
                $(this).find(".card").removeClass("col-md-4 col-lg-3 col-xl-2").addClass("col-md-6");
            } else if (cardsCount === 3) {
                $(this).addClass("col-md-12 col-lg-9 col-xl-6 three-cards");
                $(this).find(".card").removeClass("col-lg-3 col-xl-2");
            } else if (cardsCount === 4) {
                $(this).addClass("col-md-12 col-lg-12 col-xl-8 four-cards");
                $(this).find(".card").removeClass("col-xl-2");
            } else {
                $(this).addClass("col-12 above-four-cards");
            }
        });
        $(".db-common-info .card a, .top_main_mobile__menu a, .available-cards a").on('click', function () {
            $(".loading-icon").removeClass("hidden");
        });
    });

    var insAccessId = localStorage.getItem('instituteAccess');
    var instituteId = "";
    var check = false;
    if(insAccessId!="" && insAccessId!=null) {
        $('.institute-access').each(function() {
            instituteId = $(this).attr('id').split('_').pop();
            if(insAccessId===instituteId) {
                check = true;
                $("#institute_"+instituteId).addClass("access-granted");
                $('.db-common-info').addClass('has-institutes');
                $("#instituteMessage").show();
                document.getElementById("instituteMessage").innerHTML = "You have been added to <span class='text-capitalize'>"+$('#institute_'+instituteId).find('h5').text()+".</span>";
                $(".access-granted .card h5").before($("<span class='institute-tag'>New</span>"));
            } else if(insAccessId!=instituteId && check==false) {
                $("#institute_"+instituteId).removeClass("access-granted");
                $("#instituteMessage").hide();
            }
        });
    } else {
        $(".institute-access").removeClass("access-granted");
        $('.db-common-info').removeClass('has-institutes');
        $("#instituteMessage").hide();
    }

    window.addEventListener('load', function() {
        localStorage.setItem('instituteAccess','');
        $(".loading-icon").addClass("hidden");
    }, false);

    window.addEventListener( "pageshow", function () {
        $(".loading-icon").addClass("hidden");
    }, false);
</script>

</body>
</html>
