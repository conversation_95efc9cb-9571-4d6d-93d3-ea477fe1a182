<script>

// Centralized function to hide all sections below Advanced Tools accordion
function hideAllAdvancedToolsSections() {
    const sectionsToHide = [
        'teaching-coach-section',
        'question-bank-section',
        'analytics-section'
    ];

    sectionsToHide.forEach(sectionId => {
        const section = document.getElementById(sectionId);
        if (section) {
            section.style.display = 'none';
        }
    });

    console.log('All advanced tools sections hidden');
}

// Function to display Teaching Coach content
displayTeachingCoachContent = function(data) {
    console.log('Teaching Coach data:', data);

    // Get the section where we'll display the content
    const teachingCoachSection = document.getElementById('teaching-coach-section');

    if (!teachingCoachSection) {
        console.error('Teaching Coach section not found');
        return;
    }

    // Clear previous content
    teachingCoachSection.innerHTML = '';

    // Check if we have valid data
    if (!data || !data.hasResource || !data.answer) {
        // No data available, show a message
        const noDataDiv = document.createElement('div');
        noDataDiv.style.padding = '30px';
        noDataDiv.style.textAlign = 'center';
        noDataDiv.style.color = '#666';
        noDataDiv.style.fontSize = '16px';
        noDataDiv.innerHTML = '<i data-lucide="info" style="width: 24px; height: 24px; margin-bottom: 10px; color: #3498db;"></i><p>Teaching Coach content is not available for this chapter. The system has attempted to generate it, but it may take some time. Please try again later.</p>';

        teachingCoachSection.appendChild(noDataDiv);

        // Initialize Lucide icons
        if (typeof lucide !== 'undefined' && lucide.createIcons) {
            lucide.createIcons();
        }

        return;
    }

    try {
        // Parse the JSON from the answer field
        const coachData = JSON.parse(data.answer);

        if (!Array.isArray(coachData) || coachData.length === 0) {
            throw new Error('Invalid data format');
        }

        // Create the container for the teaching coach content
        const coachContainer = document.createElement('div');
        coachContainer.className = 'teaching-coach-container';
        coachContainer.style.padding = '15px';

        // Create a container for the teaching coach layout
        const teachingCoachLayout = document.createElement('div');
        teachingCoachLayout.className = 'teaching-coach-layout';
        teachingCoachLayout.style.display = 'flex';
        teachingCoachLayout.style.flexDirection = 'column';
        teachingCoachLayout.style.gap = '20px';

        // Add a title for the section
        const sectionTitle = document.createElement('h3');
        sectionTitle.style.fontSize = '18px';
        sectionTitle.style.fontWeight = '600';
        sectionTitle.style.margin = '0 0 10px 0';
        sectionTitle.style.color = '#333';
        sectionTitle.textContent = 'Teaching Coach';
        teachingCoachLayout.appendChild(sectionTitle);

        // Create a container for the main content area (boxes + details side by side)
        const contentContainer = document.createElement('div');
        contentContainer.className = 'content-container';
        contentContainer.style.display = 'flex';
        contentContainer.style.gap = '20px';
        contentContainer.style.alignItems = 'flex-start';

        // Create the vertical container for teaching coach boxes
        const teachingCoachContainer = document.createElement('div');
        teachingCoachContainer.className = 'teaching-coach-boxes';
        teachingCoachContainer.style.display = 'flex';
        teachingCoachContainer.style.flexDirection = 'column';
        teachingCoachContainer.style.gap = '15px';
        teachingCoachContainer.style.width = '400px';
        teachingCoachContainer.style.flexShrink = '0';

        // Create the details panel container
        const detailsPanel = document.createElement('div');
        detailsPanel.className = 'details-panel';
        detailsPanel.style.flex = '1';
        detailsPanel.style.backgroundColor = '#ffffff';
        detailsPanel.style.borderRadius = '12px';
        detailsPanel.style.padding = '25px';
        detailsPanel.style.boxShadow = '0 4px 12px rgba(0,0,0,0.1)';
        detailsPanel.style.border = '1px solid #e9ecef';
        detailsPanel.style.display = 'none'; // Hidden by default
        detailsPanel.style.minHeight = '400px';

        // Add a loading indicator for the details panel
        const detailsLoading = document.createElement('div');
        detailsLoading.className = 'details-loading';
        detailsLoading.style.display = 'none';
        detailsLoading.style.textAlign = 'center';
        detailsLoading.style.padding = '20px';
        detailsLoading.innerHTML = '<div class="spinner" style="margin: 0 auto; width: 40px; height: 40px; border: 4px solid #f3f3f3; border-top: 4px solid #3498db; border-radius: 50%; animation: spin 2s linear infinite;"></div><p style="margin-top: 10px;">Loading details...</p>';
        detailsPanel.appendChild(detailsLoading);

        // Create a container for the details content
        const detailsContent = document.createElement('div');
        detailsContent.className = 'details-content';
        detailsPanel.appendChild(detailsContent);

        // Variable to track the currently selected step
        let selectedStep = null;

        // Function to insert details panel after clicked box in mobile view
        function insertDetailsPanelAfterBox(boxElement) {
            // Find the next sibling (could be a connector arrow or another box)
            let nextElement = boxElement.nextElementSibling;

            // If next element is a connector arrow, get the element after that
            if (nextElement && nextElement.className === 'connector-arrow') {
                nextElement = nextElement.nextElementSibling;
            }

            // Remove details panel from its current position
            if (detailsPanel.parentNode) {
                detailsPanel.parentNode.removeChild(detailsPanel);
            }

            // Insert details panel after the clicked box (and its connector if exists)
            if (nextElement) {
                teachingCoachContainer.insertBefore(detailsPanel, nextElement);
            } else {
                // If it's the last box, append at the end
                teachingCoachContainer.appendChild(detailsPanel);
            }
        }

        // Function to restore details panel to original position (for desktop view)
        function restoreDetailsPanelPosition() {
            // Remove details panel from its current position
            if (detailsPanel.parentNode) {
                detailsPanel.parentNode.removeChild(detailsPanel);
            }

            // Add it back to the content container (original position)
            contentContainer.appendChild(detailsPanel);
        }

        // Add window resize listener to handle view changes
        window.addEventListener('resize', function() {
            const isMobile = window.innerWidth <= 768;
            if (!isMobile && detailsPanel.parentNode === teachingCoachContainer) {
                // Switched from mobile to desktop, restore original position
                restoreDetailsPanelPosition();
            }
        });

        // Function to load details for a concept
        async function loadConceptDetails(concept, boxElement) {
            // Check if we're in mobile view
            const isMobile = window.innerWidth <= 768;

            if (isMobile) {
                // In mobile, insert details panel right after the clicked box
                insertDetailsPanelAfterBox(boxElement);
            }

            // Show loading indicator
            detailsLoading.style.display = 'block';
            detailsContent.style.display = 'none';
            detailsPanel.style.display = 'block';

            // Update selected box styling
            if (selectedStep) {
                selectedStep.style.transform = 'scale(1)';
                selectedStep.style.boxShadow = '0 2px 8px rgba(0,0,0,0.1)';
                selectedStep.style.borderLeft = '4px solid rgba(0,0,0,0.1)';
                selectedStep.style.border = '2px solid transparent';
                // Reset the click hint for the previously selected box
                const prevClickHint = selectedStep.querySelector('div');
                if (prevClickHint) {
                    prevClickHint.style.backgroundColor = 'rgba(255,255,255,0.8)';
                    prevClickHint.style.color = '#666';
                    prevClickHint.innerHTML = '▶';
                }
            }
            boxElement.style.transform = 'scale(1.05)';
            boxElement.style.boxShadow = '0 12px 24px rgba(66, 133, 244, 0.3)';
            boxElement.style.borderLeft = '4px solid #4285f4';
            boxElement.style.border = '2px solid #4285f4';
            // Update the click hint for the selected box
            const clickHint = boxElement.querySelector('div');
            if (clickHint) {
                clickHint.style.backgroundColor = 'rgba(66, 133, 244, 0.3)';
                clickHint.style.color = '#4285f4';
                clickHint.innerHTML = '✓';
            }
            selectedStep = boxElement;

            try {
                // Make API call to get details
                const response = await fetch('/autogpt/getTeachingCoachLevel2?chapterId=' + gptChapterId + '&resId=' + gptResId + '&slug=' + concept.slug);
                if (!response.ok) {
                    throw new Error('Failed to fetch concept details');
                }

                const data = await response.json();
                if (!data || !data.response) {
                    throw new Error('Invalid response data');
                }

                // Parse the JSON from the response field
                const details = JSON.parse(data.response);

                // Render the details
                renderConceptDetails(details, concept);
            } catch (error) {
                console.error('Error loading concept details:', error);
                detailsContent.innerHTML = '<div style="padding: 20px; text-align: center; color: #e74c3c;">' +
                    '<i data-lucide="alert-triangle" style="width: 24px; height: 24px; margin-bottom: 10px;"></i>' +
                    '<p>There was an error loading the details. Please try again later.</p>' +
                    '</div>';

                // Initialize Lucide icons
                if (typeof lucide !== 'undefined' && lucide.createIcons) {
                    lucide.createIcons();
                }
            } finally {
                // Hide loading indicator
                detailsLoading.style.display = 'none';
                detailsContent.style.display = 'block';
            }
        }

        // Function to render concept details
        function renderConceptDetails(details, concept) {
            // Create the details content with improved styling
            let html = '<div class="details-header" style="margin-bottom: 25px; border-bottom: 2px solid #e9ecef; padding-bottom: 20px; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); padding: 20px; border-radius: 8px; margin: -25px -25px 25px -25px;">' +
                '<h3 style="font-size: 24px; font-weight: 700; margin: 0 0 10px 0; color: #2c3e50;">' + concept.label + '</h3>' +
                '<div style="display: flex; gap: 15px; align-items: center; flex-wrap: wrap;">' +
                '<span style="font-size: 15px; color: #6c757d; font-weight: 500; background: white; padding: 5px 12px; border-radius: 20px; border: 1px solid #dee2e6;">' + concept.type + '</span>' +
                '<span style="font-size: 12px; padding: 6px 12px; border-radius: 15px; background-color: ' + getBloomColor(concept.bloom).bgColor + '; color: ' + getBloomColor(concept.bloom).textColor + '; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px;">' + concept.bloom + '</span>';

            if (details.minutes) {
                html += '<span style="font-size: 14px; color: #6c757d; background: white; padding: 5px 12px; border-radius: 20px; border: 1px solid #dee2e6;"><i data-lucide="clock" style="width: 14px; height: 14px; vertical-align: middle; margin-right: 5px;"></i>' + details.minutes + ' min</span>';
            }

            html += '</div></div>';

            // Add learning objectives if available
            if (details.objectives && details.objectives.length > 0) {
                html += '<div class="details-section" style="margin-bottom: 25px; background: #f8f9fa; padding: 20px; border-radius: 10px; border-left: 4px solid #007bff;">' +
                    '<h4 style="font-size: 18px; font-weight: 600; margin: 0 0 15px 0; color: #007bff; display: flex; align-items: center;"><i data-lucide="target" style="width: 20px; height: 20px; margin-right: 8px;"></i>Learning Objectives</h4>' +
                    '<ul style="margin: 0; padding-left: 20px; line-height: 1.6;">';

                for (let i = 0; i < details.objectives.length; i++) {
                    html += '<li style="margin-bottom: 8px; color: #495057;">' + details.objectives[i] + '</li>';
                }

                html += '</ul></div>';
            }

            // Add content summary if available
            if (details.contentSummary) {
                html += '<div class="details-section" style="margin-bottom: 25px; background: #e8f5e9; padding: 20px; border-radius: 10px; border-left: 4px solid #28a745;">' +
                    '<h4 style="font-size: 18px; font-weight: 600; margin: 0 0 15px 0; color: #28a745; display: flex; align-items: center;"><i data-lucide="file-text" style="width: 20px; height: 20px; margin-right: 8px;"></i>Content Summary</h4>' +
                    '<p style="margin: 0; line-height: 1.6; color: #495057;">' + details.contentSummary + '</p>' +
                    '</div>';
            }

            // Add activities if available
            if (details.activities && details.activities.length > 0) {
                html += '<div class="details-section" style="margin-bottom: 25px; background: #fff3e0; padding: 20px; border-radius: 10px; border-left: 4px solid #ff9800;">' +
                    '<h4 style="font-size: 18px; font-weight: 600; margin: 0 0 15px 0; color: #ff9800; display: flex; align-items: center;"><i data-lucide="activity" style="width: 20px; height: 20px; margin-right: 8px;"></i>Activities</h4>';

                for (let i = 0; i < details.activities.length; i++) {
                    const activity = details.activities[i];
                    html += '<div style="background-color: #fff; border-radius: 8px; padding: 18px; margin-bottom: 15px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); border: 1px solid #f0f0f0;">' +
                        '<h5 style="font-size: 16px; font-weight: 600; margin: 0 0 10px 0; color: #2c3e50; display: flex; align-items: center; justify-content: space-between;">' +
                        '<span>' + activity.title + '</span>';

                    if (activity.duration) {
                        html += '<span style="font-size: 12px; color: #6c757d; font-weight: 500; background: #f8f9fa; padding: 4px 8px; border-radius: 12px;">' + activity.duration + ' min</span>';
                    }

                    html += '</h5>';

                    if (activity.materials) {
                        html += '<div style="margin: 0 0 10px 0; font-size: 14px; color: #495057;"><strong style="color: #6f42c1;">Materials:</strong> ' + activity.materials.join(', ') + '</div>';
                    }

                    if (activity.steps) {
                        html += '<div style="margin: 0; font-size: 14px; color: #495057; line-height: 1.5;"><strong style="color: #6f42c1;">Steps:</strong> ' + activity.steps + '</div>';
                    }

                    html += '</div>';
                }

                html += '</div>';
            }

            // Add formative checks if available
            if (details.formativeChecks && details.formativeChecks.length > 0) {
                html += '<div class="details-section" style="margin-bottom: 25px; background: #e3f2fd; padding: 20px; border-radius: 10px; border-left: 4px solid #2196f3;">' +
                    '<h4 style="font-size: 18px; font-weight: 600; margin: 0 0 15px 0; color: #2196f3; display: flex; align-items: center;"><i data-lucide="check-circle" style="width: 20px; height: 20px; margin-right: 8px;"></i>Formative Checks</h4>';

                for (let i = 0; i < details.formativeChecks.length; i++) {
                    const check = details.formativeChecks[i];
                    html += '<div style="background-color: #fff; border-radius: 8px; padding: 18px; margin-bottom: 15px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); border: 1px solid #f0f0f0;">' +
                        '<div style="margin: 0 0 12px 0; font-size: 15px; color: #2c3e50;"><strong style="color: #2196f3;">' + check.type + ':</strong> ' + check.stem + '</div>';

                    if (check.options) {
                        html += '<ul style="margin: 0 0 12px 0; padding-left: 20px; line-height: 1.5;">';

                        for (let j = 0; j < check.options.length; j++) {
                            html += '<li style="margin-bottom: 5px; color: #495057;">' + check.options[j] + '</li>';
                        }

                        html += '</ul>';
                    }

                    html += '<div style="margin: 0; font-size: 14px; color: #495057; background: #f8f9fa; padding: 10px; border-radius: 6px;"><strong style="color: #28a745;">Answer:</strong> ' + check.answer + '</div>' +
                        '</div>';
                }

                html += '</div>';
            }

            // Add misconceptions if available
            if (details.misconceptions && details.misconceptions.length > 0) {
                html += '<div class="details-section" style="margin-bottom: 25px; background: #fce4ec; padding: 20px; border-radius: 10px; border-left: 4px solid #e91e63;">' +
                    '<h4 style="font-size: 18px; font-weight: 600; margin: 0 0 15px 0; color: #e91e63; display: flex; align-items: center;"><i data-lucide="alert-triangle" style="width: 20px; height: 20px; margin-right: 8px;"></i>Common Misconceptions</h4>';

                for (let i = 0; i < details.misconceptions.length; i++) {
                    const misconception = details.misconceptions[i];
                    html += '<div style="background-color: #fff; border-radius: 8px; padding: 18px; margin-bottom: 15px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); border: 1px solid #f0f0f0;">' +
                        '<div style="margin: 0 0 10px 0; font-size: 15px; color: #2c3e50;"><strong style="color: #e91e63;">Issue:</strong> ' + misconception.issue + '</div>' +
                        '<div style="margin: 0; font-size: 14px; color: #495057; background: #f8f9fa; padding: 10px; border-radius: 6px;"><strong style="color: #28a745;">Fix:</strong> ' + misconception.fix + '</div>' +
                        '</div>';
                }

                html += '</div>';
            }

            // Add assets if available
            if (details.assets && details.assets.length > 0) {
                html += '<div class="details-section" style="background: #f3e5f5; padding: 20px; border-radius: 10px; border-left: 4px solid #9c27b0;">' +
                    '<h4 style="font-size: 18px; font-weight: 600; margin: 0 0 15px 0; color: #9c27b0; display: flex; align-items: center;"><i data-lucide="folder" style="width: 20px; height: 20px; margin-right: 8px;"></i>Assets</h4>' +
                    '<ul style="margin: 0; padding-left: 20px; line-height: 1.6;">';

                for (let i = 0; i < details.assets.length; i++) {
                    html += '<li style="margin-bottom: 8px; color: #495057;">' + details.assets[i] + '</li>';
                }

                html += '</ul></div>';
            }

            // Update the details content
            detailsContent.innerHTML = html;

            // Initialize Lucide icons
            if (typeof lucide !== 'undefined' && lucide.createIcons) {
                lucide.createIcons();
            }
        }

        // Define an array of background colors for the boxes
        const boxColors = [
            '#E3F2FD', // Light Blue
            '#E8F5E9', // Light Green
            '#FFF3E0', // Light Orange
            '#F3E5F5', // Light Purple
            '#FCE4EC', // Light Pink
            '#E0F7FA', // Light Cyan
            '#FFF8E1', // Light Yellow
            '#EFEBE9', // Light Brown
            '#F1F8E9', // Light Lime
            '#E8EAF6', // Light Indigo
            '#FFF3E0', // Light Deep Orange
            '#E0F2F1'  // Light Teal
        ];

        // Add each concept as a box in the grid
        coachData.forEach((concept, index) => {
            // Create the concept box
            const conceptBox = document.createElement('div');
            conceptBox.className = 'concept-box';
            conceptBox.style.backgroundColor = boxColors[index % boxColors.length];
            conceptBox.style.borderRadius = '12px';
            conceptBox.style.padding = '18px 20px';
            conceptBox.style.boxShadow = '0 2px 8px rgba(0,0,0,0.1)';
            conceptBox.style.cursor = 'pointer';
            conceptBox.style.transition = 'all 0.3s ease';
            conceptBox.style.border = '2px solid transparent';
            conceptBox.style.width = '100%';
            conceptBox.style.display = 'flex';
            conceptBox.style.flexDirection = 'column';
            conceptBox.style.gap = '10px';
            conceptBox.style.minHeight = '100px';
            conceptBox.style.position = 'relative';

            // Add a subtle click indicator
            conceptBox.style.borderLeft = '4px solid rgba(0,0,0,0.1)';

            // Add a small click hint icon
            const clickHint = document.createElement('div');
            clickHint.style.position = 'absolute';
            clickHint.style.top = '8px';
            clickHint.style.right = '8px';
            clickHint.style.width = '16px';
            clickHint.style.height = '16px';
            clickHint.style.borderRadius = '50%';
            clickHint.style.backgroundColor = 'rgba(255,255,255,0.8)';
            clickHint.style.display = 'flex';
            clickHint.style.alignItems = 'center';
            clickHint.style.justifyContent = 'center';
            clickHint.style.fontSize = '10px';
            clickHint.style.color = '#666';
            clickHint.innerHTML = '▶';
            conceptBox.appendChild(clickHint);

            // Add hover effect
            conceptBox.addEventListener('mouseover', function() {
                if (conceptBox !== selectedStep) {
                    conceptBox.style.transform = 'translateY(-3px) scale(1.02)';
                    conceptBox.style.boxShadow = '0 8px 20px rgba(0,0,0,0.2)';
                    conceptBox.style.borderLeft = '4px solid #4285f4';
                    clickHint.style.backgroundColor = 'rgba(66, 133, 244, 0.2)';
                    clickHint.style.color = '#4285f4';
                }
            });

            conceptBox.addEventListener('mouseout', function() {
                if (conceptBox !== selectedStep) {
                    conceptBox.style.transform = 'translateY(0) scale(1)';
                    conceptBox.style.boxShadow = '0 2px 8px rgba(0,0,0,0.1)';
                    conceptBox.style.borderLeft = '4px solid rgba(0,0,0,0.1)';
                    clickHint.style.backgroundColor = 'rgba(255,255,255,0.8)';
                    clickHint.style.color = '#666';
                }
            });

            // Create the concept title
            const conceptTitle = document.createElement('h4');
            conceptTitle.style.fontSize = '16px';
            conceptTitle.style.fontWeight = '700';
            conceptTitle.style.margin = '0';
            conceptTitle.style.color = '#2c3e50';
            conceptTitle.style.lineHeight = '1.3';
            conceptTitle.textContent = concept.label;

            // Create the concept type display
            const conceptType = document.createElement('div');
            conceptType.style.fontSize = '13px';
            conceptType.style.color = '#7f8c8d';
            conceptType.style.fontWeight = '500';
            conceptType.style.marginBottom = '8px';
            conceptType.textContent = concept.type;

            // Create the Bloom's taxonomy tag
            const bloomTag = document.createElement('span');
            bloomTag.className = 'bloom-tag';
            bloomTag.style.fontSize = '10px';
            bloomTag.style.padding = '3px 8px';
            bloomTag.style.borderRadius = '12px';
            bloomTag.style.backgroundColor = getBloomColor(concept.bloom).bgColor;
            bloomTag.style.color = getBloomColor(concept.bloom).textColor;
            bloomTag.style.fontWeight = '600';
            bloomTag.style.textTransform = 'uppercase';
            bloomTag.style.letterSpacing = '0.5px';
            bloomTag.style.alignSelf = 'flex-start';
            bloomTag.textContent = concept.bloom;

            // Add elements to the box
            conceptBox.appendChild(conceptTitle);
            conceptBox.appendChild(conceptType);
            conceptBox.appendChild(bloomTag);

            // Add click event to load details
            conceptBox.addEventListener('click', function() {
                // Scroll to the top of the Teaching Coach section
                const teachingCoachSection = document.getElementById('teaching-coach-section');
                if (teachingCoachSection) {
                    teachingCoachSection.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }

                // Load the concept details
                loadConceptDetails(concept, conceptBox);
            });

            // Add the concept box to the container
            teachingCoachContainer.appendChild(conceptBox);

            // Add a connector arrow if not the last item
            if (index < coachData.length - 1) {
                const connector = document.createElement('div');
                connector.className = 'connector-arrow';
                connector.style.display = 'flex';
                connector.style.justifyContent = 'center';
                connector.style.alignItems = 'center';
                connector.style.height = '20px';
                connector.style.margin = '5px 0';
                connector.style.color = '#9ca3af';
                connector.style.fontSize = '16px';
                connector.style.fontWeight = 'bold';

                // Create the arrow icon
                const arrowIcon = document.createElement('div');
                arrowIcon.style.width = '0';
                arrowIcon.style.height = '0';
                arrowIcon.style.borderLeft = '6px solid transparent';
                arrowIcon.style.borderRight = '6px solid transparent';
                arrowIcon.style.borderTop = '8px solid #9ca3af';
                arrowIcon.style.transition = 'all 0.3s ease';

                connector.appendChild(arrowIcon);
                teachingCoachContainer.appendChild(connector);
            }
        });

        // Add the boxes container and details panel to the content container
        contentContainer.appendChild(teachingCoachContainer);
        contentContainer.appendChild(detailsPanel);

        // Add the content container to the teaching coach layout
        teachingCoachLayout.appendChild(contentContainer);

        // Add responsive styles for mobile
        const mobileStyle = document.createElement('style');
        mobileStyle.textContent = '@media (max-width: 768px) {' +
            '.content-container {' +
            '    flex-direction: column !important;' +
            '}' +
            '.teaching-coach-boxes {' +
            '    width: 100% !important;' +
            '}' +
            '.details-panel {' +
            '    padding: 20px !important;' +
            '    min-height: 300px !important;' +
            '    margin: 15px 0 !important;' +
            '    width: 100% !important;' +
            '    flex: none !important;' +
            '}' +
            '.concept-box {' +
            '    min-height: 90px !important;' +
            '    padding: 15px !important;' +
            '}' +
            '.connector-arrow {' +
            '    margin: 8px 0 !important;' +
            '}' +
            '}';
        document.head.appendChild(mobileStyle);

        // Add the teaching coach layout to the container
        coachContainer.appendChild(teachingCoachLayout);
        teachingCoachSection.appendChild(coachContainer);

        // Initialize Lucide icons
        if (typeof lucide !== 'undefined' && lucide.createIcons) {
            lucide.createIcons();
        }

    } catch (error) {
        console.error('Error parsing Teaching Coach data:', error);

        // Show error message
        const errorDiv = document.createElement('div');
        errorDiv.style.padding = '20px';
        errorDiv.style.color = '#e74c3c';
        errorDiv.style.textAlign = 'center';
        errorDiv.innerHTML = '<i data-lucide="alert-triangle" style="width: 24px; height: 24px; margin-bottom: 10px;"></i><p>There was an error processing the Teaching Coach data.</p>';

        teachingCoachSection.appendChild(errorDiv);

        // Initialize Lucide icons
        if (typeof lucide !== 'undefined' && lucide.createIcons) {
            lucide.createIcons();
        }
    }

    // Function to get color based on Bloom's taxonomy level
    function getBloomColor(bloomLevel) {
        const colors = {
            'Remembering': { bgColor: '#e3f2fd', textColor: '#1565c0' },
            'Understanding': { bgColor: '#e8f5e9', textColor: '#2e7d32' },
            'Applying': { bgColor: '#fff3e0', textColor: '#e65100' },
            'Analyzing': { bgColor: '#f3e5f5', textColor: '#7b1fa2' },
            'Evaluating': { bgColor: '#fce4ec', textColor: '#c2185b' },
            'Creating': { bgColor: '#e0f7fa', textColor: '#00838f' }
        };

        return colors[bloomLevel] || { bgColor: '#f5f5f5', textColor: '#616161' };
    }

    // Scroll to the Teaching Coach section
    window.scrollTo({
        top: teachingCoachSection.offsetTop - 100,
        behavior: 'smooth'
    });
};

// Function to get Teaching Coach content
function getTeachingCoach() {
    // Get current chapter information
    const chapterSelect = document.getElementById('chapterSelect');
    const selectedValue = chapterSelect.value;

    if (!selectedValue || selectedValue === '') {
        // No chapter selected - show warning and focus on dropdown
        const chapterSelectWarning = document.getElementById('chapter-select-warning');

        if (chapterSelectWarning) {
            chapterSelectWarning.style.display = 'block';
        }

        if (chapterSelect) {
            chapterSelect.classList.add('highlight-select');
            chapterSelect.focus();
        }

        return;
    }

    // Clear any previous warnings
    const chapterSelectWarning = document.getElementById('chapter-select-warning');
    if (chapterSelectWarning) {
        chapterSelectWarning.style.display = 'none';
    }

    if (chapterSelect) {
        chapterSelect.classList.remove('highlight-select');
    }

    const [chapterId, resId] = selectedValue.split('_');
    gptResId = resId;
    gptChapterId = chapterId;

    // Hide all other sections first
    hideAllAdvancedToolsSections();

    // Close the Advanced Tools accordion immediately if it's open
    const advancedToolsContent = document.querySelector('.advanced-tools-content');
    if (advancedToolsContent && advancedToolsContent.classList.contains('active')) {
        const advancedToolsToggle = document.querySelector('.advanced-tools-toggle');
        if (advancedToolsToggle) {
            advancedToolsToggle.click();
        }
    }

    // Show loading indicator in the Teaching Coach section
    const teachingCoachSection = document.getElementById('teaching-coach-section');

    if (teachingCoachSection) {
        teachingCoachSection.style.display = 'block';

        // Show loading indicator
        const loadingHTML = '<div style="padding: 20px; text-align: center;"><div class="spinner" style="margin: 0 auto; width: 40px; height: 40px; border: 4px solid #f3f3f3; border-top: 4px solid #3498db; border-radius: 50%; animation: spin 2s linear infinite;"></div><p style="margin-top: 10px;">Loading Teaching Coach ...</p></div>';

        teachingCoachSection.innerHTML = loadingHTML;

        // Add animation style if not already present
        if (!document.getElementById('spinner-style')) {
            const style = document.createElement('style');
            style.id = 'spinner-style';
            style.textContent = '@keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }';
            document.head.appendChild(style);
        }

        // Scroll to the Teaching Coach section to show the loading indicator
        window.scrollTo({
            top: teachingCoachSection.offsetTop - 100,
            behavior: 'smooth'
        });
    }

    // Function to fetch Teaching Coach content
    async function fetchTeachingCoachContent() {
        try {
            // First attempt to get Teaching Coach content
            const response = await fetch('/prompt/getGPTsForResource?resId=' + gptResId + '&promptType=teaching_coach');
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }

            const data = await response.json();

            // Check if we have valid data
            if (!data || !data.hasResource || !data.answer) {
                console.log('No Teaching Coach data available, setting up chapter...');

                // Call setUpChapter API
                const setupResponse = await fetch('/autogpt/setUpChapter?chapterId=' + gptChapterId);
                if (!setupResponse.ok) {
                    throw new Error('Failed to set up chapter');
                }

                const setupData = await setupResponse.json();
                if (!setupData || !setupData.namespace) {
                    throw new Error('Invalid response from setUpChapter');
                }

                const namespace = setupData.namespace;
                console.log('Chapter set up successfully, namespace:', namespace);

                // Call createAndUpdateGPTContent API
                const formData = new FormData();
                formData.append('namespace', namespace);
                formData.append('readingMaterialResId', gptResId);
                formData.append('chapterId', gptChapterId);
                formData.append('promptType', 'teaching_coach');

                const createResponse = await fetch('/autogpt/createAndUpdateGPTContent', {
                    method: 'POST',
                    body: formData
                });

                if (!createResponse.ok) {
                    throw new Error('Failed to create Teaching Coach content');
                }

                console.log('Teaching Coach content created, fetching again...');

                // Try to get the Teaching Coach content again
                const retryResponse = await fetch('/prompt/getGPTsForResource?resId=' + gptResId + '&promptType=teaching_coach');
                if (!retryResponse.ok) {
                    throw new Error('Failed to fetch Teaching Coach content after creation');
                }

                const retryData = await retryResponse.json();
                return retryData;
            }

            return data;
        } catch (error) {
            console.error('Error in fetchTeachingCoachContent:', error);
            throw error;
        }
    }

    // Make the API call to get Teaching Coach content
    fetchTeachingCoachContent()
        .then(data => {
            displayTeachingCoachContent(data);

            const advancedToolsContent = document.querySelector('.advanced-tools-content');
            if (advancedToolsContent && advancedToolsContent.classList.contains('active')) {
                const advancedToolsToggle = document.querySelector('.advanced-tools-toggle');
                if (advancedToolsToggle) {
                    advancedToolsToggle.click();
                }
            }

        })
        .catch(error => {
            console.error('Error fetching Teaching Coach data:', error);

            if (teachingCoachSection) {
                const errorHTML = '<div style="padding: 30px; text-align: center; color: #e74c3c;"><i data-lucide="alert-triangle" style="width: 24px; height: 24px; margin-bottom: 10px;"></i><p>There was an error loading the Teaching Coach content. Please try again later.</p></div>';
                teachingCoachSection.innerHTML = errorHTML;

                // Initialize Lucide icons
                if (typeof lucide !== 'undefined' && lucide.createIcons) {
                    lucide.createIcons();
                }


            }
        });
}

displayQuestionBankContent = function(data) {
    console.log(data);

    // Display the content in the tabs
    const mcqContent = document.getElementById('mcq-content');
    const qnaContent = document.getElementById('qna-content');
    const questionBankSection = document.getElementById('question-bank-section');
    const questionBankTabs = document.getElementById('question-bank-tabs');
    const mcqTab = document.getElementById('mcq-tab');
    const qnaTab = document.getElementById('qna-tab');
    const mcqCount = document.getElementById('mcq-count');
    const qnaCount = document.getElementById('qna-count');

    if (mcqContent && qnaContent && questionBankSection) {
        // Clear previous content
        mcqContent.innerHTML = '';
        qnaContent.innerHTML = '';

        // Check if we have any data
        const hasMCQs = data && data.mcqs && data.mcqs.length > 0;
        const hasQnA = data && data.qna && data.qna.length > 0;

        if (!hasMCQs && !hasQnA) {
            // No data available, show a message
            questionBankTabs.style.display = 'none';
            mcqContent.style.display = 'none';
            qnaContent.style.display = 'none';

            const noDataDiv = document.createElement('div');
            noDataDiv.style.padding = '30px';
            noDataDiv.style.textAlign = 'center';
            noDataDiv.style.color = '#666';
            noDataDiv.style.fontSize = '16px';
            noDataDiv.innerHTML = '<i data-lucide="info" style="width: 24px; height: 24px; margin-bottom: 10px; color: #3498db;"></i><p>Question Bank has not been created yet for this chapter.</p>';

            questionBankSection.appendChild(noDataDiv);

            // Initialize Lucide icons
            if (typeof lucide !== 'undefined' && lucide.createIcons) {
                lucide.createIcons();
            }

            // Render math formulas if needed (for future compatibility)
            if (typeof renderMathInElement === 'function') {
                renderMathInElement(questionBankSection);
            } else if (typeof MathJax !== 'undefined' && MathJax.Hub && MathJax.Hub.Queue) {
                MathJax.Hub.Queue(["Typeset", MathJax.Hub, questionBankSection]);
            }

            return;
        }

        // Configure tabs based on available data
        if (hasMCQs && hasQnA) {
            // Both types of data available, show both tabs
            questionBankTabs.style.display = 'flex';
            mcqTab.style.display = 'block';
            qnaTab.style.display = 'block';

            // Set counts
            mcqCount.textContent = data.mcqs.length;
            qnaCount.textContent = data.qna.length;

            // Show Q&A tab by default
            qnaTab.classList.add('active');
            qnaTab.style.borderBottom = '3px solid #4285f4';
            mcqTab.classList.remove('active');
            mcqTab.style.borderBottom = 'none';

            qnaContent.style.display = 'flex';
            mcqContent.style.display = 'none';
        } else if (hasMCQs) {
            // Only MCQs available
            questionBankTabs.style.display = 'none'; // Hide tabs since there's only one type
            mcqContent.style.display = 'flex';
            qnaContent.style.display = 'none';

            // Add a title with count
            const titleDiv = document.createElement('div');
            titleDiv.style.padding = '10px 0';
            titleDiv.style.marginBottom = '15px';
            titleDiv.style.borderBottom = '1px solid #ddd';
            titleDiv.style.fontSize = '16px';
            titleDiv.style.fontWeight = '500';
            titleDiv.innerHTML = '<span class="desktop-label">Objective Types</span><span class="mobile-label" style="display: none;">Obj Types</span> <span style="margin-left: 5px; font-size: 12px; background-color: #e0e0e0; padding: 2px 6px; border-radius: 10px;">' + data.mcqs.length + '</span>';
            mcqContent.insertBefore(titleDiv, mcqContent.firstChild);

        } else if (hasQnA) {
            // Only Q&A available
            questionBankTabs.style.display = 'none'; // Hide tabs since there's only one type
            mcqContent.style.display = 'none';
            qnaContent.style.display = 'flex';

            // Add a title with count
            const titleDiv = document.createElement('div');
            titleDiv.style.padding = '10px 0';
            titleDiv.style.marginBottom = '15px';
            titleDiv.style.borderBottom = '1px solid #ddd';
            titleDiv.style.fontSize = '16px';
            titleDiv.style.fontWeight = '500';
            titleDiv.innerHTML = '<span class="desktop-label">Question & Answers</span><span class="mobile-label" style="display: none;">Q&A</span> <span style="margin-left: 5px; font-size: 12px; background-color: #e0e0e0; padding: 2px 6px; border-radius: 10px;">' + data.qna.length + '</span>';
            qnaContent.insertBefore(titleDiv, qnaContent.firstChild);
        }

        // Display MCQs
        if (hasMCQs) {
            // Create MCQs display
            for (let index = 0; index < data.mcqs.length; index++) {
                const mcq = data.mcqs[index];
                const mcqDiv = document.createElement('div');
                mcqDiv.className = 'mcq-item';
                mcqDiv.style.marginBottom = '30px';
                mcqDiv.style.padding = '15px';
                mcqDiv.style.backgroundColor = '#f9f9f9';
                mcqDiv.style.borderRadius = '8px';
                mcqDiv.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';
                mcqDiv.style.display = 'block';
                mcqDiv.style.width = '100%';

                // Question
                const questionDiv = document.createElement('div');
                questionDiv.className = 'mcq-question';
                questionDiv.innerHTML = '<strong>Q' + (index + 1) + ':</strong> ' + (mcq.question || mcq.ps);
                questionDiv.style.marginBottom = '15px';
                questionDiv.style.fontSize = '16px';
                questionDiv.style.fontWeight = '500';
                mcqDiv.appendChild(questionDiv);

                // Options
                const optionsDiv = document.createElement('div');
                optionsDiv.className = 'mcq-options';
                optionsDiv.style.marginLeft = '20px';
                optionsDiv.style.marginBottom = '15px';
                optionsDiv.style.display = 'flex';
                optionsDiv.style.flexDirection = 'column';
                optionsDiv.style.width = '100%';

                // Find the correct answer
                let correctOptionIndex = 0;
                for (let i = 1; i <= 4; i++) {
                    if (mcq['answer' + i] === 'Yes') {
                        correctOptionIndex = i;
                        break;
                    }
                }

                // Add options
                for (let i = 1; i <= 4; i++) {
                    const option = mcq['option' + i];
                    if (option) {
                        const optionDiv = document.createElement('div');
                        optionDiv.className = 'mcq-option';
                        optionDiv.innerHTML = '<span style="margin-right: 8px; font-weight: bold;">' + String.fromCharCode(64 + i) + '.</span> ' + option;
                        optionDiv.style.marginBottom = '8px';
                        optionDiv.style.padding = '5px';
                        optionDiv.style.borderRadius = '4px';
                        optionDiv.style.display = 'block';
                        optionDiv.style.width = '100%';

                        // Highlight correct answer
                        if (i === correctOptionIndex) {
                            optionDiv.style.backgroundColor = 'rgba(76, 175, 80, 0.2)';
                            optionDiv.style.borderLeft = '3px solid #4CAF50';
                        }

                        optionsDiv.appendChild(optionDiv);
                    }
                }
                mcqDiv.appendChild(optionsDiv);

                // Add explanation if available
                if (mcq.answerDescription) {
                    const explanationDiv = document.createElement('div');
                    explanationDiv.className = 'mcq-explanation';
                    explanationDiv.style.marginTop = '10px';
                    explanationDiv.style.padding = '10px';
                    explanationDiv.style.backgroundColor = '#f0f0f0';
                    explanationDiv.style.borderRadius = '4px';
                    explanationDiv.style.borderLeft = '3px solid #3498db';

                    const explanationTitle = document.createElement('div');
                    explanationTitle.style.fontWeight = 'bold';
                    explanationTitle.style.marginBottom = '5px';
                    explanationTitle.textContent = 'Explanation:';
                    explanationDiv.appendChild(explanationTitle);

                    const explanationContent = document.createElement('div');
                    explanationContent.innerHTML = mcq.answerDescription;
                    explanationDiv.appendChild(explanationContent);

                    mcqDiv.appendChild(explanationDiv);
                }

                // Add marks and difficulty level if available
                if (mcq.marks || mcq.difficultyLevel) {
                    const metaDiv = document.createElement('div');
                    metaDiv.className = 'mcq-meta';
                    metaDiv.style.marginTop = '10px';
                    metaDiv.style.fontSize = '14px';
                    metaDiv.style.color = '#666';
                    metaDiv.style.display = 'flex';
                    metaDiv.style.justifyContent = 'flex-end';

                    let metaText = '';

                    if (mcq.marks) {
                        metaText += 'Marks: ' + mcq.marks;
                    }

                    if (mcq.difficultyLevel) {
                        if (metaText) metaText += ' | ';
                        metaText += 'Difficulty: ' + mcq.difficultyLevel;
                    }

                    metaDiv.textContent = metaText;
                    mcqDiv.appendChild(metaDiv);
                }

                mcqContent.appendChild(mcqDiv);
            }
        }

        // Display Q&A
        if (hasQnA) {
            // Create Q&A display
            for (let index = 0; index < data.qna.length; index++) {
                const qa = data.qna[index];
                const qaDiv = document.createElement('div');
                qaDiv.className = 'qa-item';
                qaDiv.style.marginBottom = '30px';
                qaDiv.style.padding = '15px';
                qaDiv.style.backgroundColor = '#f9f9f9';
                qaDiv.style.borderRadius = '8px';
                qaDiv.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';
                qaDiv.style.display = 'block';
                qaDiv.style.width = '100%';

                // Question
                const questionDiv = document.createElement('div');
                questionDiv.className = 'qa-question';
                questionDiv.innerHTML = '<strong>Q' + (index + 1) + ':</strong> ' + (qa.question || qa.ps);
                questionDiv.style.marginBottom = '15px';
                questionDiv.style.fontSize = '16px';
                questionDiv.style.fontWeight = '500';
                qaDiv.appendChild(questionDiv);

                // Answer
                if (qa.answer) {
                    const answerDiv = document.createElement('div');
                    answerDiv.className = 'qa-answer';
                    answerDiv.style.marginLeft = '20px';
                    answerDiv.style.marginBottom = '15px';
                    answerDiv.style.padding = '10px';
                    answerDiv.style.backgroundColor = '#f0f0f0';
                    answerDiv.style.borderRadius = '4px';
                    answerDiv.style.borderLeft = '3px solid #4CAF50';

                    const answerTitle = document.createElement('div');
                    answerTitle.style.fontWeight = 'bold';
                    answerTitle.style.marginBottom = '5px';
                    answerTitle.textContent = 'Answer:';
                    answerDiv.appendChild(answerTitle);

                    const answerContent = document.createElement('div');
                    answerContent.innerHTML = qa.answer;
                    answerDiv.appendChild(answerContent);

                    qaDiv.appendChild(answerDiv);
                }

                // Add marks and difficulty level if available
                if (qa.marks || qa.difficultyLevel) {
                    const metaDiv = document.createElement('div');
                    metaDiv.className = 'qa-meta';
                    metaDiv.style.marginTop = '10px';
                    metaDiv.style.fontSize = '14px';
                    metaDiv.style.color = '#666';
                    metaDiv.style.display = 'flex';
                    metaDiv.style.justifyContent = 'flex-end';

                    let metaText = '';

                    if (qa.marks) {
                        metaText += 'Marks: ' + qa.marks;
                    }

                    if (qa.difficultyLevel) {
                        if (metaText) metaText += ' | ';
                        metaText += 'Difficulty: ' + qa.difficultyLevel;
                    }

                    metaDiv.textContent = metaText;
                    qaDiv.appendChild(metaDiv);
                }

                qnaContent.appendChild(qaDiv);
            }
        }

        // Initialize Lucide icons for the new section
        if (typeof lucide !== 'undefined' && lucide.createIcons) {
            lucide.createIcons();
        }

        // Render math formulas if the function exists
        if (typeof renderMathInElement === 'function') {
            // Render math in MCQ content
            if (hasMCQs) {
                renderMathInElement(mcqContent);
            }

            // Render math in Q&A content
            if (hasQnA) {
                renderMathInElement(qnaContent);
            }
        } else if (typeof MathJax !== 'undefined' && MathJax.Hub && MathJax.Hub.Queue) {
            // Alternative: Use MathJax directly if available
            MathJax.Hub.Queue(["Typeset", MathJax.Hub, questionBankSection]);
        }

        // Scroll to the Question Bank section, but not all the way to the top
        // This ensures the toggle-switch remains visible
        window.scrollTo({
            top: questionBankSection.offsetTop - 100, // Leave space at the top for the toggle-switch
            behavior: 'smooth'
        });
    }
};

// Define setupTabHandlers as a global function
window.setupTabHandlers = function() {
    const mcqTab = document.getElementById('mcq-tab');
    const qnaTab = document.getElementById('qna-tab');
    const mcqContent = document.getElementById('mcq-content');
    const qnaContent = document.getElementById('qna-content');

    if (mcqTab && qnaTab && mcqContent && qnaContent) {
        // Only set up handlers if they haven't been set up already
        if (!mcqTab.hasAttribute('data-handler-set')) {
            mcqTab.addEventListener('click', function() {
                // Activate Objective Types tab
                mcqTab.classList.add('active');
                mcqTab.style.borderBottom = '3px solid #4285f4';
                qnaTab.classList.remove('active');
                qnaTab.style.borderBottom = 'none';

                // Show MCQ content, hide Q&A content
                mcqContent.style.display = 'flex';
                qnaContent.style.display = 'none';

                // Render math in MCQ content if needed
                if (typeof renderMathInElement === 'function') {
                    renderMathInElement(mcqContent);
                }
            });
            mcqTab.setAttribute('data-handler-set', 'true');
        }

        if (!qnaTab.hasAttribute('data-handler-set')) {
            qnaTab.addEventListener('click', function() {
                // Activate Question & Answers tab
                qnaTab.classList.add('active');
                qnaTab.style.borderBottom = '3px solid #4285f4';
                mcqTab.classList.remove('active');
                mcqTab.style.borderBottom = 'none';

                // Show Q&A content, hide MCQ content
                qnaContent.style.display = 'flex';
                mcqContent.style.display = 'none';

                // Render math in Q&A content if needed
                if (typeof renderMathInElement === 'function') {
                    renderMathInElement(qnaContent);
                }
            });
            qnaTab.setAttribute('data-handler-set', 'true');
        }
    }
};

window.displayAnalyticsContent = function(data) {
    console.log("Analytics data:", data);
     document.getElementById("book-analytics").display="block";
    // Get the analytics section elements
    const analyticsSection = document.getElementById('analytics-section');
    const bookAnalyticsView = document.getElementById('book-analytics');
    const chapterAnalyticsView = document.getElementById('chapter-analytics');
    const bookPracticeView = document.getElementById('book-practice');
    const chapterPracticeView = document.getElementById('chapter-practice');
    const bookAnalyticsChart = document.getElementById('bookAnalyticsChart');
    const bookAnalyticsTable = document.getElementById('bookAnalyticsTable').querySelector('tbody');

    // Hide loading overlay
    const bookAnalyticsChartLoading = document.getElementById('bookAnalyticsChartLoading');
    if (bookAnalyticsChartLoading) {
        bookAnalyticsChartLoading.style.display = 'none';
    }

    if (analyticsSection && bookAnalyticsView && bookAnalyticsChart && bookAnalyticsTable) {
        // Make sure the analytics section is visible
        analyticsSection.style.display = 'block';
        bookAnalyticsView.style.display = 'block';
        chapterAnalyticsView.style.display = 'none';
        bookPracticeView.style.display = 'none';
        chapterPracticeView.style.display = 'none';

        // Clear previous content
        bookAnalyticsTable.innerHTML = '';

        // Check if we have any data
        if (!data || !data.chapterData || !data.chapterData.labels || data.chapterData.labels.length === 0) {
            // No data available, show a message
            const noDataDiv = document.createElement('div');
            noDataDiv.style.padding = '30px';
            noDataDiv.style.textAlign = 'center';
            noDataDiv.style.color = '#666';
            noDataDiv.style.fontSize = '16px';
            noDataDiv.innerHTML = '<i data-lucide="info" style="width: 24px; height: 24px; margin-bottom: 10px; color: #3498db;"></i><p>No analytics data available for this book.</p>';

            bookAnalyticsView.innerHTML = '';
            bookAnalyticsView.appendChild(noDataDiv);

            // Initialize Lucide icons
            if (typeof lucide !== 'undefined' && lucide.createIcons) {
                lucide.createIcons();
            }

            return;
        }

        // Create chart if Chart.js is available
        if (typeof Chart !== 'undefined') {
            // Destroy previous chart if it exists
            if (window.bookChart) {
                window.bookChart.destroy();
            }

            // Create new chart
            window.bookChart = new Chart(bookAnalyticsChart, {
                type: 'pie',
                data: {
                    labels: data.chapterData.labels,
                    datasets: [{
                        label: 'Interactions',
                        data: data.chapterData.data,
                        backgroundColor: [
                            'rgba(54, 162, 235, 0.9)',
                            'rgba(255, 99, 132, 0.9)',
                            'rgba(255, 206, 86, 0.9)',
                            'rgba(75, 192, 192, 0.9)',
                            'rgba(153, 102, 255, 0.9)',
                            'rgba(255, 159, 64, 0.9)',
                            'rgba(199, 199, 199, 0.9)',
                            'rgba(83, 102, 255, 0.9)',
                            'rgba(40, 159, 64, 0.9)',
                            'rgba(210, 199, 199, 0.9)'
                        ],
                        borderColor: [
                            'rgba(54, 162, 235, 1)',
                            'rgba(255, 99, 132, 1)',
                            'rgba(255, 206, 86, 1)',
                            'rgba(75, 192, 192, 1)',
                            'rgba(153, 102, 255, 1)',
                            'rgba(255, 159, 64, 1)',
                            'rgba(199, 199, 199, 1)',
                            'rgba(83, 102, 255, 1)',
                            'rgba(40, 159, 64, 1)',
                            'rgba(210, 199, 199, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Chapter Interactions',
                            font: {
                                size: 16
                            }
                        },
                        legend: {
                            position: 'right'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.raw || 0;
                                    const total = data.chapterData.total;
                                    let percentage = 0;
                                    if (total > 0) {
                                        percentage = Math.round((value / total) * 100);
                                    }
                                    return label + ': ' + value + ' (' + percentage + '%)';
                                }
                            }
                        }
                    }
                }
            });
        } else {
            console.error('Chart.js is not available');
            // Show a message that Chart.js is not available
            bookAnalyticsChart.innerHTML = '<div style="padding: 20px; text-align: center;"><p>Chart.js is not available. Please include Chart.js to view analytics charts.</p></div>';
        }

        // Populate the table
        data.chapterDetails.forEach(function(chapter, index) {
            const row = document.createElement('tr');

            // Chapter name
            const nameCell = document.createElement('td');
            nameCell.textContent = chapter.name;
            row.appendChild(nameCell);

            // Interaction count
            const countCell = document.createElement('td');
            countCell.textContent = chapter.count;
            row.appendChild(countCell);

            // Percentage
            const percentageCell = document.createElement('td');
            let percentage = 0;
            if (data.chapterData.total > 0) {
                percentage = Math.round((chapter.count / data.chapterData.total) * 100);
            }
            percentageCell.textContent = percentage + '%';
            row.appendChild(percentageCell);

            // Action button
            const actionCell = document.createElement('td');
            const viewButton = document.createElement('button');
            viewButton.className = 'btn btn-sm btn-primary';
            viewButton.textContent = 'View Details';
            viewButton.onclick = function() {
                drillDownToChapter(chapter.id, chapter.name);
            };
            actionCell.appendChild(viewButton);
            row.appendChild(actionCell);

            bookAnalyticsTable.appendChild(row);
        });

        // Scroll to the Analytics section
        window.scrollTo({
            top: analyticsSection.offsetTop - 100,
            behavior: 'smooth'
        });

        // If we have practice data, load it
        if (data.practiceData) {
            loadBookPracticeData(data.practiceData);
        } else {
            // If no practice data is available, fetch it separately
            getBookPracticeData();
        }
    }
};

// Function to get book practice data
function getBookPracticeData() {
    // Get filter values
    const fromDate = document.getElementById('analyticsFromDate') ? document.getElementById('analyticsFromDate').value : null;
    const toDate = document.getElementById('analyticsToDate') ? document.getElementById('analyticsToDate').value : null;
    const batchId = document.getElementById('analyticsBatchFilter') ? document.getElementById('analyticsBatchFilter').value : null;
    const isDemoMode = document.getElementById('analyticsDemoMode') ? document.getElementById('analyticsDemoMode').checked : false;

    // Get the bookId from the URL
    let bookId;
    try {
        // Try to get bookId from URL parameter
        const urlParams = new URLSearchParams(window.location.search);
        bookId = urlParams.get('bookId');
        console.log('Practice: Got bookId from URL:', bookId);
    } catch (e) {
        console.error('Practice: Error getting bookId from URL:', e);
    }

    // If bookId is not in URL, try to get it from global variable
    if (!bookId && typeof window.bookId !== 'undefined') {
        bookId = window.bookId;
        console.log('Practice: Got bookId from global variable:', bookId);
    }

    // If still no bookId, log error and return
    if (!bookId) {
        console.error('Practice: No bookId found');
        return;
    }

    // Build parameters string
    let paramsStr = `bookId=${bookId}`;
    if (fromDate) paramsStr += `&fromDate=${fromDate}`;
    if (toDate) paramsStr += `&toDate=${toDate}`;
    if (batchId) paramsStr += `&batchId=${batchId}`;
    // Only add demo parameter if explicitly checked
    if (isDemoMode) paramsStr += `&showReport=demo`;

    // Show loading overlay
    const bookPracticeChartLoading = document.getElementById('bookPracticeChartLoading');
    const bookPracticeTrendsChartLoading = document.getElementById('bookPracticeTrendsChartLoading');
    if (bookPracticeChartLoading) bookPracticeChartLoading.style.display = 'flex';
    if (bookPracticeTrendsChartLoading) bookPracticeTrendsChartLoading.style.display = 'flex';


}

// Function to load book practice data
function loadBookPracticeData(data) {
    console.log('Loading book practice data:', data);

    // Get the practice dashboard elements
    const bookPracticeView = document.getElementById('book-practice');
    let bookPracticeChart = document.getElementById('bookPracticeChart');
    let bookPracticeTrendsChart = document.getElementById('bookPracticeTrendsChart');
    const bookPracticeTable = document.getElementById('bookPracticeTable').querySelector('tbody');

    // Make sure the practice view is visible
    if (bookPracticeView) {
        bookPracticeView.style.display = 'block';
    }

    // Check if data is empty or null
    const noData = !data || !data.summary || data.summary.totalQuestions === 0;

    // Show/hide content based on data availability
    if (noData) {
        // Hide charts and tables
        $('#bookPracticeChart').closest('.chart-container').hide();
        $('#bookPracticeTrendsChart').closest('.chart-container').hide();
        $('#bookPracticeTable').closest('.table-responsive').hide();
        $('.col-md-12.mb-4').hide(); // Hide summary cards

        // Show no data message
        $('#practiceNoDataMessage').show();
        return;
    } else {
        // Show charts and tables
        $('#bookPracticeChart').closest('.chart-container').show();
        $('#bookPracticeTrendsChart').closest('.chart-container').show();
        $('#bookPracticeTable').closest('.table-responsive').show();
        $('.col-md-12.mb-4').show(); // Show summary cards

        // Hide no data message
        $('#practiceNoDataMessage').hide();
    }

    // Reset canvas elements to ensure clean chart rendering
    if (bookPracticeChart) {
        // Replace the canvas with a fresh one
        const chartContainer = bookPracticeChart.parentNode;
        const oldCanvas = bookPracticeChart;
        const newCanvas = document.createElement('canvas');
        newCanvas.id = 'bookPracticeChart';
        chartContainer.replaceChild(newCanvas, oldCanvas);
        bookPracticeChart = newCanvas;
    }

    if (bookPracticeTrendsChart) {
        // Replace the canvas with a fresh one
        const trendsContainer = bookPracticeTrendsChart.parentNode;
        const oldTrendsCanvas = bookPracticeTrendsChart;
        const newTrendsCanvas = document.createElement('canvas');
        newTrendsCanvas.id = 'bookPracticeTrendsChart';
        trendsContainer.replaceChild(newTrendsCanvas, oldTrendsCanvas);
        bookPracticeTrendsChart = newTrendsCanvas;
    }

    // Hide loading overlays
    const bookPracticeChartLoading = document.getElementById('bookPracticeChartLoading');
    const bookPracticeTrendsChartLoading = document.getElementById('bookPracticeTrendsChartLoading');
    if (bookPracticeChartLoading) bookPracticeChartLoading.style.display = 'none';
    if (bookPracticeTrendsChartLoading) bookPracticeTrendsChartLoading.style.display = 'none';

    if (bookPracticeView && bookPracticeChart && bookPracticeTrendsChart && bookPracticeTable) {
        // Update summary cards
        document.getElementById('bookPracticeTotalQuestions').textContent = data.summary.totalQuestions;
        document.getElementById('bookPracticeCorrectAnswers').textContent = data.summary.totalCorrect;
        document.getElementById('bookPracticeAccuracy').textContent = data.summary.overallAccuracy + '%';

        // Clear previous table content
        bookPracticeTable.innerHTML = '';

        // Create chapter performance chart if Chart.js is available
        if (typeof Chart !== 'undefined') {
            // Destroy previous chart if it exists and is a valid Chart.js instance
            if (window.bookPracticeChart && typeof window.bookPracticeChart.destroy === 'function') {
                window.bookPracticeChart.destroy();
            }

            // Prepare data for the chart
            const chapterNames = data.chapterDetails.map(chapter => chapter.name);
            const questionCounts = data.chapterDetails.map(chapter => chapter.questionCount);
            const correctCounts = data.chapterDetails.map(chapter => chapter.correctCount);

            // Create new chart
            window.bookPracticeChart = new Chart(bookPracticeChart, {
                type: 'bar',
                data: {
                    labels: chapterNames,
                    datasets: [
                        {
                            label: 'Total Questions',
                            data: questionCounts,
                            backgroundColor: 'rgba(54, 162, 235, 0.9)',
                            borderColor: 'rgba(54, 162, 235, 1)',
                            borderWidth: 1
                        },
                        {
                            label: 'Correct Answers',
                            data: correctCounts,
                            backgroundColor: 'rgba(75, 192, 192, 0.9)',
                            borderColor: 'rgba(75, 192, 192, 1)',
                            borderWidth: 1
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Chapter Performance',
                            font: {
                                size: 16
                            }
                        },
                        legend: {
                            position: 'top'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Count'
                            }
                        }
                    }
                }
            });

            // Create trends chart
            if (window.bookPracticeTrendsChart && typeof window.bookPracticeTrendsChart.destroy === 'function') {
                window.bookPracticeTrendsChart.destroy();
            }

            // Create new trends chart
            window.bookPracticeTrendsChart = new Chart(bookPracticeTrendsChart, {
                type: 'line',
                data: {
                    labels: data.timeSeriesData.labels,
                    datasets: [
                        {
                            label: 'Practice Count',
                            data: data.timeSeriesData.practiceData,
                            backgroundColor: 'rgba(54, 162, 235, 0.6)',
                            borderColor: 'rgba(54, 162, 235, 1)',
                            borderWidth: 2,
                            yAxisID: 'y',
                            tension: 0.3
                        },
                        {
                            label: 'Accuracy (%)',
                            data: data.timeSeriesData.accuracyData,
                            backgroundColor: 'rgba(255, 99, 132, 0.6)',
                            borderColor: 'rgba(255, 99, 132, 1)',
                            borderWidth: 2,
                            yAxisID: 'y1',
                            tension: 0.3
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Practice Trends',
                            font: {
                                size: 16
                            }
                        }
                    },
                    scales: {
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            title: {
                                display: true,
                                text: 'Practice Count'
                            }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            title: {
                                display: true,
                                text: 'Accuracy (%)'
                            },
                            min: 0,
                            max: 100,
                            grid: {
                                drawOnChartArea: false
                            }
                        }
                    }
                }
            });
        } else {
            console.error('Chart.js is not available');
            bookPracticeChart.innerHTML = '<div style="padding: 20px; text-align: center;"><p>Chart.js is not available. Please include Chart.js to view practice charts.</p></div>';
            bookPracticeTrendsChart.innerHTML = '<div style="padding: 20px; text-align: center;"><p>Chart.js is not available. Please include Chart.js to view practice charts.</p></div>';
        }

        // Populate the table
        data.chapterDetails.forEach(function (chapter) {
            const row = document.createElement('tr');

            // Chapter name
            const nameCell = document.createElement('td');
            nameCell.textContent = chapter.name;
            row.appendChild(nameCell);

            // Question count
            const questionCell = document.createElement('td');
            questionCell.textContent = chapter.questionCount;
            row.appendChild(questionCell);

            // Correct count
            const correctCell = document.createElement('td');
            correctCell.textContent = chapter.correctCount;
            row.appendChild(correctCell);

            // Accuracy
            const accuracyCell = document.createElement('td');
            accuracyCell.textContent = chapter.accuracy + '%';
            row.appendChild(accuracyCell);

            // Action button
            const actionCell = document.createElement('td');
            const viewButton = document.createElement('button');
            viewButton.className = 'btn btn-sm btn-primary';
            viewButton.textContent = 'View Details';
            viewButton.onclick = function () {
                drillDownToChapterPractice(chapter.id, chapter.name);
            };
            actionCell.appendChild(viewButton);
            row.appendChild(actionCell);

            bookPracticeTable.appendChild(row);
        });
    }
}
// Function to drill down to chapter practice
    function drillDownToChapterPractice(chapterId, chapterName) {
        console.log("Drilling down to chapter practice:", chapterId, chapterName);

        // Get the analytics views
        const bookAnalyticsView = document.getElementById('book-analytics');
        const chapterAnalyticsView = document.getElementById('chapter-analytics');
        const bookPracticeView = document.getElementById('book-practice');
        const chapterPracticeView = document.getElementById('chapter-practice');
        const chapterPracticeTitle = document.getElementById('chapterPracticeTitle');

        if (bookPracticeView && chapterPracticeView && chapterPracticeTitle) {
            // Hide book view, show chapter view
            bookAnalyticsView.style.display = 'none';
            chapterAnalyticsView.style.display = 'none';
            bookPracticeView.style.display = 'none';
            chapterPracticeView.style.display = 'block';

            // Set chapter title
            chapterPracticeTitle.textContent = 'Practice Dashboard for ' + chapterName;

            // Show loading overlays
            const chapterPracticeChartLoading = document.getElementById('chapterPracticeChartLoading');
            const chapterPracticeTrendsChartLoading = document.getElementById('chapterPracticeTrendsChartLoading');
            if (chapterPracticeChartLoading) chapterPracticeChartLoading.style.display = 'flex';
            if (chapterPracticeTrendsChartLoading) chapterPracticeTrendsChartLoading.style.display = 'flex';

            // Get filter values
            const fromDate = document.getElementById('analyticsFromDate') ? document.getElementById('analyticsFromDate').value : null;
            const toDate = document.getElementById('analyticsToDate') ? document.getElementById('analyticsToDate').value : null;
            const batchId = document.getElementById('analyticsBatchFilter') ? document.getElementById('analyticsBatchFilter').value : null;
            const isDemoMode = document.getElementById('analyticsDemoMode') ? document.getElementById('analyticsDemoMode').checked : false;

            // Get the bookId from the URL
            let bookId;
            try {
                // Try to get bookId from URL parameter
                const urlParams = new URLSearchParams(window.location.search);
                bookId = urlParams.get('bookId');
                console.log('Chapter Practice: Got bookId from URL:', bookId);
            } catch (e) {
                console.error('Chapter Practice: Error getting bookId from URL:', e);
            }

            // If bookId is not in URL, try to get it from global variable
            if (!bookId && typeof window.bookId !== 'undefined') {
                bookId = window.bookId;
                console.log('Chapter Practice: Got bookId from global variable:', bookId);
            }

            // Build parameters string
            let paramsStr = `chapterId=${chapterId}`;
            if (bookId) paramsStr += `&bookId=${bookId}`;
            if (fromDate) paramsStr += `&fromDate=${fromDate}`;
            if (toDate) paramsStr += `&toDate=${toDate}`;
            if (batchId) paramsStr += `&batchId=${batchId}`;
            if (isDemoMode) paramsStr += `&showReport=demo`;

            // Fetch chapter practice data with filter parameters
            $.ajax({
                url: '${createLink(controller: 'bookAnalytics', action: 'getChapterPracticeData')}',
                data: paramsStr,
                success: function (data) {
                    console.log('Chapter Practice Response:', data);
                    if (data.status === 'success') {
                        displayChapterPracticeData(data.data);
                    } else {
                        console.error('Error loading chapter practice data:', data.message);
                    }
                },
                error: function (xhr, status, error) {
                    console.error('Error calling Chapter Practice API:', error);
                    console.error('Status:', status);
                    console.error('Response:', xhr.responseText);
                }
            });
        }
    }

// Function to display chapter practice data
    function displayChapterPracticeData(data) {
        console.log("Chapter practice data:", data);

        let chapterPracticeChart = document.getElementById('chapterPracticeChart');
        let chapterPracticeTrendsChart = document.getElementById('chapterPracticeTrendsChart');
        const chapterPracticeTable = document.getElementById('chapterPracticeTable').querySelector('tbody');
        const chapterPracticeView = document.getElementById('chapter-practice');

        // Make sure the chapter practice view is visible
        if (chapterPracticeView) {
            chapterPracticeView.style.display = 'block';
        }

        // Check if data is empty or null
        const noData = !data || !data.summary || data.summary.totalQuestions === 0;

        // Show/hide content based on data availability
        if (noData) {
            // Hide charts and tables
            $('#chapterPracticeChart').closest('.chart-container').hide();
            $('#chapterPracticeTrendsChart').closest('.chart-container').hide();
            $('#chapterPracticeTable').closest('.table-responsive').hide();
            $('.col-md-12.mb-4').hide(); // Hide summary cards

            // Show no data message
            $('#chapterPracticeNoDataMessage').show();
            return;
        } else {
            // Show charts and tables
            $('#chapterPracticeChart').closest('.chart-container').show();
            $('#chapterPracticeTrendsChart').closest('.chart-container').show();
            $('#chapterPracticeTable').closest('.table-responsive').show();
            $('.col-md-12.mb-4').show(); // Show summary cards

            // Hide no data message
            $('#chapterPracticeNoDataMessage').hide();
        }

        // Reset canvas elements to ensure clean chart rendering
        if (chapterPracticeChart) {
            // Replace the canvas with a fresh one
            const chartContainer = chapterPracticeChart.parentNode;
            const oldCanvas = chapterPracticeChart;
            const newCanvas = document.createElement('canvas');
            newCanvas.id = 'chapterPracticeChart';
            chartContainer.replaceChild(newCanvas, oldCanvas);
            chapterPracticeChart = newCanvas;
        }

        if (chapterPracticeTrendsChart) {
            // Replace the canvas with a fresh one
            const trendsContainer = chapterPracticeTrendsChart.parentNode;
            const oldTrendsCanvas = chapterPracticeTrendsChart;
            const newTrendsCanvas = document.createElement('canvas');
            newTrendsCanvas.id = 'chapterPracticeTrendsChart';
            trendsContainer.replaceChild(newTrendsCanvas, oldTrendsCanvas);
            chapterPracticeTrendsChart = newTrendsCanvas;
        }

        // Hide loading overlays
        const chapterPracticeChartLoading = document.getElementById('chapterPracticeChartLoading');
        const chapterPracticeTrendsChartLoading = document.getElementById('chapterPracticeTrendsChartLoading');
        if (chapterPracticeChartLoading) chapterPracticeChartLoading.style.display = 'none';
        if (chapterPracticeTrendsChartLoading) chapterPracticeTrendsChartLoading.style.display = 'none';

        if (chapterPracticeChart && chapterPracticeTrendsChart && chapterPracticeTable) {
            // Update summary cards
            document.getElementById('chapterPracticeTotalQuestions').textContent = data.summary.totalQuestions;
            document.getElementById('chapterPracticeCorrectAnswers').textContent = data.summary.totalCorrect;
            document.getElementById('chapterPracticeAccuracy').textContent = data.summary.overallAccuracy + '%';

            // Clear previous table content
            chapterPracticeTable.innerHTML = '';

            // Create question type chart if Chart.js is available
            if (typeof Chart !== 'undefined') {
                // Destroy previous chart if it exists and is a valid Chart.js instance
                if (window.chapterPracticeChart && typeof window.chapterPracticeChart.destroy === 'function') {
                    window.chapterPracticeChart.destroy();
                }

                // Create new chart
                window.chapterPracticeChart = new Chart(chapterPracticeChart, {
                    type: 'doughnut',
                    data: {
                        labels: data.questionTypeData.labels,
                        datasets: [{
                            label: 'Question Types',
                            data: data.questionTypeData.data,
                            backgroundColor: [
                                'rgba(255, 99, 132, 0.9)',
                                'rgba(54, 162, 235, 0.9)',
                                'rgba(255, 206, 86, 0.9)',
                                'rgba(75, 192, 192, 0.9)',
                                'rgba(153, 102, 255, 0.9)'
                            ],
                            borderColor: [
                                'rgba(255, 99, 132, 1)',
                                'rgba(54, 162, 235, 1)',
                                'rgba(255, 206, 86, 1)',
                                'rgba(75, 192, 192, 1)',
                                'rgba(153, 102, 255, 1)'
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: 'Question Type Distribution',
                                font: {
                                    size: 16
                                }
                            },
                            legend: {
                                position: 'right'
                            }
                        }
                    }
                });

                // Create trends chart
                if (window.chapterPracticeTrendsChart && typeof window.chapterPracticeTrendsChart.destroy === 'function') {
                    window.chapterPracticeTrendsChart.destroy();
                }

                // Create new trends chart
                window.chapterPracticeTrendsChart = new Chart(chapterPracticeTrendsChart, {
                    type: 'line',
                    data: {
                        labels: data.timeSeriesData.labels,
                        datasets: [
                            {
                                label: 'Practice Count',
                                data: data.timeSeriesData.practiceData,
                                backgroundColor: 'rgba(54, 162, 235, 0.6)',
                                borderColor: 'rgba(54, 162, 235, 1)',
                                borderWidth: 2,
                                yAxisID: 'y',
                                tension: 0.3
                            },
                            {
                                label: 'Accuracy (%)',
                                data: data.timeSeriesData.accuracyData,
                                backgroundColor: 'rgba(255, 99, 132, 0.6)',
                                borderColor: 'rgba(255, 99, 132, 1)',
                                borderWidth: 2,
                                yAxisID: 'y1',
                                tension: 0.3
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: 'Practice Trends',
                                font: {
                                    size: 16
                                }
                            }
                        },
                        scales: {
                            y: {
                                type: 'linear',
                                display: true,
                                position: 'left',
                                title: {
                                    display: true,
                                    text: 'Practice Count'
                                }
                            },
                            y1: {
                                type: 'linear',
                                display: true,
                                position: 'right',
                                title: {
                                    display: true,
                                    text: 'Accuracy (%)'
                                },
                                min: 0,
                                max: 100,
                                grid: {
                                    drawOnChartArea: false
                                }
                            }
                        }
                    }
                });
            } else {
                console.error('Chart.js is not available');
                chapterPracticeChart.innerHTML = '<div style="padding: 20px; text-align: center;"><p>Chart.js is not available. Please include Chart.js to view practice charts.</p></div>';
                chapterPracticeTrendsChart.innerHTML = '<div style="padding: 20px; text-align: center;"><p>Chart.js is not available. Please include Chart.js to view practice charts.</p></div>';
            }

            // Populate the table
            data.topicDetails.forEach(function (topic) {
                const row = document.createElement('tr');

                // Topic name
                const nameCell = document.createElement('td');
                nameCell.textContent = topic.name;
                row.appendChild(nameCell);

                // Question count
                const questionCell = document.createElement('td');
                questionCell.textContent = topic.questionCount;
                row.appendChild(questionCell);

                // Correct count
                const correctCell = document.createElement('td');
                correctCell.textContent = topic.correctCount;
                row.appendChild(correctCell);

                // Accuracy
                const accuracyCell = document.createElement('td');
                accuracyCell.textContent = topic.accuracy + '%';
                row.appendChild(accuracyCell);

                chapterPracticeTable.appendChild(row);
            });
        }
    };

// Function to drill down to chapter level analytics
    function drillDownToChapter(chapterId, chapterName) {
        console.log("Drilling down to chapter:", chapterId, chapterName);

        // Get the analytics views
        const bookAnalyticsView = document.getElementById('book-analytics');
        const chapterAnalyticsView = document.getElementById('chapter-analytics');
        const chapterAnalyticsTitle = document.getElementById('chapterAnalyticsTitle');
        const chapterAnalyticsChart = document.getElementById('chapterAnalyticsChart');
        const chapterAnalyticsTable = document.getElementById('chapterAnalyticsTable').querySelector('tbody');

        if (bookAnalyticsView && chapterAnalyticsView && chapterAnalyticsTitle && chapterAnalyticsChart && chapterAnalyticsTable) {
            // Hide book view, show chapter view
            bookAnalyticsView.style.display = 'none';
            chapterAnalyticsView.style.display = 'block';

            // Set chapter title
            chapterAnalyticsTitle.textContent = 'Analytics for ' + chapterName;

            // Clear previous content
            chapterAnalyticsTable.innerHTML = '';

            // Show loading overlay
            const chapterAnalyticsChartLoading = document.getElementById('chapterAnalyticsChartLoading');
            if (chapterAnalyticsChartLoading) {
                chapterAnalyticsChartLoading.style.display = 'flex';
            }

            // Get filter values
            const fromDate = document.getElementById('analyticsFromDate') ? document.getElementById('analyticsFromDate').value : null;
            const toDate = document.getElementById('analyticsToDate') ? document.getElementById('analyticsToDate').value : null;
            const batchId = document.getElementById('analyticsBatchFilter') ? document.getElementById('analyticsBatchFilter').value : null;
            const isDemoMode = document.getElementById('analyticsDemoMode') ? document.getElementById('analyticsDemoMode').checked : false;

            console.log('Drill down - Demo mode checked:', isDemoMode);

            // Construct the params string manually for better control
            let paramsStr = 'chapterId=' + chapterId;
            if (fromDate) paramsStr += '&fromDate=' + fromDate;
            if (toDate) paramsStr += '&toDate=' + toDate;
            if (batchId) paramsStr += '&batchId=' + batchId;
            if (isDemoMode) paramsStr += '&showReport=demo';

            console.log('Drill down - Params string:', paramsStr);

            // Fetch chapter analytics data with filter parameters
            // Instead of using g:remoteFunction with a JavaScript variable, we'll use jQuery.ajax directly
            $.ajax({
                url: '${createLink(controller: 'bookAnalytics', action: 'getChapterAnalytics')}',
                data: paramsStr,
                success: function (data) {
                    console.log('Chapter Response:', data);
                    displayChapterAnalytics(data.data);
                },
                error: function (xhr, status, error) {
                    console.error('Error calling Chapter API:', error);
                    console.error('Status:', status);
                    console.error('Response:', xhr.responseText);
                }
            });
        }
    }

// Function to display chapter analytics
    function displayChapterAnalytics(data) {
        console.log("Chapter analytics data:", data);

        const chapterAnalyticsChart = document.getElementById('chapterAnalyticsChart');
        const chapterAnalyticsTable = document.getElementById('chapterAnalyticsTable').querySelector('tbody');
        const chapterAnalyticsTitle = document.getElementById('chapterAnalyticsTitle');

        // Hide loading overlay
        const chapterAnalyticsChartLoading = document.getElementById('chapterAnalyticsChartLoading');
        if (chapterAnalyticsChartLoading) {
            chapterAnalyticsChartLoading.style.display = 'none';
        }

        if (chapterAnalyticsChart && chapterAnalyticsTable && chapterAnalyticsTitle) {
            // Update chapter title
            if (data.chapterInfo) {
                chapterAnalyticsTitle.textContent = 'Analytics for ' + data.chapterInfo.name;
            }

            // Clear previous content
            chapterAnalyticsTable.innerHTML = '';

            // Create chart if Chart.js is available
            if (typeof Chart !== 'undefined') {
                // Destroy previous chart if it exists
                if (window.chapterChart) {
                    window.chapterChart.destroy();
                }

                // Create new chart
                window.chapterChart = new Chart(chapterAnalyticsChart, {
                    type: 'line',
                    data: {
                        labels: data.timeData.labels,
                        datasets: [{
                            label: 'Interactions Over Time',
                            data: data.timeData.data,
                            backgroundColor: 'rgba(54, 162, 235, 0.6)',
                            borderColor: 'rgba(54, 162, 235, 1)',
                            borderWidth: 2,
                            tension: 0.3,
                            fill: true
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: 'Interactions Over Time',
                                font: {
                                    size: 16
                                }
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                title: {
                                    display: true,
                                    text: 'Count'
                                }
                            }
                        }
                    }
                });

                // Create prompt type chart if data is available
                if (data.promptTypeData && data.promptTypeData.labels && data.promptTypeData.labels.length > 0) {
                    // Create a container for the prompt type chart
                    const promptChartContainer = document.createElement('div');
                    promptChartContainer.className = 'chart-container mt-4';
                    promptChartContainer.style.height = '250px';

                    const promptCanvas = document.createElement('canvas');
                    promptCanvas.id = 'promptTypeChart';
                    promptChartContainer.appendChild(promptCanvas);

                    // Add it after the main chart
                    chapterAnalyticsChart.parentNode.appendChild(promptChartContainer);

                    // Create the chart
                    window.promptChart = new Chart(promptCanvas, {
                        type: 'doughnut',
                        data: {
                            labels: data.promptTypeData.labels,
                            datasets: [{
                                label: 'Prompt Types',
                                data: data.promptTypeData.data,
                                backgroundColor: [
                                    'rgba(255, 99, 132, 0.9)',
                                    'rgba(54, 162, 235, 0.9)',
                                    'rgba(255, 206, 86, 0.9)',
                                    'rgba(75, 192, 192, 0.9)',
                                    'rgba(153, 102, 255, 0.9)',
                                    'rgba(255, 159, 64, 0.9)'
                                ],
                                borderColor: [
                                    'rgba(255, 99, 132, 1)',
                                    'rgba(54, 162, 235, 1)',
                                    'rgba(255, 206, 86, 1)',
                                    'rgba(75, 192, 192, 1)',
                                    'rgba(153, 102, 255, 1)',
                                    'rgba(255, 159, 64, 1)'
                                ],
                                borderWidth: 1
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                title: {
                                    display: true,
                                    text: 'Prompt Type Distribution',
                                    font: {
                                        size: 16
                                    }
                                },
                                legend: {
                                    position: 'right'
                                }
                            }
                        }
                    });
                }
            } else {
                console.error('Chart.js is not available');
                // Show a message that Chart.js is not available
                chapterAnalyticsChart.innerHTML = '<div style="padding: 20px; text-align: center;"><p>Chart.js is not available. Please include Chart.js to view analytics charts.</p></div>';
            }

            // Populate the table
            if (data.metrics) {
                data.metrics.forEach(function (metric) {
                    const row = document.createElement('tr');

                    // Metric name
                    const nameCell = document.createElement('td');
                    nameCell.textContent = metric.name;
                    row.appendChild(nameCell);

                    // Metric value
                    const valueCell = document.createElement('td');
                    valueCell.textContent = metric.value;
                    row.appendChild(valueCell);

                    chapterAnalyticsTable.appendChild(row);
                });
            }

            // Add resource details if available
            if (data.resourceDetails && data.resourceDetails.length > 0) {
                // Create a container for resource details
                const resourceContainer = document.createElement('div');
                resourceContainer.className = 'resource-details mt-4';
                resourceContainer.innerHTML = '<h5>Resource Details</h5>';

                const resourceTable = document.createElement('table');
                resourceTable.className = 'table table-striped data-table';
                resourceTable.innerHTML = `
                <thead>
                    <tr>
                        <th>Resource Name</th>
                        <th>Type</th>
                        <th>Views</th>
                        <th>GPT Interactions</th>
                    </tr>
                </thead>
                <tbody></tbody>
            `;

                const resourceTableBody = resourceTable.querySelector('tbody');

                // Add top 5 resources by total interactions
                data.resourceDetails
                    .sort((a, b) => b.totalInteractions - a.totalInteractions)
                    .slice(0, 5)
                    .forEach(function (resource) {
                        const row = document.createElement('tr');

                        // Resource name
                        const nameCell = document.createElement('td');
                        nameCell.textContent = resource.name;
                        row.appendChild(nameCell);

                        // Resource type
                        const typeCell = document.createElement('td');
                        typeCell.textContent = resource.type;
                        row.appendChild(typeCell);

                        // View count
                        const viewCell = document.createElement('td');
                        viewCell.textContent = resource.viewCount;
                        row.appendChild(viewCell);

                        // GPT count
                        const gptCell = document.createElement('td');
                        gptCell.textContent = resource.gptCount;
                        row.appendChild(gptCell);

                        resourceTableBody.appendChild(row);
                    });

                resourceContainer.appendChild(resourceTable);

                // Add it after the metrics table
                chapterAnalyticsTable.parentNode.parentNode.appendChild(resourceContainer);
            }
        }
    }

// Set up analytics filters and handlers
    document.addEventListener('DOMContentLoaded', function () {
        // Check if the demo mode checkbox exists and log its state
        const demoModeCheckbox = document.getElementById('analyticsDemoMode');
        if (demoModeCheckbox) {
            console.log('Demo mode checkbox found, initial state:', demoModeCheckbox.checked);

            // Add an event listener to log when the checkbox state changes
            demoModeCheckbox.addEventListener('change', function () {
                console.log('Demo mode checkbox changed to:', this.checked);
            });
        } else {
            console.log('Demo mode checkbox not found');
        }

        // Add direct click handler to Analytics buttons
        const analyticsButtons = document.querySelectorAll('.ai-prompt-btn.analytics');
        console.log('Found Analytics buttons:', analyticsButtons.length);
        analyticsButtons.forEach(function (button) {
            console.log('Found Analytics button, adding direct click handler');
            button.addEventListener('click', function (e) {
                console.log('Analytics button clicked directly');
                e.preventDefault();
                e.stopPropagation(); // Stop event propagation
                try {
                    if (typeof window.getBookAnalytics === 'function') {
                        console.log('Calling getBookAnalytics function');
                        window.getBookAnalytics();
                    } else {
                        console.error('getBookAnalytics is not a function');
                    }
                } catch (error) {
                    console.error('Error calling getBookAnalytics:', error);
                }
            });
        });
        // Set up back button handlers
        const backToBookAnalyticsBtn = document.getElementById('backToBookAnalytics');
        if (backToBookAnalyticsBtn) {
            backToBookAnalyticsBtn.addEventListener('click', function () {
                const bookAnalyticsView = document.getElementById('book-analytics');
                const chapterAnalyticsView = document.getElementById('chapter-analytics');

                if (bookAnalyticsView && chapterAnalyticsView) {
                    bookAnalyticsView.style.display = 'block';
                    chapterAnalyticsView.style.display = 'none';
                }
            });
        }

        const backToBookPracticeBtn = document.getElementById('backToBookPractice');
        if (backToBookPracticeBtn) {
            backToBookPracticeBtn.addEventListener('click', function () {
                const bookPracticeView = document.getElementById('book-practice');
                const chapterPracticeView = document.getElementById('chapter-practice');

                if (bookPracticeView && chapterPracticeView) {
                    bookPracticeView.style.display = 'block';
                    chapterPracticeView.style.display = 'none';
                }
            });
        }

        // Add tab navigation for Analytics and Practice
        const analyticsSection = document.getElementById('analytics-section');
        if (analyticsSection) {
            // Create tab navigation if it doesn't exist
            if (!document.getElementById('analytics-tabs')) {
                const tabsContainer = document.createElement('div');
                tabsContainer.id = 'analytics-tabs';
                tabsContainer.className = 'analytics-tabs';
                tabsContainer.style.display = 'flex';
                tabsContainer.style.marginBottom = '15px';
                tabsContainer.style.borderBottom = '1px solid #ddd';

                // Create Analytics tab
                const analyticsTab = document.createElement('div');
                analyticsTab.id = 'analytics-tab';
                analyticsTab.className = 'analytics-tab active';
                analyticsTab.style.padding = '10px 20px';
                analyticsTab.style.cursor = 'pointer';
                analyticsTab.style.fontWeight = '500';
                analyticsTab.style.borderBottom = '3px solid #4285f4';
                analyticsTab.textContent = 'Usage Analytics';
                analyticsTab.onclick = function () {
                    // Show Analytics, hide Practice
                    document.getElementById('book-analytics').style.display = 'block';
                    document.getElementById('chapter-analytics').style.display = 'none';
                    document.getElementById('book-practice').style.display = 'none';
                    document.getElementById('chapter-practice').style.display = 'none';
                    //hide teaching coach
                    document.getElementById('teaching-coach-section').style.display = 'none';

                    // Update tab styles
                    analyticsTab.style.borderBottom = '3px solid #4285f4';
                    practiceTab.style.borderBottom = 'none';
                    analyticsTab.classList.add('active');
                    practiceTab.classList.remove('active');
                };

                // Create Practice tab
                const practiceTab = document.createElement('div');
                practiceTab.id = 'practice-tab';
                practiceTab.className = 'analytics-tab';
                practiceTab.style.padding = '10px 20px';
                practiceTab.style.cursor = 'pointer';
                practiceTab.style.fontWeight = '500';
                practiceTab.textContent = 'Practice Dashboard';
                practiceTab.onclick = function () {
                    // Show Practice, hide Analytics
                    document.getElementById('book-analytics').style.display = 'none';
                    document.getElementById('chapter-analytics').style.display = 'none';
                    document.getElementById('book-practice').style.display = 'block';
                    document.getElementById('chapter-practice').style.display = 'none';

                    // Update tab styles
                    analyticsTab.style.borderBottom = 'none';
                    practiceTab.style.borderBottom = '3px solid #4285f4';
                    analyticsTab.classList.remove('active');
                    practiceTab.classList.add('active');

                    // Always load fresh practice data when switching to the tab
                    getBookPracticeData();
                };

                // Add tabs to container
                tabsContainer.appendChild(analyticsTab);
                tabsContainer.appendChild(practiceTab);

                // Insert tabs after the header
                const analyticsHeader = analyticsSection.querySelector('.ai-section-header');
                if (analyticsHeader) {
                    analyticsHeader.insertAdjacentElement('afterend', tabsContainer);
                }
            }
        }

        // Load batches for the user
        const analyticsBatchFilter = document.getElementById('analyticsBatchFilter');
        if (analyticsBatchFilter) {
            // Load batches using AJAX
            <g:remoteFunction controller="bookAnalytics" action="getBatchesForUser" onSuccess="loadBatchesForAnalytics(data);"/>
        }

        // Function to load batches into the dropdown
        window.loadBatchesForAnalytics = function (data) {
            if (data.status === 'success' && data.batches) {
                const batchFilter = document.getElementById('analyticsBatchFilter');
                if (batchFilter) {
                    // Clear existing options except the first one
                    while (batchFilter.options.length > 1) {
                        batchFilter.remove(1);
                    }

                    // Add new options
                    data.batches.forEach(function (batch) {
                        if (batch.id !== 'personal') { // Skip 'personal' as it's already added
                            const option = document.createElement('option');
                            option.value = batch.id;
                            option.textContent = batch.name;
                            batchFilter.appendChild(option);
                        }
                    });
                }
            }
        };

        // Set up date range selector
        const analyticsDateRange = document.getElementById('analyticsDateRange');
        if (analyticsDateRange) {
            analyticsDateRange.addEventListener('change', function () {
                const customDateRange = document.getElementById('analyticsCustomDateRange');
                const customDateRangeTo = document.getElementById('analyticsCustomDateRangeTo');

                if (this.value === 'custom') {
                    customDateRange.style.display = 'block';
                    customDateRangeTo.style.display = 'block';
                } else {
                    customDateRange.style.display = 'none';
                    customDateRangeTo.style.display = 'none';

                    // Calculate dates based on selection
                    const today = new Date();
                    const days = parseInt(this.value);
                    const fromDate = new Date();
                    fromDate.setDate(today.getDate() - days);

                    // Format dates for input fields
                    document.getElementById('analyticsFromDate').value = formatDate(fromDate);
                    document.getElementById('analyticsToDate').value = formatDate(today);
                }
            });

            // Initialize date range
            const today = new Date();
            const days = 30; // Default to 30 days
            const fromDate = new Date();
            fromDate.setDate(today.getDate() - days);

            // Format dates for input fields
            if (document.getElementById('analyticsFromDate')) {
                document.getElementById('analyticsFromDate').value = formatDate(fromDate);
            }
            if (document.getElementById('analyticsToDate')) {
                document.getElementById('analyticsToDate').value = formatDate(today);
            }
        }

        // Set up apply filters button
        const applyAnalyticsFilters = document.getElementById('applyAnalyticsFilters');
        if (applyAnalyticsFilters) {
            applyAnalyticsFilters.addEventListener('click', function () {
                // Get current chapter information
                const chapterSelect = document.getElementById('chapterSelect');
                const selectedValue = chapterSelect.value;

                if (!selectedValue || selectedValue === '') {
                    // No chapter selected - show warning and focus on dropdown
                    const chapterSelectWarning = document.getElementById('chapter-select-warning');

                    if (chapterSelectWarning) {
                        chapterSelectWarning.style.display = 'block';
                    }

                    if (chapterSelect) {
                        chapterSelect.focus();
                        chapterSelect.classList.add('highlight-select');

                        // Scroll to the chapter select if needed
                        chapterSelect.scrollIntoView({behavior: 'smooth', block: 'center'});
                    }

                    return;
                }

                // Get the book ID and refresh analytics
                const [chapterId, resId] = selectedValue.split('_');
                gptResId = resId;
                gptChapterId = chapterId;
                const bookId = ${bookId};

                console.log('Apply Filters clicked - Demo mode:', document.getElementById('analyticsDemoMode').checked);
                getBookAnalytics();
            });
        }
    });

// Format date as YYYY-MM-DD
    function formatDate(date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return year + '-' + month + '-' + day;
    }

    window.getBookAnalytics = function () {
        // Hide all other sections first
        hideAllAdvancedToolsSections();

        // Get current chapter information
        const chapterSelect = document.getElementById('chapterSelect');
        console.log('Chapter select element found:', chapterSelect);
        const selectedValue = chapterSelect ? chapterSelect.value : null;
        console.log('Selected value:', selectedValue);

        if (!selectedValue || selectedValue === '') {
            // No chapter selected - show warning and focus on dropdown
            const chapterSelectWarning = document.getElementById('chapter-select-warning');

            if (chapterSelectWarning) {
                chapterSelectWarning.style.display = 'block';
            }

            if (chapterSelect) {
                chapterSelect.focus();
                chapterSelect.classList.add('highlight-select');

                // Add highlight style if not already present
                if (!document.getElementById('highlight-select-style')) {
                    const style = document.createElement('style');
                    style.id = 'highlight-select-style';
                    style.textContent = '.highlight-select { border-color: #e74c3c !important; box-shadow: 0 0 0 0.2rem rgba(231, 76, 60, 0.25) !important; }';
                    document.head.appendChild(style);
                }

                // Scroll to the chapter select if needed
                chapterSelect.scrollIntoView({behavior: 'smooth', block: 'center'});
            }

            return;
        }

        // Clear any previous warnings
        const chapterSelectWarning = document.getElementById('chapter-select-warning');
        if (chapterSelectWarning) {
            chapterSelectWarning.style.display = 'none';
        }

        if (chapterSelect) {
            chapterSelect.classList.remove('highlight-select');
        }

        // Close the Advanced Tools accordion immediately if it's open
        const advancedToolsContent = document.querySelector('.advanced-tools-content');
        if (advancedToolsContent && advancedToolsContent.classList.contains('active')) {
            const advancedToolsToggle = document.querySelector('.advanced-tools-toggle');
            if (advancedToolsToggle) {
                advancedToolsToggle.click();
            }
        }

        const [chapterId, resId] = selectedValue.split('_');
        gptResId = resId;
        gptChapterId = chapterId;
        // Get the bookId from the URL
        let bookId;
        try {
            // Try to get bookId from URL parameter
            const urlParams = new URLSearchParams(window.location.search);
            bookId = urlParams.get('bookId');
            console.log('Got bookId from URL:', bookId);
        } catch (e) {
            console.error('Error getting bookId from URL:', e);
        }

        // If bookId is not in URL, try to get it from global variable
        if (!bookId && typeof window.bookId !== 'undefined') {
            bookId = window.bookId;
            console.log('Got bookId from global variable:', bookId);
        }

        // If still no bookId, log error and return
        if (!bookId) {
            console.error('No bookId found');
            return;
        }

        // Show loading indicator in the Analytics section
        const analyticsSection = document.getElementById('analytics-section');
        const bookAnalyticsView = document.getElementById('book-analytics');
        const chapterAnalyticsView = document.getElementById('chapter-analytics');
        const bookAnalyticsChart = document.getElementById('bookAnalyticsChart');
        const bookAnalyticsChartLoading = document.getElementById('bookAnalyticsChartLoading');

        if (analyticsSection && bookAnalyticsView && chapterAnalyticsView && bookAnalyticsChart) {
            analyticsSection.style.display = 'block';
            bookAnalyticsView.style.display = 'block';
            chapterAnalyticsView.style.display = 'none';

            // Show loading overlay
            if (bookAnalyticsChartLoading) {
                bookAnalyticsChartLoading.style.display = 'flex';
            }

            // Add animation style if not already present
            if (!document.getElementById('spinner-style')) {
                const style = document.createElement('style');
                style.id = 'spinner-style';
                style.textContent = '@keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }';
                document.head.appendChild(style);
            }

            // Scroll to the Analytics section
            window.scrollTo({
                top: analyticsSection.offsetTop - 100,
                behavior: 'smooth'
            });
        }

        // Get filter values
        const fromDate = document.getElementById('analyticsFromDate') ? document.getElementById('analyticsFromDate').value : null;
        const toDate = document.getElementById('analyticsToDate') ? document.getElementById('analyticsToDate').value : null;
        const batchId = document.getElementById('analyticsBatchFilter') ? document.getElementById('analyticsBatchFilter').value : null;
        const isDemoMode = document.getElementById('analyticsDemoMode') ? document.getElementById('analyticsDemoMode').checked : false;

        console.log('Demo mode checked:', isDemoMode);

        // Construct the params string manually for better control
        let paramsStr = 'bookId=' + bookId;
        if (fromDate) paramsStr += '&fromDate=' + fromDate;
        if (toDate) paramsStr += '&toDate=' + toDate;
        if (batchId) paramsStr += '&batchId=' + batchId;
        if (isDemoMode) paramsStr += '&showReport=demo';

        console.log('Params string:', paramsStr);

        // Use AJAX to call the server with filter parameters
        // Instead of using g:remoteFunction with a JavaScript variable, we'll use jQuery.ajax directly
        $.ajax({
            url: '${createLink(controller: 'bookAnalytics', action: 'getBookAnalytics')}',
            data: paramsStr,
            success: function (data) {
                console.log('Response:', data);
                displayAnalyticsContent(data.data);
            },
            error: function (xhr, status, error) {
                console.error('Error calling API:', error);
                console.error('Status:', status);
                console.error('Response:', xhr.responseText);
            }
        });
    };

    window.getQuestionBankMCQs = function () {
        // Get current chapter information
        const chapterSelect = document.getElementById('chapterSelect');
        console.log('Question Bank: Chapter select element found:', chapterSelect);
        const selectedValue = chapterSelect ? chapterSelect.value : null;
        console.log('Question Bank: Selected value:', selectedValue);

        if (!selectedValue || selectedValue === '') {
            // No chapter selected - show warning and focus on dropdown
            const chapterSelectWarning = document.getElementById('chapter-select-warning');

            if (chapterSelectWarning) {
                chapterSelectWarning.style.display = 'block';
            }

            if (chapterSelect) {
                chapterSelect.focus();
                chapterSelect.classList.add('highlight-select');

                // Add highlight style if not already present
                if (!document.getElementById('highlight-select-style')) {
                    const style = document.createElement('style');
                    style.id = 'highlight-select-style';
                    style.textContent = '.highlight-select { border-color: #e74c3c !important; box-shadow: 0 0 0 0.2rem rgba(231, 76, 60, 0.25) !important; }';
                    document.head.appendChild(style);
                }

                // Scroll to the chapter select if needed
                chapterSelect.scrollIntoView({behavior: 'smooth', block: 'center'});
            }

            return;
        }

        // Hide all other sections first
        hideAllAdvancedToolsSections();

        // Close the Advanced Tools accordion immediately if it's open
        const advancedToolsContent = document.querySelector('.advanced-tools-content');
        if (advancedToolsContent && advancedToolsContent.classList.contains('active')) {
            const advancedToolsToggle = document.querySelector('.advanced-tools-toggle');
            if (advancedToolsToggle) {
                advancedToolsToggle.click();
            }
        }

        const [chapterId, resId] = selectedValue.split('_');
        gptResId = resId;
        gptChapterId = chapterId;

        // Show loading indicator in the Question Bank section
        const questionBankSection = document.getElementById('question-bank-section');
        const mcqContent = document.getElementById('mcq-content');
        const qnaContent = document.getElementById('qna-content');

        if (questionBankSection && mcqContent && qnaContent) {
            questionBankSection.style.display = 'block';

            // Show loading indicator in both tabs
            const loadingHTML = '<div style="padding: 20px; text-align: center;"><div class="spinner" style="margin: 0 auto; width: 40px; height: 40px; border: 4px solid #f3f3f3; border-top: 4px solid #3498db; border-radius: 50%; animation: spin 2s linear infinite;"></div><p style="margin-top: 10px;">Loading content...</p></div>';

            mcqContent.innerHTML = loadingHTML;
            qnaContent.innerHTML = loadingHTML;

            // Add animation style if not already present
            if (!document.getElementById('spinner-style')) {
                const style = document.createElement('style');
                style.id = 'spinner-style';
                style.textContent = '@keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }';
                document.head.appendChild(style);
            }

            // Set up tab click handlers if not already set
            if (typeof setupTabHandlers === 'function') {
                setupTabHandlers();
            }

            // Scroll to the Question Bank section to show the loading indicator, but not all the way to the top
            // This ensures the toggle-switch remains visible
            window.scrollTo({
                top: questionBankSection.offsetTop - 100, // Leave space at the top for the toggle-switch
                behavior: 'smooth'
            });
        }

        // Use AJAX to call the server
        <g:remoteFunction controller="prompt" action="createTest" params="'readingMaterialResId='+gptResId" onSuccess="displayQuestionBankContent(data);"/>
    }
</script>

<div class="ai-assistant-header color-option-1">
    <div class="ai-assistant-title">
        <div class="icon-container" style="background-color: rgba(255, 255, 255, 0.2);">
            <i data-lucide="bot"></i>
        </div>
        <h2>Teacher AI Assistant</h2>
    </div>
    <div class="command-palette-shortcut" onclick="openCommandPalette()" title="Press ⌘K or Ctrl+K to search tools">
        <span class="shortcut-key">⌘K</span>
        <span class="shortcut-text">Search</span>
        <i data-lucide="search" class="search-icon"></i>
    </div>
</div>

<div class="chapter-navigation">
    <div class="chapter-select-container">
        <label for="chapterSelect">Select Chapter</label>
        <select id="chapterSelect" class="form-control chapter-select">
            <option value="" selected disabled>-- Select a chapter --</option>
            <g:each in="${chaptersList}" var="chapter">
                <option value="${chapter.chapterId}_${chapter.resId}">${chapter.chapterName}</option>
            </g:each>
        </select>
        <div id="chapter-select-warning" style="color: #e74c3c; font-size: 12px; margin-top: 5px; display: none;">Please select a chapter first</div>
    </div>
    <div class="book-view-container">
        <button id="bookViewBtn" class="btn book-view-btn pulse-animation" onclick="switchToBookView()">
            <div class="icon-container">
                <i data-lucide="book-open"></i>
            </div>
            Book View
        </button>
    </div>
</div>

<div class="ai-section start-here-section">
    <div class="ai-section-header section-toggle">
        <div class="header-left">
            <div class="icon-container" style="background-color: rgba(66, 133, 244, 0.8);">
                <i data-lucide="rocket"></i>
            </div>
            <h3>Start here</h3>
        </div>
        <i data-lucide="chevron-up" class="section-chevron"></i>
    </div>
    <div class="ai-prompt-buttons">
        <!-- Lesson Plan from Plan section -->
        <button class="ai-prompt-btn lesson-plan" onclick="talkToAI('Lesson Plan', 'teacher_lessonplan')">
            <div class="icon-container" style="background-color: rgba(66, 133, 244, 0.8);">
                <i data-lucide="clipboard-list"></i>
            </div>
            <span>Lesson Plan</span>
        </button>

        <!-- Teaching Coach button -->
        <button class="ai-prompt-btn teaching-coach" onclick="getTeachingCoach()">
            <div class="icon-container" style="background-color: rgba(52, 168, 83, 0.8);">
                <i data-lucide="graduation-cap"></i>
            </div>
            <span>Teaching Coach</span>
        </button>



        <!-- Question Bank button -->
        <button class="ai-prompt-btn" onclick="getQuestionBankMCQs()">
            <div class="icon-container" style="background-color: rgba(161, 66, 244, 0.8);">
                <i data-lucide="list-checks"></i>
            </div>
            <span>Question Bank</span>
        </button>


        <!-- Analytics from Assess section -->
        <button class="ai-prompt-btn analytics">
            <div class="icon-container" style="background-color: rgba(0, 150, 136, 0.8);">
                <i data-lucide="bar-chart"></i>
            </div>
            <span>Analytics</span>
        </button>
    </div>
</div>

<hr class="section-divider">

<!-- Advanced Tools Section -->
<div class="ai-section">
    <div class="ai-section-header advanced-tools-toggle section-toggle">
        <div class="header-left">
            <i class="fa-solid fa-wand-magic-sparkles"></i>
            <h3>Advanced Tools</h3>
        </div>
        <i class="fa-solid fa-chevron-up section-chevron active"></i>
    </div>

    <div class="advanced-tools-content active">
        <!-- Plan Accordion -->
        <div class="accordion-section" style="border-color: #90caf9;">
            <div class="accordion-header" style="background-color: rgba(208, 228, 247, 0.5);">
                <div class="icon-container" style="background-color: rgba(208, 228, 247, 0.9);">
                    <i data-lucide="lightbulb"></i>
                </div>
                <h4>Plan</h4>
                <i data-lucide="chevron-up" class="accordion-icon"></i>
            </div>
            <div class="accordion-content">
                <div class="ai-prompt-buttons">
                    <button class="ai-prompt-btn" onclick="talkToAI('Lesson Plan', 'teacher_lessonplan')">
                        <i class="fa-solid fa-chalkboard-teacher"></i>
                        <span>Lesson Plan</span>
                    </button>
                    <button class="ai-prompt-btn" onclick="getTeachingCoach()">
                        <i class="fa-solid fa-clone"></i>
                        <span>Teaching Coach</span>
                    </button>
                    <button class="ai-prompt-btn" onclick="talkToAI('Definitions/Concepts', 'concepts')">
                        <i class="fa-solid fa-book"></i>
                        <span>Definitions/Concepts</span>
                    </button>
                    <button class="ai-prompt-btn" onclick="talkToAI('Chapter Summary', 'summary')">
                        <i class="fa-solid fa-file-alt"></i>
                        <span>Chapter Summary</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- Teach Accordion -->
        <div class="accordion-section" style="border-color: #81c784;">
            <div class="accordion-header" style="background-color: rgba(209, 239, 214, 0.5);">
                <div class="icon-container" style="background-color: rgba(209, 239, 214, 0.9);">
                    <i data-lucide="presentation"></i>
                </div>
                <h4>Teach</h4>
                <i data-lucide="chevron-up" class="accordion-icon"></i>
            </div>
            <div class="accordion-content">
                <div class="ai-prompt-buttons">
                    <button class="ai-prompt-btn" onclick="talkToAI('Interactive Activities', 'teacher_activities')">
                        <i class="fa-solid fa-puzzle-piece"></i>
                        <span>Interactive Activities</span>
                    </button>
                    <button class="ai-prompt-btn" onclick="talkToAI('Homework & Assignment', 'teacher_homework')">
                        <i class="fa-solid fa-house"></i>
                        <span>Homework & Assignment</span>
                    </button>
                    <button class="ai-prompt-btn" onclick="getSuggestedVideos('Suggested Videos', 'videos')">
                        <i class="fa-solid fa-video"></i>
                        <span>Suggested Videos</span>
                    </button>
                    <button class="ai-prompt-btn" onclick="talkToAI('Real-World Examples', 'teacher_realworld')">
                        <i class="fa-solid fa-globe"></i>
                        <span>Real-World Examples</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- Assess Accordion -->
        <div class="accordion-section" style="border-color: #ba68c8;">
            <div class="accordion-header" style="background-color: rgba(233, 213, 242, 0.5);">
                <div class="icon-container" style="background-color: rgba(233, 213, 242, 0.9);">
                    <i data-lucide="clipboard-check"></i>
                </div>
                <h4>Assess</h4>
                <i data-lucide="chevron-up" class="accordion-icon"></i>
            </div>
            <div class="accordion-content">
                <div class="ai-prompt-buttons">
                    <button class="ai-prompt-btn" onclick="talkToAI('Create MCQ', 'mcq')">
                        <i class="fa-solid fa-clipboard-check"></i>
                        <span>Create MCQs</span>
                    </button>
                    <button class="ai-prompt-btn" onclick="talkToAI('Create Question and Answers', 'qna')">
                        <i class="fa-solid fa-clipboard-check"></i>
                        <span>Create Question and Answers</span>
                    </button>
                    <button class="ai-prompt-btn" onclick="getQuestionBankMCQs()">
                        <i class="fa-solid fa-question"></i>
                        <span>Question Bank</span>
                    </button>

                    <button class="ai-prompt-btn analytics">
                        <i class="fa-solid fa-chart-line"></i>
                        <span>Analytics</span>
                    </button>

                </div>
            </div>
        </div>

        <!-- Removed Analyze & Monitor section as its contents were moved to Assess -->

        <!-- Study Accordion -->
        <div class="accordion-section" style="border-color: #ffcc80;">
            <div class="accordion-header" style="background-color: rgba(255, 241, 214, 0.5);">
                <div class="icon-container" style="background-color: rgba(255, 241, 214, 0.9);">
                    <i data-lucide="graduation-cap"></i>
                </div>
                <h4>Study</h4>
                <i data-lucide="chevron-up" class="accordion-icon"></i>
            </div>
            <div class="accordion-content">
                <div class="ai-prompt-buttons">
                    <!-- Chapter Summary moved to Plan section -->
                    <button class="ai-prompt-btn" onclick="talkToAI('Highlights & Notes', 'examnotes')">
                        <i class="fa-solid fa-highlighter"></i>
                        <span>Highlights & Notes</span>
                    </button>
                    <button class="ai-prompt-btn" onclick="talkToAI('Flashcards', 'flashcards')">
                        <i class="fa-solid fa-clone"></i>
                        <span>Flashcards</span>
                    </button>
                    <button class="ai-prompt-btn" onclick="talkToAI('Study Tricks', 'tricks')">
                        <i class="fa-solid fa-lightbulb"></i>
                        <span>Study Tricks</span>
                    </button>

                    <button class="ai-prompt-btn" onclick="talkToAI('Important Notes', 'examnotes')">
                        <i class="fa-solid fa-star"></i>
                        <span>Important Notes</span>
                    </button>
                    <button class="ai-prompt-btn" onclick="talkToAI('Common Mistakes', 'common_mistakes')">
                        <i class="fa-solid fa-triangle-exclamation"></i>
                        <span>Common Mistakes</span>
                    </button>
                    <button class="ai-prompt-btn" onclick="talkToAI('Important Formulae', 'important_formulas_equation_theorems')">
                        <i class="fa-solid fa-square-root-variable"></i>
                        <span>Important Formulae</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>



<!-- Back to Top Button -->
<div id="back-to-top" style="position: fixed; bottom: 20px; right: 20px; background-color: rgba(66, 133, 244, 0.9); color: white; width: 40px; height: 40px; border-radius: 50%; display: none; justify-content: center; align-items: center; cursor: pointer; box-shadow: 0 2px 5px rgba(0,0,0,0.2); z-index: 1000;" onclick="scrollToTop()">
    <i data-lucide="chevron-up" style="width: 24px; height: 24px;"></i>
</div>

<!-- Question Bank Section -->
<div class="ai-section" id="question-bank-section" style="display: none;">
    <div class="ai-section-header section-toggle">
        <div class="header-left">
            <div class="icon-container" style="background-color: rgba(251, 140, 0, 0.8);">
                <i data-lucide="list-checks"></i>
            </div>
            <h3>Question Bank</h3>
        </div>
        <i data-lucide="chevron-up" class="section-chevron"></i>
    </div>

    <!-- Tab Navigation -->
    <div id="question-bank-tabs" class="question-bank-tabs" style="display: none; margin-bottom: 15px; border-bottom: 1px solid #ddd;">
        <div id="qna-tab" class="question-bank-tab active" style="padding: 10px 20px; cursor: pointer; font-weight: 500; border-bottom: 3px solid #4285f4; display: none;">
            <span class="desktop-label">Question & Answers</span>
            <span class="mobile-label" style="display: none;">Q&A</span>
            <span id="qna-count" style="margin-left: 5px; font-size: 12px; background-color: #e0e0e0; padding: 2px 6px; border-radius: 10px;">0</span>
        </div>
        <div id="mcq-tab" class="question-bank-tab" style="padding: 10px 20px; cursor: pointer; font-weight: 500; display: none;">
            <span class="desktop-label">Objective Types</span>
            <span class="mobile-label" style="display: none;">Obj Types</span>
            <span id="mcq-count" style="margin-left: 5px; font-size: 12px; background-color: #e0e0e0; padding: 2px 6px; border-radius: 10px;">0</span>
        </div>
    </div>

    <style>
        @media (max-width: 768px) {
            .desktop-label { display: none !important; }
            .mobile-label { display: inline !important; }
        }

        /* Analytics Styles */
        .analytics-view {
            padding: 15px;
        }

        .chart-container {
            position: relative;
            height: 300px;
            margin-bottom: 20px;
        }

        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.8);
            display: none; /* Hide by default, will be shown when loading */
            justify-content: center;
            align-items: center;
            z-index: 10;
        }

        .back-button {
            margin-bottom: 15px;
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
        }

        .data-table th, .data-table td {
            padding: 8px 12px;
            border: 1px solid #ddd;
        }

        .data-table th {
            background-color: #f5f5f5;
            font-weight: 600;
        }

        .data-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        .data-table tr:hover {
            background-color: #f1f1f1;
        }
    </style>

    <!-- Tab Content -->
    <div class="question-bank-content">
        <!-- MCQs Tab Content -->
        <div id="mcq-content" class="tab-content" style="display: flex; flex-direction: column; width: 100%;">
            <!-- MCQs will be displayed here -->
        </div>

        <!-- Q&A Tab Content -->
        <div id="qna-content" class="tab-content" style="display: none; flex-direction: column; width: 100%;">
            <!-- Q&A will be displayed here -->
        </div>
    </div>
</div>

<!-- Teaching Coach Section -->
<div class="ai-section" id="teaching-coach-section" style="display: none; background-color: rgba(66, 133, 244, 0.05); border-radius: 8px; border: 1px solid rgba(66, 133, 244, 0.1);">
    <div class="ai-section-header section-toggle">
        <div class="header-left">
            <div class="icon-container" style="background-color: rgba(66, 133, 244, 0.8);">
                <i data-lucide="graduation-cap"></i>
            </div>
            <h3>Teaching Coach</h3>
        </div>
        <i data-lucide="chevron-up" class="section-chevron"></i>
    </div>

    <!-- Teaching Coach content will be displayed here -->
</div>

<!-- Analytics Section -->
<div class="ai-section" id="analytics-section" style="display: none;">
    <div class="ai-section-header section-toggle">
        <div class="header-left">
            <div class="icon-container" style="background-color: rgba(0, 150, 136, 0.8);">
                <i data-lucide="bar-chart"></i>
            </div>
            <h3>Analytics</h3>
        </div>
        <i data-lucide="chevron-up" class="section-chevron"></i>
    </div>

    <!-- Analytics Filters -->
    <div class="analytics-filters" style="padding: 15px; background-color: #f8f9fa; border-bottom: 1px solid #e9ecef; margin-bottom: 15px;">
        <div class="row">
            <div class="col-md-4">
                <div class="form-group">
                    <label for="analyticsDateRange">Date Range</label>
                    <select id="analyticsDateRange" class="form-control">
                        <option value="7">Last 7 days</option>
                        <option value="30" selected>Last 30 days</option>
                        <option value="90">Last 90 days</option>
                        <option value="365">Last 1 year</option>
                        <option value="custom">Custom Range</option>
                    </select>
                </div>
            </div>
            <div class="col-md-4" id="analyticsCustomDateRange" style="display: none;">
                <div class="form-group">
                    <label for="analyticsFromDate">From</label>
                    <input type="date" id="analyticsFromDate" class="form-control">
                </div>
            </div>
            <div class="col-md-4" id="analyticsCustomDateRangeTo" style="display: none;">
                <div class="form-group">
                    <label for="analyticsToDate">To</label>
                    <input type="date" id="analyticsToDate" class="form-control">
                </div>
            </div>

            <!-- Batch Filter (Only for Managers and Instructors) -->
            <g:if test="${userRoles?.any { it == 'ROLE_INSTITUTE_ADMIN' || it == 'ROLE_INSTITUTE_REPORT_MANAGER' || it == 'ROLE_GPT_MANAGER' }}">
                <div class="col-md-4">
                    <div class="form-group">
                        <label for="analyticsBatchFilter">Batch</label>
                        <select id="analyticsBatchFilter" class="form-control">
                            <option value="personal">My Personal Data</option>
                            <!-- Will be populated by JavaScript -->
                        </select>
                    </div>
                </div>
            </g:if>
            <g:else>
                <!-- For regular users, just add a spacer -->
                <div class="col-md-4"></div>
            </g:else>

            <div class="col-md-4">
                <div class="form-group">
                    <label for="analyticsDemoMode">Demo Mode</label>
                    <div class="custom-control custom-switch">
                        <input type="checkbox" class="custom-control-input" id="analyticsDemoMode">
                        <label class="custom-control-label" for="analyticsDemoMode">Show Demo Data</label>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="form-group">
                    <label>&nbsp;</label>
                    <button id="applyAnalyticsFilters" class="btn btn-primary form-control">Apply Filters</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Analytics Content -->
    <div class="analytics-content">
        <!-- Book-level Analytics -->
        <div id="book-analytics" class="analytics-view">
            <div class="row">
                <!-- Book Analytics Chart -->
                <div class="col-md-6">
                    <div class="chart-container">
                        <div id="bookAnalyticsChartLoading" class="loading-overlay">
                            <div class="spinner-border text-primary" role="status">
                                <span class="sr-only">Loading...</span>
                            </div>
                        </div>
                        <canvas id="bookAnalyticsChart"></canvas>
                    </div>
                </div>

                <!-- Book Analytics Table -->
                <div class="col-md-6">
                    <div class="table-responsive">
                        <table id="bookAnalyticsTable" class="table table-striped data-table">
                            <thead>
                                <tr>
                                    <th>Chapter Name</th>
                                    <th>Interaction Count</th>
                                    <th>Percentage</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Will be populated by JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Chapter-level Analytics (initially hidden) -->
        <div id="chapter-analytics" class="analytics-view" style="display: none;">
            <button id="backToBookAnalytics" class="btn btn-secondary back-button">
                <i class="fa fa-arrow-left"></i> Back to Book Analytics
            </button>
            <h4 id="chapterAnalyticsTitle" class="mt-3 mb-4">Chapter Analytics</h4>

            <div class="row">
                <!-- Chapter Analytics Chart -->
                <div class="col-md-6">
                    <div class="chart-container">
                        <div id="chapterAnalyticsChartLoading" class="loading-overlay">
                            <div class="spinner-border text-primary" role="status">
                                <span class="sr-only">Loading...</span>
                            </div>
                        </div>
                        <canvas id="chapterAnalyticsChart"></canvas>
                    </div>
                </div>

                <!-- Chapter Analytics Table -->
                <div class="col-md-6">
                    <div class="table-responsive">
                        <table id="chapterAnalyticsTable" class="table table-striped data-table">
                            <thead>
                                <tr>
                                    <th>Metric</th>
                                    <th>Value</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Will be populated by JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Book-level Practice Dashboard -->
        <div id="book-practice" class="analytics-view" style="display: none;">
            <h4 class="mt-3 mb-4">Practice Dashboard</h4>

            <!-- No data message -->
            <div id="practiceNoDataMessage" style="display: none; text-align: center; padding: 30px;">
                <div style="margin-bottom: 20px;">
                    <i class="fa fa-chart-bar" style="font-size: 48px; color: #ccc;"></i>
                </div>
                <h5>No Practice Data Available</h5>
                <p class="text-muted">There is no practice data available for this book.</p>
            </div>

            <div class="row">
                <!-- Practice Summary Cards -->
                <div class="col-md-12 mb-4">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="card-title">Total Questions</h5>
                                    <p id="bookPracticeTotalQuestions" class="card-text display-4">0</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="card-title">Correct Answers</h5>
                                    <p id="bookPracticeCorrectAnswers" class="card-text display-4">0</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="card-title">Overall Accuracy</h5>
                                    <p id="bookPracticeAccuracy" class="card-text display-4">0%</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Practice Charts -->
                <div class="col-md-6">
                    <div class="chart-container">
                        <div id="bookPracticeChartLoading" class="loading-overlay">
                            <div class="spinner-border text-primary" role="status">
                                <span class="sr-only">Loading...</span>
                            </div>
                        </div>
                        <canvas id="bookPracticeChart"></canvas>
                    </div>
                </div>

                <!-- Practice Trends Chart -->
                <div class="col-md-6">
                    <div class="chart-container">
                        <div id="bookPracticeTrendsChartLoading" class="loading-overlay">
                            <div class="spinner-border text-primary" role="status">
                                <span class="sr-only">Loading...</span>
                            </div>
                        </div>
                        <canvas id="bookPracticeTrendsChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- Practice Details Table -->
            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="table-responsive">
                        <table id="bookPracticeTable" class="table table-striped data-table">
                            <thead>
                                <tr>
                                    <th>Chapter</th>
                                    <th>Questions</th>
                                    <th>Correct</th>
                                    <th>Accuracy</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Will be populated by JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Chapter-level Practice Dashboard -->
        <div id="chapter-practice" class="analytics-view" style="display: none;">
            <button id="backToBookPractice" class="btn btn-secondary back-button">
                <i class="fa fa-arrow-left"></i> Back to Book Practice
            </button>
            <h4 id="chapterPracticeTitle" class="mt-3 mb-4">Chapter Practice Dashboard</h4>

            <!-- No data message -->
            <div id="chapterPracticeNoDataMessage" style="display: none; text-align: center; padding: 30px;">
                <div style="margin-bottom: 20px;">
                    <i class="fa fa-chart-bar" style="font-size: 48px; color: #ccc;"></i>
                </div>
                <h5>No Practice Data Available</h5>
                <p class="text-muted">There is no practice data available for this chapter.</p>
            </div>

            <div class="row">
                <!-- Practice Summary Cards -->
                <div class="col-md-12 mb-4">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="card-title">Total Questions</h5>
                                    <p id="chapterPracticeTotalQuestions" class="card-text display-4">0</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="card-title">Correct Answers</h5>
                                    <p id="chapterPracticeCorrectAnswers" class="card-text display-4">0</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="card-title">Overall Accuracy</h5>
                                    <p id="chapterPracticeAccuracy" class="card-text display-4">0%</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Question Type Chart -->
                <div class="col-md-6">
                    <div class="chart-container">
                        <div id="chapterPracticeChartLoading" class="loading-overlay">
                            <div class="spinner-border text-primary" role="status">
                                <span class="sr-only">Loading...</span>
                            </div>
                        </div>
                        <canvas id="chapterPracticeChart"></canvas>
                    </div>
                </div>

                <!-- Practice Trends Chart -->
                <div class="col-md-6">
                    <div class="chart-container">
                        <div id="chapterPracticeTrendsChartLoading" class="loading-overlay">
                            <div class="spinner-border text-primary" role="status">
                                <span class="sr-only">Loading...</span>
                            </div>
                        </div>
                        <canvas id="chapterPracticeTrendsChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- Practice Details Table -->
            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="table-responsive">
                        <table id="chapterPracticeTable" class="table table-striped data-table">
                            <thead>
                                <tr>
                                    <th>Topic</th>
                                    <th>Questions</th>
                                    <th>Correct</th>
                                    <th>Accuracy</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Will be populated by JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Extra spacing to ensure all buttons are visible when scrolling -->
<div style="height: 100px;"></div>

<script>
    // Define an array of soft pastel colors to use for button icons
    const colorArray = [
        'rgba(208, 228, 247, 0.9)', // Soft Blue
        'rgba(209, 239, 214, 0.9)', // Soft Green
        'rgba(233, 213, 242, 0.9)', // Soft Purple
        'rgba(249, 220, 220, 0.9)', // Soft Red
        'rgba(255, 241, 214, 0.9)', // Soft Orange
        'rgba(212, 242, 235, 0.9)', // Soft Turquoise
        'rgba(249, 228, 212, 0.9)', // Soft Pumpkin
        'rgba(242, 215, 213, 0.9)', // Soft Pomegranate
        'rgba(212, 239, 233, 0.9)', // Soft Green Sea
        'rgba(228, 214, 242, 0.9)', // Soft Wisteria
        'rgba(214, 240, 223, 0.9)', // Soft Nephritis
        'rgba(252, 243, 207, 0.9)', // Soft Sunflower
        'rgba(250, 229, 211, 0.9)', // Soft Carrot
        'rgba(214, 234, 248, 0.9)', // Soft Belize Hole
        'rgba(229, 231, 233, 0.9)'  // Soft Asbestos
    ];

    // Command palette variables
    let commandPalette;
    let commandPaletteInput;
    let commandPaletteResults;
    let allTools = [];

    function switchToBookView() {
        // Add your book view switching logic here
        console.log('Switching to book view');
        window.location.href = '/prompt/bookgpt?siteName=books&bookId=${bookId}'
    }

    // No modal functions needed

    // Command palette functions
    function createCommandPalette() {
        try {
            // Create command palette elements if they don't exist
            if (!document.getElementById('commandPalette')) {
                // Create the main container
                commandPalette = document.createElement('div');
                commandPalette.id = 'commandPalette';
                commandPalette.className = 'command-palette';
                commandPalette.style.display = 'none';

                // Create the search input
                const searchContainer = document.createElement('div');
                searchContainer.className = 'command-search-container';

                const searchIcon = document.createElement('i');
                searchIcon.className = 'fa-solid fa-search';
                searchContainer.appendChild(searchIcon);

                commandPaletteInput = document.createElement('input');
                commandPaletteInput.type = 'text';
                commandPaletteInput.placeholder = 'Search for tools...';
                commandPaletteInput.className = 'command-search';
                searchContainer.appendChild(commandPaletteInput);

                const keyboardShortcut = document.createElement('div');
                keyboardShortcut.className = 'keyboard-shortcut';
                keyboardShortcut.innerHTML = '<span>ESC</span> to close';
                searchContainer.appendChild(keyboardShortcut);

                commandPalette.appendChild(searchContainer);

                // Create results container
                commandPaletteResults = document.createElement('div');
                commandPaletteResults.className = 'command-results';
                commandPalette.appendChild(commandPaletteResults);

                // Add to document
                document.body.appendChild(commandPalette);

                // Add event listeners safely
                if (commandPaletteInput) {
                    commandPaletteInput.addEventListener('input', filterCommands);
                    commandPaletteInput.addEventListener('keydown', handleCommandNavigation);
                }

                document.addEventListener('keydown', handleKeyboardShortcut);

                // Close when clicking outside
                document.addEventListener('click', function(e) {
                    if (commandPalette && commandPalette.style.display === 'block' &&
                        !commandPalette.contains(e.target)) {
                        closeCommandPalette();
                    }
                });

                console.log('Command palette created successfully');
            }
        } catch (e) {
            console.error('Error creating command palette:', e);
        }
    }

     function talkToAI(promptLabel, promptType) {
        console.log('Talking to AI with prompt: '+promptLabel+' type: '+promptType);


        // Get current chapter information
        const chapterSelect = document.getElementById('chapterSelect');
        const selectedValue = chapterSelect.value;

        if (!selectedValue || selectedValue === '') {
            // No chapter selected - show warning and focus on dropdown
            const chapterSelectWarning = document.getElementById('chapter-select-warning');

            if (chapterSelectWarning) {
                chapterSelectWarning.style.display = 'block';
            }

            if (chapterSelect) {
                chapterSelect.focus();
                chapterSelect.classList.add('highlight-select');

                // Add highlight style if not already present
                if (!document.getElementById('highlight-select-style')) {
                    const style = document.createElement('style');
                    style.id = 'highlight-select-style';
                    style.textContent = '.highlight-select { border-color: #e74c3c !important; box-shadow: 0 0 0 0.2rem rgba(231, 76, 60, 0.25) !important; }';
                    document.head.appendChild(style);
                }

                // Scroll to the chapter select if needed
                chapterSelect.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }


        }else {

            // Clear any previous warnings
            const chapterSelectWarning = document.getElementById('chapter-select-warning');
            if (chapterSelectWarning) {
                chapterSelectWarning.style.display = 'none';
            }

            if (chapterSelect) {
                chapterSelect.classList.remove('highlight-select');
            }

            const [chapterId, resId] = selectedValue.split('_');
            gptResId = resId;
            gptChapterId = chapterId;


            // Show chat window if it's hidden
            if (typeof showChatWindowCB === 'function') {
                showChatWindowCB();
            }
            getDefaultAnswer(promptLabel.replaceAll("&", "and"), '', promptType);
        }
    }

    function showLoadingIndicator() {
        // Add loading indicator to the page
        const loadingIndicator = document.createElement('div');
        loadingIndicator.id = 'ai-loading-indicator';
        loadingIndicator.innerHTML = '<div class="spinner"></div><p>Generating response...</p>';
        document.body.appendChild(loadingIndicator);
    }

    function hideLoadingIndicator() {
        // Remove loading indicator
        const loadingIndicator = document.getElementById('ai-loading-indicator');
        if (loadingIndicator) {
            loadingIndicator.remove();
        }
    }

    function collectAllTools() {
        // Collect all tool buttons
        allTools = [];
        try {
            const toolButtons = document.querySelectorAll('.ai-prompt-btn');

            if (toolButtons && toolButtons.length > 0) {
                toolButtons.forEach(button => {
                    try {
                        // Safely get the label
                        let label = 'Unknown';
                        const spanElement = button.querySelector('span');
                        if (spanElement && spanElement.textContent) {
                            label = spanElement.textContent.trim();
                        }

                        // Get the onClick attribute
                        const onClick = button.getAttribute('onclick');

                        // Get the icon element
                        const iconElement = button.querySelector('i');
                        let bgColor = '#4285f4'; // Default blue

                        if (iconElement) {
                            // Try to get the background color
                            const computedStyle = window.getComputedStyle(iconElement);
                            if (computedStyle && computedStyle.backgroundColor) {
                                bgColor = computedStyle.backgroundColor;
                            }
                        }

                        // Only add valid tools
                        if (label && onClick) {
                            allTools.push({
                                label: label,
                                onClick: onClick,
                                element: button,
                                bgColor: bgColor
                            });
                        }
                    } catch (err) {
                        console.error('Error processing button:', err);
                    }
                });
            } else {
                console.warn('No tool buttons found');
            }
        } catch (e) {
            console.error('Error collecting tools:', e);
        }
    }

    function openCommandPalette() {
        try {
            if (!commandPalette) {
                createCommandPalette();
            }

            // Collect all tools
            collectAllTools();

            // Debug message
            console.log('Collected tools:', allTools);

            // Show the palette
            if (commandPalette) {
                commandPalette.style.display = 'block';

                if (commandPaletteInput) {
                    commandPaletteInput.value = '';
                    commandPaletteInput.focus();
                }

                // Show all tools initially
                showAllCommands();
            }
        } catch (e) {
            console.error('Error opening command palette:', e);
        }
    }

    function closeCommandPalette() {
        try {
            if (commandPalette) {
                commandPalette.style.display = 'none';
            }
        } catch (e) {
            console.error('Error closing command palette:', e);
        }
    }

    // Debug function to add a test item to the command palette
    function addTestItem() {
        try {
            if (!commandPaletteResults) {
                console.error('Command palette results element not found');
                return;
            }

            const testItem = document.createElement('div');
            testItem.className = 'result-item';
            testItem.style.display = 'flex';
            testItem.style.alignItems = 'center';
            testItem.style.padding = '10px 20px';
            testItem.style.backgroundColor = 'white';
            testItem.style.color = '#333';

            const iconDiv = document.createElement('div');
            iconDiv.className = 'result-icon';
            iconDiv.style.marginRight = '15px';

            const icon = document.createElement('i');
            icon.className = 'fa-solid fa-circle';
            icon.style.backgroundColor = '#4285f4';
            icon.style.width = '30px';
            icon.style.height = '30px';
            icon.style.display = 'flex';
            icon.style.alignItems = 'center';
            icon.style.justifyContent = 'center';
            icon.style.borderRadius = '50%';
            icon.style.color = 'white';
            iconDiv.appendChild(icon);

            const labelDiv = document.createElement('div');
            labelDiv.className = 'result-label';
            labelDiv.textContent = 'Test Item';
            labelDiv.style.fontSize = '14px';
            labelDiv.style.fontWeight = '500';
            labelDiv.style.color = '#333';

            testItem.appendChild(iconDiv);
            testItem.appendChild(labelDiv);

            commandPaletteResults.appendChild(testItem);
            console.log('Added test item to results');
        } catch (e) {
            console.error('Error adding test item:', e);
        }
    }

    function showAllCommands() {
        try {
            if (!commandPaletteResults) {
                console.error('Command palette results element not found');
                return;
            }

            commandPaletteResults.innerHTML = '';

            // Add a test item first
            addTestItem();

            if (!Array.isArray(allTools) || allTools.length === 0) {
                const noTools = document.createElement('div');
                noTools.className = 'no-results';
                noTools.textContent = 'No tools available';
                noTools.style.padding = '20px';
                noTools.style.textAlign = 'center';
                noTools.style.color = '#666';
                commandPaletteResults.appendChild(noTools);
                console.warn('No tools available to show');
                return;
            }

            console.log('Showing all tools:', allTools.length, 'tools available');

            allTools.forEach(tool => {
                try {
                    if (tool) {
                        const resultItem = createResultItem(tool);
                        commandPaletteResults.appendChild(resultItem);
                        console.log('Added tool to results:', tool.label);
                    }
                } catch (err) {
                    console.error('Error creating result item:', err);
                }
            });
        } catch (e) {
            console.error('Error showing all commands:', e);
        }
    }

    function filterCommands() {
        try {
            if (!commandPaletteInput || !commandPaletteResults) {
                console.error('Command palette elements not found');
                return;
            }

            const query = commandPaletteInput.value.toLowerCase();
            commandPaletteResults.innerHTML = '';

            if (query.trim() === '') {
                showAllCommands();
                return;
            }

            // Add a test item first
            addTestItem();

            // Safely filter tools
            const filteredTools = [];
            if (Array.isArray(allTools)) {
                allTools.forEach(tool => {
                    if (tool && tool.label && typeof tool.label === 'string' &&
                        tool.label.toLowerCase().includes(query)) {
                        filteredTools.push(tool);
                    }
                });
            }

            if (filteredTools.length === 0) {
                const noResults = document.createElement('div');
                noResults.className = 'no-results';
                noResults.textContent = 'No matching tools found';
                noResults.style.padding = '20px';
                noResults.style.textAlign = 'center';
                noResults.style.color = '#666';
                commandPaletteResults.appendChild(noResults);
            } else {
                console.log('Filtered tools:', filteredTools.length, 'tools found');

                filteredTools.forEach(tool => {
                    try {
                        const resultItem = createResultItem(tool);
                        commandPaletteResults.appendChild(resultItem);
                        console.log('Added filtered tool to results:', tool.label);
                    } catch (err) {
                        console.error('Error creating result item:', err);
                    }
                });
            }
        } catch (e) {
            console.error('Error filtering commands:', e);
        }
    }

    function createResultItem(tool) {
        try {
            if (!tool) {
                console.warn('Null or undefined tool passed to createResultItem');
                return document.createElement('div'); // Return empty div to avoid errors
            }

            const resultItem = document.createElement('div');
            resultItem.className = 'result-item';

            // Get icon class and background color safely
            let iconClass = 'fa-solid fa-circle';
            let bgColor = tool.bgColor || '#4285f4'; // Use the bgColor from the tool object or default to blue
            try {
                if (tool.element && tool.element.querySelector && tool.element.querySelector('i')) {
                    const iconElement = tool.element.querySelector('i');
                    iconClass = iconElement.className || iconClass;
                }
            } catch (e) {
                console.error('Error getting icon class:', e);
            }

            // Safely get the label
            const label = tool.label ? tool.label : 'Unknown Tool';

            // Create elements manually instead of using innerHTML
            const iconDiv = document.createElement('div');
            iconDiv.className = 'result-icon';

            const icon = document.createElement('i');
            icon.className = iconClass;
            icon.style.backgroundColor = bgColor;
            iconDiv.appendChild(icon);

            const labelDiv = document.createElement('div');
            labelDiv.className = 'result-label';
            labelDiv.textContent = label;
            labelDiv.style.color = '#333';
            labelDiv.style.visibility = 'visible';

            // Clear and append children
            resultItem.innerHTML = '';
            resultItem.appendChild(iconDiv);
            resultItem.appendChild(labelDiv);

            resultItem.addEventListener('click', function() {
                closeCommandPalette();
                // Execute the tool's onclick function if available
                if (tool.onClick) {
                    try {
                        eval(tool.onClick);
                    } catch (e) {
                        console.error('Error executing tool onClick:', e);
                    }
                }
            });

            return resultItem;
        } catch (err) {
            console.error('Error in createResultItem:', err);
            return document.createElement('div'); // Return empty div to avoid errors
        }
    }

    function handleCommandNavigation(e) {
        try {
            if (!commandPaletteResults) {
                console.error('Command palette results element not found');
                return;
            }

            const items = commandPaletteResults.querySelectorAll('.result-item');
            const activeItem = commandPaletteResults.querySelector('.result-item.active');
            let activeIndex = -1;

            if (activeItem) {
                activeIndex = Array.from(items).indexOf(activeItem);
            }

            // Handle arrow keys
            if (e.key === 'ArrowDown') {
                e.preventDefault();
                if (items && activeIndex < items.length - 1) {
                    if (activeItem) activeItem.classList.remove('active');
                    if (items[activeIndex + 1]) {
                        items[activeIndex + 1].classList.add('active');
                        items[activeIndex + 1].scrollIntoView({ block: 'nearest' });
                    }
                }
            } else if (e.key === 'ArrowUp') {
                e.preventDefault();
                if (items && activeIndex > 0) {
                    if (activeItem) activeItem.classList.remove('active');
                    if (items[activeIndex - 1]) {
                        items[activeIndex - 1].classList.add('active');
                        items[activeIndex - 1].scrollIntoView({ block: 'nearest' });
                    }
                }
            } else if (e.key === 'Enter' && activeItem) {
                e.preventDefault();
                activeItem.click();
            } else if (e.key === 'Escape') {
                e.preventDefault();
                closeCommandPalette();
            }
        } catch (e) {
            console.error('Error handling command navigation:', e);
        }
    }

    function handleKeyboardShortcut(e) {
        try {
            // Check for Cmd+K or Ctrl+K
            if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
                e.preventDefault();
                openCommandPalette();
            } else if (e.key === 'Escape' && commandPalette && commandPalette.style.display === 'block') {
                closeCommandPalette();
            }
        } catch (err) {
            console.error('Error handling keyboard shortcut:', err);
        }
    }

    // Function to scroll to top
    function scrollToTop() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    }

    // Function to handle scroll events
    function handleScroll() {
        const backToTopButton = document.getElementById('back-to-top');
        if (!backToTopButton) return;

        if (window.scrollY > 300) {
            backToTopButton.style.display = 'flex';
        } else {
            backToTopButton.style.display = 'none';
        }
    }

    document.addEventListener('DOMContentLoaded', function() {
        // Initialize Lucide icons
        lucide.createIcons();

        // Apply colors to all button icon containers
        const iconContainers = document.querySelectorAll('.ai-prompt-btn .icon-container');
        iconContainers.forEach((container, index) => {
            // Use modulo to cycle through colors if we have more buttons than colors
            const colorIndex = index % colorArray.length;
            if (!container.style.backgroundColor) {
                container.style.backgroundColor = colorArray[colorIndex];
            }
        });

        // Remove pulse animation after 5 seconds
        setTimeout(function() {
            const bookViewBtn = document.getElementById('bookViewBtn');
            if (bookViewBtn) {
                bookViewBtn.classList.remove('pulse-animation');
            }
        }, 5000);

        // Create command palette
        createCommandPalette();

        // Chapter select change handler
        const chapterSelect = document.getElementById('chapterSelect');
        if (chapterSelect) {
            chapterSelect.addEventListener('change', function() {
                const selectedValue = this.value;
                if (selectedValue) {
                    const [chapterId, resId] = selectedValue.split('_');
                    gptResId = resId;
                    gptChapterId = chapterId;
                    namespace = chapterId+"_"+resId;

                    console.log('Selected chapter ID:', chapterId, 'Resource ID:', resId);

                    // Hide warning and remove highlight
                    const chapterSelectWarning = document.getElementById('chapter-select-warning');
                    if (chapterSelectWarning) {
                        chapterSelectWarning.style.display = 'none';
                    }
                    chapterSelect.classList.remove('highlight-select');

                    // Clear the chat when chapter is changed
                    if (typeof clearChat === 'function') {
                        console.log('Clearing chat due to chapter change');
                      //  clearChat();
                        getPromptsList();
                    } else {
                        console.log('clearChat function not available');
                    }

                    // Hide all advanced tools sections when chapter changes
                    hideAllAdvancedToolsSections();
                }
            });
        }

        // Add scroll event listener for back-to-top button
        window.addEventListener('scroll', handleScroll);

        // Initialize the back-to-top button state
        handleScroll();

        // Section toggle handlers
        const sectionToggles = document.querySelectorAll('.section-toggle');

        sectionToggles.forEach(toggle => {
            toggle.addEventListener('click', function() {
                const content = this.closest('.ai-section').querySelector('.ai-prompt-buttons, .advanced-tools-content');
                const chevron = this.querySelector('.section-chevron');

                if (content) {
                    if (content.classList.contains('advanced-tools-content')) {
                        content.classList.toggle('active');
                        content.style.display = content.classList.contains('active') ? 'block' : 'none';
                    } else {
                        content.style.display = content.style.display === 'none' ? 'block' : 'none';
                    }

                    if (chevron) {
                        chevron.classList.toggle('active');
                    }
                }
            });
        });

        // Accordion handlers
        const accordionHeaders = document.querySelectorAll('.accordion-header');

        accordionHeaders.forEach(header => {
            // Set all accordions to be open by default
            const accordionContent = header.nextElementSibling;
            accordionContent.style.display = 'block';

            header.addEventListener('click', function() {
                const accordionContent = this.nextElementSibling;
                const accordionIcon = this.querySelector('.accordion-icon');

                // Toggle the current accordion
                accordionContent.style.display = accordionContent.style.display === 'none' ? 'block' : 'none';
                accordionIcon.classList.toggle('active');
            });
        });
    });

    function getSuggestedVideos(){
        //make the ajax api call  to /prompt/getSuggestedVideos parameters chapterId
        $.ajax({
            url: '${createLink(controller: 'prompt', action: 'getSuggestedVideos')}',
            data: { chapterId: gptChapterId },
            success: function (data) {
                // Show chat window if it's hidden
                if (typeof showChatWindowCB === 'function') {
                    showChatWindowCB();
                }
                videoResponseHandler(JSON.parse(data.suggestedVideos));

            },
            error: function (xhr, status, error) {
                console.error('Error calling API:', error);
                console.error('Status:', status);



    }
    });
    }
</script>


