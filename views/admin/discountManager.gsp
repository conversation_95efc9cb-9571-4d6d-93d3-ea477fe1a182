<%@ page import="javax.servlet.http.Cookie" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />
<link href="https://cdn.datatables.net/1.10.20/css/dataTables.bootstrap4.min.css" type="text/css" rel="stylesheet">
<link href="https://cdn.jsdelivr.net/gh/gitbrent/bootstrap4-toggle@3.6.1/css/bootstrap4-toggle.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css" rel="stylesheet" type="text/css">

<style>
    @media only screen and (max-width: 767px){
        .discount-manager-header{
            flex-direction: column !important;
        }

        .discount-manager .status-filter-toggle{
            margin: 0 !important;
        }
    }
        .dataTables_wrapper > .row:nth-child(2){
            overflow-x:scroll;
        }
    #DataTables_Table_0{
        width: 100% !important;
    }
</style>

<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="discount-manager row justify-content-center p-5" style="min-height: 550px;">
    <div class="container-fluid">
        <div class="d-flex justify-content-around align-items-center discount-manager-header">
            <div class="w-100">
                <h3><strong>Discount Manager</strong></h3>
                <div class="d-flex align-items-center justify-content-left mt-4">
                    Type:<select id="actionType" name="actionType" class="form-control col-md-3 ml-3" onchange="actionTypeCall();">
                    <option value="addDiscount">Add Discount</option>
                    <option value="viewDiscounts">View Discounts</option>
                </select>
                </div>
            </div>
            </div>
        </div>
        <div class='col-md-12 main' style=" margin: 40px auto; float: none; padding: 15px;">
            <div id="content-books">
                <div class="p-3" id="addDiscounts">
                    <h5 class="text-center"><strong>Add Discount</strong></h5>
                    <div class="flex_st d-flex pt-4">

                        <div class="col-md-4">
                            <label>Select Type:</label>
                            <select id="type" name="type" class="form-control" onchange="typeOptionChanged(this);">
                                <option value="" disabled="disabled" selected>Select</option>
                                <option value="New Users" id="newusr">New Users</option>
                                <option value="Book Level" id="bookcrs">Book/Course level</option>
                                <option value="Reference Code" id="refcode">Reference Code</option>
                            </select>
                        </div>

                        <div class="col-md-4">
                            <label>Name</label>
                            <input type="text" class="form-control admin" name="discountName" id="discountName"  placeholder="Enter a name">
                        </div>
                        <div class="publist col-md-4 w-100" >
                            <% if(session["userdetails"].publisherId==null) {%>
                            <label>Select Publisher</label>
                            <div class="d-flex align-items-center justify-content-left">
                            <g:select id="publisherId" class="form-control" optionKey="id" optionValue="name"
                                                 name="publisherId" from="${publishers}" noSelection="${['':'Select']}" class="form-control"/>
                            </div>
                            <%  } %>
                        </div>
                    </div>
                    <br>
                    <div class="flex_st d-flex">
                        <div class="col-md-6">
                            <label>Start Date</label>
                            <input type="text" class="form-control admin" id="startDate" name="startDate" placeholder="Select" size="10" autocomplete="off">
                        </div>
                        <div class="col-md-6">
                            <label>End Date</label>
                            <input type="text" class="form-control admin" id="endDate" name="endDate" placeholder="Select" size="10" autocomplete="off">
                        </div>
                    </div>
                    <br>
                    <div class="flex_st d-flex">
                        <div class="col-md-4">
                            <label>Percentage</label>
                            <input type="number" min="1" max="99" class="form-control admin" id="percentage" name="percentage" placeholder="Enter discount %"  autocomplete="off" onkeypress="return onlyNumberKey(event)">
                        </div>

                        <div class="col-md-4">
                            <label>Value (In Rs)</label>
                            <div class="row m-0 p-0 align-items-center justify-content-start">
                                <input type="number" min="1" class="form-control admin" id="amount" name="amount" placeholder="Enter discount value"  autocomplete="off" onkeypress="return onlyNumberKey(event)">
                            </div>
                        </div>

                        <div class="col-md-4" id="redeemcountdiv">
                            <label>Redeem count</label>
                            <div class="row m-0 p-0 align-items-center justify-content-start">
                                <input type="number" class="form-control admin" id="redeemCount" name="redeemCount" placeholder="Enter redeem count"  autocomplete="off" onkeypress="return onlyNumberKey(event)">
                            </div>
                        </div>

                    </div>

                    <div id="discountTable" class="col-md-12 pt-4" style="display:none;">
                        <h5 class="text-center mb-3">(or)</h5>
                        <table class="table table-bordered" id="getDiscounts">
                        <thead class="bg-primary text-white">
                        <tr>
                            <th data-header="spentupto">Spent Upto (In Rs)</th>
                            <th data-header="discperc">Discount Percentage</th>
                            <th data-header="discvalue">Discount Value (In Rs)</th>
                        </tr>
                        </thead>
                        <tbody>
                        <% for (int i = 0; i < 4; i++) { %>
                        <tr>
                            <td><input type="number" min="1" class="form-control" id="spent${(i + 1)}" onkeypress="return onlyNumberKey(event)"> </td>
                            <td><input type="number" min="1" max="99" class="form-control" id="disper${(i + 1)}" onkeypress="return onlyNumberKey(event)"></td>
                            <td><input type="number" min="1" class="form-control" id="amnt${(i + 1)}" onkeypress="return onlyNumberKey(event)"> </td>
                        </tr>
                        <% } %>
                        </tbody>
                    </table>
                    </div>
                    <br>
                    <div class="flex_st d-flex">
                        <div class="col-md-5 lib2">
                            <div class="form-check form-check-inline yes-all-books">
                                <input class="form-check-input" type="radio" name="allBooks" id="yes" value="true" style="margin-top: 2px;">
                                <label class="form-check-label" for="yes">All Books</label>
                            </div>
                            <div class="form-check form-check-inline no-all-books">
                                <input class="form-check-input" type="radio" name="allBooks" id="no" value="false" style="margin-top: 2px;" checked>
                                <label class="form-check-label" for="no">Selected Books</label>
                            </div>
                        </div>
                    </div>
                    <br>
                    <div class="flex_st d-flex">
                      <div class="col-md-12">
                          <textarea class="form-control admin" name="bookId" id="bookId" placeholder="Comma seperated book ids"></textarea>
                      </div>
                    </div>
                    <br>
                    <div id="couponCodes" class="col-md-6">
                        <label>Coupon Code</label>
                        <input type="text" class="form-control admin text-uppercase" name="couponCode" id="couponCode" placeholder="Enter a coupon code">
                    </div>
                    <br>
                    <div class="col-md-12">
                        <button class="btn btn-lg btn-primary" id="addDiscount" onclick="addDiscount();">
                            Add Discount
                        </button>
                    </div>

                    <div class="col-md-12">
                        <div id="errormsg1" class="alert alert-danger p-2 mt-4 m-0" role="alert" style="display: none;"></div>
                        <div id="successmsg" class="alert alert-success p-2 mt-4 m-0" role="alert" style="display: none"></div>
                    </div>

                </div>

            </div>

            <div class="text-center status-filter-toggle pt-4" style="display: none;">
                <div class="form-check-inline">
                    <label class="form-check-label">Show</label>
                </div>
                <div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" name="statusFilter" id="active" value="active" checked onchange="showActiveStatus(this.value)" style="margin-top: 2px;">
                    <label class="form-check-label" for="active">Active</label>
                </div>
                <div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" name="statusFilter" id="closed" value="closed" onchange="showActiveStatus(this.value)" style="margin-top: 2px;">
                    <label class="form-check-label" for="closed">Closed</label>
                </div>
            </div>
            <div id="discountList" class="pb-4" style="display: none;"></div>

        </div>

    </div>
</div>



<g:render template="/${session['entryController']}/footer_new"></g:render>
<script src="https://cdn.datatables.net/1.10.20/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.10.20/js/dataTables.bootstrap4.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/js/bootstrap-datepicker.min.js"></script>
<script src="https://cdn.jsdelivr.net/gh/gitbrent/bootstrap4-toggle@3.6.1/js/bootstrap4-toggle.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js"></script>

<script>
    var instituteLibrary= "${instituteLibrary}"
    $('#startDate, #endDate').datepicker({
        futureOnly: true,
        format: 'dd-mm-yyyy',
        startDate: new Date(),
        maxViewMode: 1,
        autoclose: true,
        todayHighlight: true,
        keyboardNavigation: false,
        orientation: "bottom left"
    }).on('change', function () {
        $("#errormsg1").hide();
    });
    function actionTypeCall(){
        var action = document.getElementById("actionType")[document.getElementById("actionType").selectedIndex].value;
        if(action == "viewDiscounts") {
            $("#addDiscounts").hide();
            $("#errormsg1").hide();
            $("#successmsg").hide();
            var statusSelector = $("input[name='statusFilter']:checked").val();
            showActiveStatus(statusSelector);
        }else if(action == "addDiscount"){
            $("#addDiscounts").show();
            $("#discountList, .status-filter-toggle").hide();
            $('.loading-icon').removeClass('hidden');
            setTimeout(function() {
                $('.loading-icon').addClass('hidden');
            }, 1000);
        }
    }


    function showDiscounts(data){
        $('.loading-icon').addClass('hidden');
        if(data.status=="OK") {
            var htmlStr = "<table class='table table-striped table-bordered'>\n" +
                "           <thead class='bg-primary text-white text-center'> <tr>\n" +
                "                            <th>Name</th>\n" +
                "                            <th>Type</th>\n" +
                "                            <th>Coupon Code</th>\n" +
                "                            <th>Added By</th>\n" +
                "                            <th>Date Created</th>\n" +
                "                            <th>Start Date</th>\n" +
                "                            <th>End Date</th>\n" +
                "                            <th>Status</th>\n" +
                "                            <th>Discount Value<br>(In Rs)</th>";
                if(instituteLibrary == "true"){
                   htmlStr += "                            <th>Spent Upto<br>(In Rs)</th>"
                }
             htmlStr +=   "                            <th>Discount %</th>\n";
                if(instituteLibrary !="true"){
                 htmlStr +=   "                            <th id='redeemCol'>Redeem count </th>\n";
                }
              htmlStr +=  "                            <th>Publisher</th>\n" +
                "                            <th>Book Id</th>\n" +
                "                            <th>Action</th>\n" +
                "                        </tr> </thead>\n";
        }
        if(data.status=="OK"){
            var discountList = data.discountList;
            var redeemcount;
            var publisherName;
            for(i=0;i<discountList.length;i++){

                if (discountList[i].redeemCount == null){
                    redeemcount = '';
                }else {
                    redeemcount = discountList[i].redeemCount;
                }

                if (discountList[i].publisherId !=null){
                    publisherName = discountList[i].publisherId;
                }
                if (discountList[i].publisherId == ""){
                    publisherName = "All Publisher";
                }

                htmlStr +="<tr>" +
                    "<td style='text-transform:capitalize;'>"+discountList[i].name+"</td>"+
                    "<td>"+discountList[i].type+"</td>"+
                    "<td>"+discountList[i].couponCode+"</td>"+
                    "<td>"+discountList[i].createdBy+"</td>"+
                    "<td>"+discountList[i].dateCreated+"</td>"+
                    "<td>"+discountList[i].startDate+"</td>"+
                    "<td>"+discountList[i].endDate+"</td>"+
                    "<td>"+discountList[i].status+"</td>"+
                    "<td>"+discountList[i].discountValue+"</td>";
                    if(instituteLibrary == "true"){
                    htmlStr +=   "<td>"+discountList[i].spentUpto+"</td>";
                    }

                htmlStr +=   "<td>"+discountList[i].discountPercentage+"</td>";

                    if(instituteLibrary != "true"){
                     htmlStr +=  "<td id='redeemColval'>"+redeemcount+"</td>";
                    }
                   htmlStr += "<td>"+publisherName+"</td>"+
                    "<td>"+discountList[i].bookId+"</td>";
                    if(discountList[i].status=="active") {
                        htmlStr +=   "<td><a class='btn btn-sm btn-outline-primary' href='javascript:closeDiscount(" + discountList[i].discountId + ");' style='font-weight: normal;'>CLOSE</a></td>"
                    }else{
                        htmlStr +=   "<td>CLOSED</td>"
                    }
                htmlStr +="</tr>";
            }
            htmlStr +="                        \n" +
                "                    </table>";
            document.getElementById("discountList").innerHTML= htmlStr;
            $('#discountList table').DataTable( {
                "ordering": false,
                "language": {
                    "searchPlaceholder": "Name, Book id, etc..."
                }
            });
        }else{
            document.getElementById("discountList").innerHTML= "<h6 class='mt-3'>No discounts found!</h6>";
        }
        $("#discountList, .status-filter-toggle").show();
    }

    function closeDiscount(id){
        swal({
                title: "Are you sure?",
                text: "Do you want to go ahead and close this discount?",
                type: "warning",
                allowOutsideClick: true,
                showConfirmButton: true,
                showCancelButton: true,
                confirmButtonColor: "#F79420",
                confirmButtonText: "Yes, close it!",
                cancelButtonText: "Cancel",
                closeOnConfirm: false,
                closeOnCancel: true,
                allowEscapeKey: false,
                customClass: '',
            },
            function(isConfirm){
                if (isConfirm) {
                    swal({
                        title: "Closed",
                        text: "Discount closed successfully!",
                        type: "success",
                        confirmButtonColor: "#F79420",
                        allowEscapeKey: false,
                    }, function() {
                        <g:remoteFunction controller="admin" action="closeDiscountById" params="'discountId='+id" onSuccess = "discountClosed(data);"/>
                    });
                }
            });
    }

    function discountClosed(data){
        if(data.status=="OK"){
            var statusSelector = $("input[name='statusFilter']:checked").val();
            showActiveStatus(statusSelector);
        }
    }



    function discountSlab() {
        var array = [];
        var headers = [];
        $('table#getDiscounts tr th').each(function(index, item) {
            headers[index] = $(item).attr('data-header');
        });
        $('table#getDiscounts tr').has('td').each(function() {
            var arrayItem = {};
            $('td', $(this)).each(function(index, item) {
                if($(item).find('input').val() != "") {
                    arrayItem[headers[index]] = $(item).find('input').val();
                }
            });
            array.push(arrayItem);
        });

        var tableData = array.filter(el => Object.keys(el).length);
        if (tableData.length != 0) tableData=JSON.stringify(tableData);
        return tableData;
    }

    function addDiscount() {
        var getDiscounts = [];
        var inValidStartDate = false;
        var isValidTable = false;
        var allBooksCheck = $("input[name='allBooks']:checked").val();
        var type = document.getElementById("type");
        var selectedValue=type[type.selectedIndex].value;
        var discountData=discountSlab();
        var publisherId = document.getElementById("publisherId");
        var redeemCount = document.getElementById('redeemCount').value;

        if (document.getElementById("endDate").value != "" && document.getElementById("startDate").value != "") {
            var ensDates = document.getElementById("endDate").value;
           var  startDates = document.getElementById("startDate").value;
            ensDates = new Date(ensDates.split("-").reverse().join("-"));
            startDates = new Date(startDates.split("-").reverse().join("-"));
            if (ensDates.getTime() < startDates.getTime()) inValidStartDate = true;
        }

        <% if(session["userdetails"].publisherId==null) {%>
        var selectedPublisherId = publisherId[publisherId.selectedIndex].value;

        if (selectedPublisherId =='' || selectedPublisherId == null){
            selectedPublisherId = '';
        }
        <%  } else{%>
        var selectedPublisherId ="${session["userdetails"].publisherId}";
        <%  } %>

        if (($("#spent1").val()!="" && $("#disper1").val()=="" && $("#amnt1").val()=="")
            || ($("#spent2").val()!="" && $("#disper2").val()=="" && $("#amnt2").val()=="")
            || ($("#spent3").val()!="" && $("#disper3").val()=="" && $("#amnt3").val()=="")
            || ($("#spent4").val()!="" && $("#disper4").val()=="" && $("#amnt4").val()=="")) {
            isValidTable = false;
        } else if (($("#spent1").val()=="" && ($("#disper1").val()!="" || $("#amnt1").val()!=""))
            || ($("#spent2").val()=="" && ($("#disper2").val()!="" || $("#amnt2").val()!=""))
            || ($("#spent3").val()=="" && ($("#disper3").val()!="" || $("#amnt3").val()!=""))
            || ($("#spent4").val()=="" && ($("#disper4").val()!="" || $("#amnt4").val()!=""))) {
            isValidTable = false;
        } else {
            isValidTable = true;
        }

        var hasZeroValue = false;
        $("#getDiscounts input").each(function() {
            if(this.value != "" & this.value == 0) {
                hasZeroValue = true;
            }
        });

        var hasValidBookIds = true;
        if (document.getElementById("bookId").value != "" && document.getElementById("bookId").value.match(/[^\d,]/g,'')) {
            hasValidBookIds = false;
        }

        if (selectedValue == "") {
            document.getElementById("errormsg1").innerHTML = "Please select type."
            $("#errormsg1").show();
            $("#type").focus();
        } else if (document.getElementById("discountName").value == "") {
            document.getElementById("errormsg1").innerHTML = "Please enter the name."
            $("#errormsg1").show();
            $("#discountName").focus();
        } else if (document.getElementById("startDate").value == "") {
            document.getElementById("errormsg1").innerHTML = "Please select start date."
            $("#errormsg1").show();
            $("#startDate").focus();
        } else if (inValidStartDate) {
            document.getElementById("errormsg1").innerHTML = "Please enter valid start date. Start date cannot be greater then end date.";
            $("#errormsg1").show();
            $("#startDate").focus();
        } else if (selectedValue != "Book Level" && document.getElementById("percentage").value == "" && document.getElementById("amount").value == "") {
            document.getElementById("errormsg1").innerHTML = "Please enter percentage or value."
            $("#errormsg1").show();
        } else if (selectedValue != "Book Level" && document.getElementById("percentage").value != "" && document.getElementById("amount").value != "") {
            document.getElementById("errormsg1").innerHTML = "Please enter either percentage or value."
        } else if (document.getElementById("percentage").value != "" && document.getElementById("percentage").value == 0) {
            document.getElementById("errormsg1").innerHTML = "The percentage must be above 0."
            $("#errormsg1").show();
            $("#percentage").focus();
        } else if (document.getElementById("amount").value != "" && document.getElementById("amount").value == 0) {
            document.getElementById("errormsg1").innerHTML = "The discount value must be above 0."
            $("#errormsg1").show();
            $("#amount").focus();
        } else if (selectedValue == "Book Level" && (document.getElementById("percentage").value == "" && document.getElementById("amount").value == "") && (discountData.length <= 0)) {
            document.getElementById("errormsg1").innerHTML = "Please enter either percentage or value (OR) spent amount with percentage or value."
            $("#errormsg1").show();
        } else if (selectedValue == "Book Level" && !isValidTable) {
            document.getElementById("errormsg1").innerHTML = "Please enter spent amount with either percentage or value."
            $("#errormsg1").show();
        } else if (hasZeroValue) {
            document.getElementById("errormsg1").innerHTML = "Spent amount, percentage and values are must be above 0."
            $("#errormsg1").show();
        } else if (document.getElementById("bookId").value == "" && allBooksCheck == "false") {
            document.getElementById("errormsg1").innerHTML = "Please enter book ids."
            $("#errormsg1").show();
            $("#bookId").focus();
        } else if (document.getElementById("bookId").value != "" && allBooksCheck == "false" && !hasValidBookIds) {
            document.getElementById("errormsg1").innerHTML = "Please enter correct book ids."
            $("#errormsg1").show();
            $("#bookId").focus();
        } else {
            $("#errormsg1").hide();
            $('.loading-icon').removeClass('hidden');
            var type=selectedValue;
            var allBooks = allBooksCheck;
            var name=document.getElementById("discountName").value;
            name = encodeURIComponent(name);
            var percentage=document.getElementById("percentage").value;
            var startDate=document.getElementById("startDate").value;
            var endDate=document.getElementById("endDate").value;
            endDate = endDate.split("-").reverse().join("-");
            startDate=startDate.split("-").reverse().join("-");
            var amount=document.getElementById("amount").value;
            var bookId=document.getElementById("bookId").value;
            var couponCode=document.getElementById("couponCode").value;
            var couponCodeUppercase = couponCode.toUpperCase();

            var addDiscount;

            if (siteId !=21 && siteId !=34){
                addDiscount = 'addDiscountWS';
            }else{
                addDiscount = 'addDiscount';
            }

            <g:remoteFunction controller="admin" action="'+addDiscount+'"
            params="'discountName='+name+'&endDate='+endDate+'&discountPercentage='+percentage+'&discountValue='+amount+'&startDate='+startDate+'&type='+type+'&allBooks='+allBooks+'&bookId='+bookId+'&couponCode='+couponCodeUppercase+'&discountData='+discountData+'&publisherId='+selectedPublisherId+'&redeemCount='+redeemCount" onSuccess = "discountAdded(data);"/>
        }
    }

    function  discountAdded(data){
        if(data.status="OK" && data.invalidBookId=="" && data.existCouponCode!="codeExist") {
            $('.loading-icon').addClass('hidden');
            document.getElementById("successmsg").innerHTML ="Added successfully!"
            $("#successmsg").show();
            setTimeout(function() {
                $('#successmsg').fadeOut('slow');
            }, 5000);
            $("#addDiscounts input[type='number'], #addDiscounts input[type='text'], #bookId").val("").prop('disabled', false);
            document.getElementById("no").checked = true;
        }else if(data.status="OK" && data.invalidBookId!="" && data.existCouponCode!="codeExist") {
            $('.loading-icon').addClass('hidden');
            document.getElementById("successmsg").innerHTML = "Added successfully, incorrect book id(s) are "+data.invalidBookId;
            document.getElementById("no").checked = true;
            $("#successmsg").show();
            setTimeout(function() {
                $('#successmsg').fadeOut('slow');
            }, 5000);
            $("#addDiscounts input[type='number'], #addDiscounts input[type='text'], #bookId").val("").prop('disabled', false);
        }else {
            $('.loading-icon').addClass('hidden');
            document.getElementById("errormsg1").innerHTML = "Coupon code already exist! Please enter different coupon code."
            document.getElementById("no").checked = true;
            $("#errormsg1").show();
            $("#couponCode").focus();
            $('#redeemCount').val('')
        }
    }
    function onlyNumberKey(evt) {
        // Only ASCII charactar in that range allowed
        var ASCIICode = (evt.which) ? evt.which : evt.keyCode
        if (ASCIICode > 31 && (ASCIICode < 48 || ASCIICode > 57))
            return false;
        return true;
    }
</script>

<script>
    function showActiveStatus(value){
        if (value == 'active') {
            $('.loading-icon').removeClass('hidden');
            <g:remoteFunction controller="admin" action="discountManager" params="'showList=true&addStatus=active'" onSuccess = "showDiscounts(data);"/>
        } else if (value == 'closed') {
            $('.loading-icon').removeClass('hidden');
            <g:remoteFunction controller="admin" action="discountManager" params="'showList=true&addStatus=closed'" onSuccess = "showDiscounts(data);"/>
        }
    }

    function typeOptionChanged(field) {
        $("#addDiscounts input[type='number'], #addDiscounts input[type='text'], #bookId").val("").prop('disabled', false);
        $("#errormsg1").hide();
        var type = field[field.selectedIndex].value;
        if(type == "Reference Code"){
            $("#yes").trigger('click');
            $("#bookId, #discountTable, .no-all-books,#couponCodes").hide();
        } else if(type == "Book Level") {
            $("#no").trigger('click');
            $("#bookId, .no-all-books,#couponCodes").show();
        } else if(type == "New Users") {
            $("#no").trigger('click');
            $("#bookId, .no-all-books,#couponCodes").show();
            $("#discountTable").hide();
        }

        if (instituteLibrary == "true" && type == "Book Level"){
            $("#bookId, .no-all-books,#couponCodes").show();
            $("#discountTable").show()
        }
    }

    function checkEmptyInputs() {
        var num = 0;
        $("#getDiscounts input").each(function() {
            if(this.value == "" && !isNaN(this.value)) {
                ++num;
            }
        });
        num == 12 ? $('#percentage, #amount').prop('disabled', false) : $('#percentage, #amount').prop('disabled', true);
    }

    $(document).ready(function () {

        // For discount percentage
        $("#percentage").keyup(function (e) {
            $("#errormsg1").hide();
            e.target.value.length > 0 ? $('#amount, #getDiscounts input').prop('disabled', true) : $('#amount, #getDiscounts input').prop('disabled', false);
        }).on('keypress', function () {
            if(this.value.length==2) return false;
        });

        // For discount value
        $("#amount").keyup(function (e) {
            $("#errormsg1").hide();
            e.target.value.length > 0 ? $('#percentage, #getDiscounts input').prop('disabled', true) : $('#percentage, #getDiscounts input').prop('disabled', false);
        }).on('keypress', function () {
            if(this.value.length==6) return false;
        });

        // Table inputs condition
        for (var i = 0; i<4; i++) {
            $("#disper"+(i+1)).on('keyup', function (e) {
                $("#errormsg1").hide();
                var inputId = $(this).attr('id');
                var idNumber = parseInt(inputId.replace(/[^0-9.]/g, ""));
                //e.target.value.length > 0 ? $("#amnt"+idNumber).prop('disabled', true) : $("#amnt"+idNumber).prop('disabled', false);
            }).on('keypress', function () {
                if(this.value.length==2) return false;
            });

            $("#amnt"+(i+1)).on('keyup', function (e) {
                $("#errormsg1").hide();
                var inputId = $(this).attr('id');
                var idNumber = parseInt(inputId.replace(/[^0-9.]/g, ""));
                //e.target.value.length > 0 ? $("#disper"+idNumber).prop('disabled', true) : $("#disper"+idNumber).prop('disabled', false);
            }).on('keypress', function () {
                if(this.value.length==6) return false;
            });

            $("#spent"+(i+1)).on('keypress', function () {
                $("#errormsg1").hide();
                if(this.value.length==6) return false;
            });
        }

        // Disable mouse wheel for number inputs
        $('input[type="number"]').on('wheel', function () {
            $(this).blur();
        });


        // Check table inputs empty or not
        $("#getDiscounts input").each(function() {
            $(this).keyup(function(){
                checkEmptyInputs();
            });
        });

        $("#discountName, #bookId").keyup(function () {
            $("#errormsg1").hide();
        });

        $("#couponCode").keyup(function () {
            $("#errormsg1").hide();
            this.value = this.value.replace(/[^a-zA-Z0-9]/g, '');
        });
        $("#getDiscounts tr td:nth-child(3) input").each(function() {
            $(this).keyup(function(){
                checkSlabDiscountValue();
            });
        });
        $("#getDiscounts tr td:nth-child(2) input").each(function() {
            $(this).keyup(function(){
                checkSlabPercentValue();
            });
        });
        
    });

    function checkSlabDiscountValue() {
        var discEmpty = 0;
        $("#getDiscounts tr td:nth-child(3) input").each(function() {
            if(this.value == "" && !isNaN(this.value)) {
                ++discEmpty;
            }
        });
        discEmpty == 4 ? $("#getDiscounts tr td:nth-child(2) input").prop('disabled', false) : $("#getDiscounts tr td:nth-child(2) input").prop('disabled', true);
    }
    
    function checkSlabPercentValue() {
        var percentEmpty = 0;
        $("#getDiscounts tr td:nth-child(2) input").each(function() {
            if(this.value == "" && !isNaN(this.value)) {
                ++percentEmpty;
            }
        });
        percentEmpty == 4 ? $("#getDiscounts tr td:nth-child(3) input").prop('disabled', false) : $("#getDiscounts tr td:nth-child(3) input").prop('disabled', true);
    }

</script>
<script>
    var siteId ="${session['siteId']}";

    //Hiding publisher dropdown for winners(siteId=21)
    if (instituteLibrary != "true"){
        $('.publist').show();
        $('#refcode').hide();
        $('#newusr').hide();
    }else {
        $('#redeemcountdiv').hide();
        $('.publist').hide();

    }

    // If all books selected disabling redeem count, and clearing bookId and hiding
    $('input[name=allBooks]').click(function () {
        if (this.id == "yes") {
            $('#bookId').val('');
            $('#redeemCount').val('')
            $("#bookId").hide();
            $("#errormsg1").hide();
            $('#redeemCount').attr('disabled','disabled');
        }else {
            $('#redeemCount').removeAttr('disabled');
            $("#bookId").show();
        }
    });

</script>