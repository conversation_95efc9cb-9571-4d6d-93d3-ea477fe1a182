<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Document</title>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css" integrity="sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm" crossorigin="anonymous">
    <link href="https://fonts.googleapis.com/css?family=Rubik:400,500" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css?family=Merriweather:300,400,700" rel="stylesheet">

    <asset:stylesheet src="autovideo/style.css"/>

</head>
<body>
<div id="overlay"></div>
<div >
    <audio id="audioElement"></audio>
    <audio id="clickAudioElement" src="/funlearn/showImage?id=1&fileName=clockTicking.mp3&imgType=audio"></audio>
    <audio id="backgroundAudioElement" loop="loop"></audio>
    <input type="hidden" id="placeHolder">
    <div id="slider">
        <section class="ws-title-wrapper" id="1" >
            <div class="ws-title d-flex justify-content-center align-items-center">
                <div>
                    <div class="img-wrapper">
                        <img src="/assets/autovideo/wslogo.svg" class="img-responsive">
                        <p>Always Keep Learning !</p>
                    </div>
                    <div class="form-wrapper">
                        <textarea id="quote" placeholder="Quote"></textarea><br>
                        <input type="text" id="quoteBy" placeholder="Quote By"><br>
                        <select id="language"><option>Select Language</option><option value="English">English</option><option value="Hindi">Hindi</option></select><br>
                        <select id="client"><option value="wonderslate">Wonderslate</option><option value="arivupro">Arivupro</option><option value="edugorilla">Edugorilla</option><option value="prepjoy">PrepJoy</option></select><br>
                        <select id="modes"><option value="read">Read Mode</option><option value="quiz">quiz Mode</option></select>
                    </div>
                </div>
            </div>

        </section>
        <section class="ws-title-wrapper" id="2" style="display: none">
            <div class="ws-title d-flex justify-content-center align-items-center">
                <div class="img-wrapper" style="text-align: center;display: none" id="wonderslate">
                    <img src="/assets/autovideo/wslogo.svg" class="img-responsive">
                </div>
                <div class="img-wrapper" style="text-align: center;display: none" id="arivupro">
                    <img src="/assets/autovideo/arivupro.png" class="img-responsive">
                    <br><img src="/assets/autovideo/wslogo.svg" class="img-responsive">
                </div>
                <div class="img-wrapper" style="text-align: center;display: none" id="prepjoy">
                    <img src="/assets/autovideo/prepjoy.png" class="img-responsive">
                </div>
            </div>
            <div class="ws-link d-flex justify-content-end" >
                <div id="wonderslateLink" style="display:none">
                    <a href="#">www.wonderslate.com</a>
                </div>
                <div  id="arivuproLink" style="display:none">
                    <a href="#">www.arivupro.com</a><br><a href="#">www.wonderslate.com</a>
                </div>
                <div id="edugorillaLink" style="display:none">
                    <a href="#">www.edugorilla.com</a><br><a href="#">www.wonderslate.com</a>
                </div>
                <div id="prepjoyLink" style="display:none">
                    <a href="#">www.prepjoy.com</a>
                </div>
            </div>

        </section>
        <section class="start" id="3" style="display: none">
            <div class="ws-quiz d-flex justify-content-center align-items-center">
                <div>
                    <div class="content-wrapper">
                        <p><span>Topic for</span> the quiz</p>
                        <h3 id="quizTopic">â€œ HARAPPAN CIVILZATION â€�</h3>
                    </div>
                    <div class="d-flex img-wrapper timeup">
                        <img src="/assets/autovideo/ws-userimg.svg" class="img-responsive img-quiz">
                    </div>
                </div>
            </div>

            %{--<div class="ws-link d-flex justify-content-end">--}%
            %{--<img src="/assets/autovideo/wslogo.svg" class="img-responsive">--}%
            %{--</div>--}%
        </section>
        <section id="4" style="display: none">
            <div class="container">
                <div class="ws-quiz d-flex justify-content-center position-relative">
                    <div>
                        <div class="que-wrapper" style="margin:0 auto;margin-top:5rem;">
                            <h4 id="questionTracker"></h4>
                            <div class="d-flex mt-3 justify-content-center"><span id="questionIndex">Q1.</span><p class="question" id="question">the question for the quiz which will be asked and
                            answered will come at this place holder?</p>
                            </div>
                            <div class="d-flex answers justify-content-start" style="margin-top: 1.5rem;">
                                <div class="col-6 d-flex">
                                    <div id="divop1" style="display: none">
                                        <div id="opt1class" class="d-flex option-wrapper">
                                            <p class="optionname">a.</p>
                                            <p id="opt1para">The option for the above question
                                            which possibly can be the answer will
                                            come here</p>
                                            <img src="/assets/autovideo/done.svg" class="done">
                                        </div>

                                    </div>
                                </div>
                                <div class="col-6 d-flex">
                                    <div id="divop2" style="display: none">
                                        <div id="opt2class" class="d-flex option-wrapper ">
                                            <p class="optionname">b.</p>
                                            <p id="opt2para">The option for the above question
                                            which possibly can be the answer will
                                            come here</p>
                                            <img src="/assets/autovideo/done.svg" class="done">
                                        </div>

                                    </div>
                                </div>

                            </div>
                            <div class="d-flex answers justify-content-start" style="margin-top: 1.5rem;">
                                <div class="col-6 d-flex">
                                    <div id="divop3" style="display: none">

                                        <div id="opt3class" class="d-flex option-wrapper ">
                                            <p class="optionname">c.</p>
                                            <p id="opt3para">The option for the above question
                                            which possibly can be the answer will
                                            come here</p>
                                            <img src="/assets/autovideo/done.svg" class="done">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6 d-flex">
                                    <div id="divop4"  style="display: none">

                                        <div id="opt4class" class="d-flex option-wrapper ">
                                            <p class="optionname">d.</p>
                                            <p id="opt4para">The option for the above question
                                            which possibly can be the answer will
                                            come here</p>
                                            <img src="/assets/autovideo/done.svg" class="done">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="d-flex answers justify-content-start" style="margin-top: 1.5rem;">
                                <div class="col-6 d-flex">
                                    <div id="divop5" style="display: none">

                                        <div id="opt5class" class="d-flex option-wrapper ">
                                            <p class="optionname">e.</p>
                                            <p id="opt5para">The option for the above question
                                            which possibly can be the answer will
                                            come here</p>
                                            <img src="/assets/autovideo/done.svg" class="done">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6">

                                </div>
                            </div>
                            <div class="row d-flex justify-content-start">
                                <div class="col-6 d-flex">
                                    <div class="answers mt-3" id="correctAnswerMoved" style="display: none;">
                                        <div class="d-flex option-wrapper ">
                                            <p class="optionname">a.</p>
                                            <p>The option for the above question
                                            which possibly can be the answer will
                                            come here</p>
                                            <img src="/assets/autovideo/done.svg" class="done">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="explanation justify-content-center"  id="correctAnswerExplanation" style="display: none;">
                                        <div>
                                            <span class="exp">Explanation</span>
                                            <p class="exp-answer" id="answerExplanation">
                                                This the the place where the complete explanation of the quiz that will be explainded made with several steps and ways so as the user reading this able to understand and would be able to grasp it so the he kills in the examination.
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="timer-container text-center mt-4" id="timer-container" style="display: none">
                            <!--<img src="/assets/autovideo/quebgimg.svg" class="img-responsive img-answer">-->
                            <div class="timer-wrapper">

                                <div class="circle"> <svg width="54" viewBox="0 0 220 220" xmlns="http://www.w3.org/2000/svg">
                                    <g transform="translate(110,110)">
                                        <circle r="100" class="e-c-base"/>
                                        <g transform="rotate(-90)">
                                            <circle r="100" class="e-c-progress"/>
                                            <g id="e-pointer">
                                                <circle cx="100" cy="0" r="8" class="e-c-pointer"/>
                                            </g>
                                        </g>
                                    </g>
                                </svg> <div class="d-flex time-remain"><div class="display-remain-time">10</div><span>s</span></div></div>

                            </div>
                        </div>
                        <div class="img-wrapper timeup text-center mt-4" id="timeup" style="display: none">
                            <img src="/assets/autovideo/timeup.svg" class="img-responsive">
                            <h3>TIME IS UP</h3>
                        </div>
                    </div>

                </div>
                <div class="d-flex img-wrapper">
                    %{--<img src="/assets/autovideo/quebgimg.svg" class="img-responsive img-que" style="z-index: 99;">--}%
                </div>
            </div>
            <!--<div class="ws-link d-flex justify-content-end">-->
            <!--<img src="/assets/autovideo/wslogo.svg" class="img-responsive">-->
            <!--</div>-->
        </section>


        <section id="5" style="">

            <div class="ws-title thanks d-flex justify-content-center align-items-center">
                <div class="img-wrapper">

                    <blockquote>
                        <p> "Thank You for Watching !</p>
                        <div id="wonderslateEnd" style="display: none">
                            <footer>You can also practice this quiz on</footer>
                            <a href="">www.wonderslate.com</a>
                        </div>
                        <div id="prepjoyEnd" style="display: none">
                          <footer>You can also practice this quiz on</footer>
                            <a href="">www.prepjoy.com</a>
                        </div>
                    </blockquote>

                </div>
            </div>

            <div class="ws-link timeup d-flex justify-content-end">
                <div id="arivuproEndLink" style="display:none">
                    <img src="/assets/autovideo/arivupro.png" class="img-responsive">
                </div>
                <div id="wonderslateEndLink" style="display:none">
                    <img src="/assets/autovideo/wslogo.svg" class="img-responsive">
                </div>
                <div id="prepjoyEndLink" style="display:none">
                    <img src="/assets/autovideo/prepjoy.png" class="img-responsive">
                </div>
            </div>
        </section>

        <div class="button-wrapper">
            <button id="next-btn" onclick="startQuiz();">Next</button>
        </div>
    </div>
    <asset:javascript src="landingpage/jquery-3.2.1.min.js" /><script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.12.9/umd/popper.min.js" integrity="sha384-ApNbgh9B+Y1QKtv3Rn7W3mgPxhU9K/ScQsAP7hUibX39j7fakFPskvXusvfa0b4Q" crossorigin="anonymous"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js" integrity="sha384-JZR6Spejh4U02d8jOt6vLEHfe/JQGiRRSQQxSfFWpi1MquVdAyjUar5+76PVCmYl" crossorigin="anonymous"></script>
    <script src="https://sdk.amazonaws.com/js/aws-sdk-2.374.0.min.js"></script>
    <script type="text/javascript"
            src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS_HTML-full">
    </script>
    <asset:javascript src="autovideo/script.js"/>
    <script>

        //AWS POLLY config
        AWS.config.accessKeyId = "${awsAccessKeyId}";
        AWS.config.secretAccessKey = "${awsSecretAccessKey}";
        AWS.config.region = 'ap-south-1';
        //instantiating Polly class
        var polly = new AWS.Polly();
        var params = {
            OutputFormat: "mp3",
            Text:"",
            TextType: "ssml",
            VoiceId: "Aditi"
        };
        var quoteForTheDay="";
        var quoteBy="";
        var audioElement = document.getElementById('audioElement');
        var clickAudioElement= document.getElementById('clickAudioElement');
        var playNextCondition=true;
        var currentState="input";
        var quizCurrentState="question";
        var totalNumberOfQuestions;
        var objectiveTypes;
        var currentIndex=0;
        var correctOption=0;
        var correctOptionArray=[];
        var correctAnswerStringArray=[];
        var currentNumberOfOptions=0;


        function startQuiz(){
            //get the audio for quote and store
            //     quoteForTheDay = document.getElementById("quote").value;
            //     quoteBy = document.getElementById("quoteBy").value;
            //     var fullQuoteString = "Today's quote <break time=\"250ms\"/>"+quoteForTheDay+"<break time=\"250ms\"/>By <break time=\"250ms\"/>"+quoteBy;
            //     getAudioAndStore(fullQuoteString,"quoteForTheDay");
            totalNumberOfQuestions=quizData.results.length;
            objectiveTypes = quizData.results;
            $("#1").hide();
            $("#next-btn").hide();
            if(document.getElementById("client").selectedIndex==0) {
                $("#wonderslate").show();
                $("#wonderslateLink").show();
                $("#wonderslateEnd").show();
                $("#wonderslateEndLink").show();
            }
            else if(document.getElementById("client").selectedIndex==1){
                $("#arivupro").show();
                $("#arivuproLink").show();
                $("#arivuproEndLink").show();
            }
            else if(document.getElementById("client").selectedIndex==2) {
                $("#edugorilla").show();
                $("#edugorillaLink").show();
            }else if(document.getElementById("client").selectedIndex==3){
                $("#prepjoy").show();
                $("#prepjoyLink").show();
                $("#prepjoyEnd").show();
                $("#prepjoyEndLink").show();
            }
            $("#2").show();
            //    $("#quoteForTheDay").hide();
            removeContentFromLocalStorageAll();
            currentState="quote";
            document.getElementById("quizTopic").innerHTML=document.getElementById("quote").value;
            document.getElementById("placeHolder").innerHTML=document.getElementById("quote").value;
            var topicString = "Topic for today's quiz is <break time=\"250ms\"/>"+document.getElementById("quote").value;
            if("Hindi"==document.getElementById("language").value) topicString = "आज का क्विज विषय है <break time=\"250ms\"/>"+document.getElementById("quote").value;
            getAudioAndStore(topicString,"quizTopic");
            // var backgroundAudioElement =document.getElementById("backgroundAudioElement");
            // backgroundAudioElement.src = "AirportLounge.mp3";
            // backgroundAudioElement.volume="0.03";
            //  backgroundAudioElement.play();
            audioElement.src="/funlearn/showImage?id=1&fileName=intro"+document.getElementById("language").value+document.getElementById("client").value+".mp3&imgType=audio";
            audioElement.play();

        }

        function getAudioAndStore(audioText,keyName){
            params.Text="<speak>"+audioText+"</speak>";

            polly.synthesizeSpeech(params, function(err, data){

                if(err){
                }
                else{

                    var audioStream = data.AudioStream;
                    localStorage.setItem(keyName, JSON.stringify(data.AudioStream));

                }

            });
        }

        function playNext(keyName){
            $("#"+keyName).show();
            var uInt8Array = new Uint8Array(JSON.parse(localStorage.getItem(keyName)).data);
            var arrayBuffer = uInt8Array.buffer;
            var blob = new Blob([arrayBuffer]);
            var url = URL.createObjectURL(blob);
            audioElement.src = url;
            audioElement.play();
        }


        function runTopicScreen(){
            $("#2").hide();
            $("#3").show();
            playNext("quizTopic");
        }

        $("#audioElement").bind('ended', function(){

            var mjaxURL  = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML';
            // done playing
            if("input"==currentState){
                if(quoteForTheDay) {

                    document.getElementById("quoteForTheDay").innerHTML = "<p>" + quoteForTheDay + "</p><footer>-" + quoteBy + "</footer>";
                }
                playNext("quoteForTheDay");
                currentState="quote";

                //do the prep for next screen

            }
            else if("quote"==currentState){
                runTopicScreen();
                currentState="topic";
                prepareQuestions(0);
                //prepare for quiz
            }
            else if("topic"==currentState){

                showQuiz(0);
                currentState="quiz";
                quizCurrentState="question";
                //prepare for quiz
            }
            else if("quiz"==currentState){

                if("question"==quizCurrentState){
                    //prepare for next question
                    if((currentIndex+1)<objectiveTypes.length) {
                        prepareQuestions(currentIndex+1);
                    }
                    document.getElementById("opt1para").innerHTML=objectiveTypes[currentIndex].op1;
                    MathJax.Hub.Queue(["Typeset", MathJax.Hub, "divop1"]);
                    $("#divop1").delay(500).slideDown(1500);
                    playNext("option1_"+currentIndex);
                    quizCurrentState="option1";

                }
                else if("option1"==quizCurrentState){

                    document.getElementById("opt2para").innerHTML=objectiveTypes[currentIndex].op2;
                    MathJax.Hub.Queue(["Typeset", MathJax.Hub, "divop2"]);
                    $("#divop2").delay(500).slideDown(1500);
                    playNext("option2_"+currentIndex);
                    quizCurrentState="option2";

                }
                else if("option2"==quizCurrentState){
                    if(objectiveTypes[currentIndex].op3!=null&&objectiveTypes[currentIndex].op3!="") {
                        document.getElementById("opt3para").innerHTML = objectiveTypes[currentIndex].op3;
                        MathJax.Hub.Queue(["Typeset", MathJax.Hub, "divop3"]);
                        $("#divop3").delay(500).slideDown(1500);
                        playNext("option3_" + currentIndex);
                        quizCurrentState = "option3";
                    }else{

                        if(document.getElementById("modes").selectedIndex==1) {
                            startTimer();
                        }else{
                            playNext("correctAnswer_"+currentIndex);
                            quizCurrentState="correctAnswer";
                        }
                    }

                }
                else if("option3"==quizCurrentState){

                    if(objectiveTypes[currentIndex].op4!=null&&objectiveTypes[currentIndex].op4!="") {
                        document.getElementById("opt4para").innerHTML = objectiveTypes[currentIndex].op4;
                        MathJax.Hub.Queue(["Typeset", MathJax.Hub, "divop4"]);
                        $("#divop4").delay(500).slideDown(1500);
                        playNext("option4_" + currentIndex);
                        quizCurrentState = "option4";
                    }else{

                        if(document.getElementById("modes").selectedIndex==1) {
                            startTimer();
                        }else{
                            playNext("correctAnswer_"+currentIndex);
                            quizCurrentState="correctAnswer";
                        }
                    }
                }
                else if("option4"==quizCurrentState){
                    if(objectiveTypes[currentIndex].op5!=null&&objectiveTypes[currentIndex].op5!="")
                    {
                        document.getElementById("opt5para").innerHTML=objectiveTypes[currentIndex].op5;
                        MathJax.Hub.Queue(["Typeset", MathJax.Hub, "divop5"]);
                        $("#divop5").delay(500).slideDown(1500);
                        playNext("option5_" + currentIndex);
                        quizCurrentState="option5";
                    }else{
                        if(document.getElementById("modes").selectedIndex==1) {
                            startTimer();
                        }else{
                            playNext("correctAnswer_"+currentIndex);
                            quizCurrentState="correctAnswer";
                        }
                    }


                }
                else if("option5"==quizCurrentState){
                    if(document.getElementById("modes").selectedIndex==1) {
                        startTimer();
                    }else{
                        playNext("correctAnswer_"+currentIndex);
                        quizCurrentState="correctAnswer";
                    }

                }
                else if("correctAnswer"==quizCurrentState){
                    $("#opt"+correctOptionArray[currentIndex]+"class").addClass('checked');
                    playNext("option"+correctOptionArray[currentIndex]+"_"+currentIndex);
                    if(objectiveTypes[currentIndex].answerDescription) {
                        quizCurrentState = "answered";
                    }else{
                        quizCurrentState="answerExplained";
                    }
                }
                else if("answered"==quizCurrentState){
                    $("#timeup").hide();
                    $("#divop1").hide();
                    $("#divop2").hide();
                    $("#divop3").hide();
                    $("#divop4").hide();
                    $("#divop5").hide();

                    showCorrectAnswer(currentIndex);
                    playNext("answerExplanation_"+currentIndex);

                    quizCurrentState="answerExplained";
                }
                else if("answerExplained"==quizCurrentState){
                    $("#timeup").hide();
                    $("#divop1").hide();
                    $("#divop2").hide();
                    $("#divop3").hide();
                    $("#divop4").hide();
                    $("#divop5").hide();
                    if((currentIndex+1)<objectiveTypes.length){
                        if(currentIndex==(objectiveTypes.length-2)){
                            var placeHolderText=""
                            if(document.getElementById("client").selectedIndex==3) {
                                placeHolderText = "Thank you for watching the video . If you have any suggestion for us feel free to leave comments.\n" +
                                    "If you liked the video and want to see more of it hit the thumbs up and dont forget to click on subscribe button to get more updates of the future videos.";
                                if ("Hindi" == document.getElementById("language").value) placeHolderText = "वीडियो देखने के लिए धन्यवाद। यदि आपके पास हमारे लिए कोई सुझाव है तो comment करें। \ n" +
                                    "अगर आपको वीडियो पसंद आया है और इसे अधिक देखना चाहते हैं, तो अंगूठे को हिट करें और भविष्य के वीडियो के अधिक अपडेट प्राप्त करने के लिए subscribe बटन पर क्लिक करें";
                            }else{
                                placeHolderText = "Thank you for watching the video. If you'd like practice this quiz then download the wonderslate app now, link in the description below. If you have any suggestion for us feel free to leave comments.\n" +
                                    "If you liked the video and want to see more of it hit the thumbs up and dont forget to click on subscribe button to get more updates of the future videos.";
                                if ("Hindi" == document.getElementById("language").value) placeHolderText = "वीडियो देखने के लिए धन्यवाद। यदि आप इस क्विज़ का अभ्यास करना चाहते हैं, तो अब नीचे दिए गए विवरण में लिंक पर Wonderslate एप्लिकेशन डाउनलोड करें। यदि आपके पास हमारे लिए कोई सुझाव है तो comment करें। \ n" +
                                    "अगर आपको वीडियो पसंद आया है और इसे अधिक देखना चाहते हैं, तो अंगूठे को हिट करें और भविष्य के वीडियो के अधिक अपडेट प्राप्त करने के लिए subscribe बटन पर क्लिक करें";
                            }
                            document.getElementById("placeHolder").innerHTML=placeHolderText;
                            var thankyou = "<break time=\"250ms\"/>"+document.getElementById("placeHolder").innerText;

                            getAudioAndStore(thankyou,"thankyou");
                        }
                        currentIndex +=1;
                        resetDisplay();
                        showQuiz(currentIndex);
                        currentState="quiz";
                        quizCurrentState="question";
                        //prepare thank you also

                    }
                    else{
                        currentState="quiz";
                        quizCurrentState="thankyou";
                        finishAndShowThankYou();
                    }

                }
                else if("thankyou"==quizCurrentState){
                    removeContentFromLocalStorageAll();
                }

            }

        });


        function removeContentFromLocalStorageAll(){
            for(var index=0;index<quizData.results.length;index++){
                localStorage.removeItem("question_"+index);
                localStorage.removeItem("option1_"+index);
                localStorage.removeItem("option2_"+index);
                localStorage.removeItem("option3_"+index);
                localStorage.removeItem("option4_"+index);
                localStorage.removeItem("option5_"+index);
                localStorage.removeItem("correctAnswer_"+index);
                localStorage.removeItem("answerExplanation_"+index);

            }
            localStorage.removeItem("thankyou");
        }

        function removeContentFromLocalStorage(){
            var index=0;
            if(currentIndex>1) {
                index=currentIndex-2;
                localStorage.removeItem("question_" + index);
                localStorage.removeItem("option1_" + index);
                localStorage.removeItem("option2_" + index);
                localStorage.removeItem("option3_" + index);
                localStorage.removeItem("option4_" + index);
                localStorage.removeItem("option5_" + index);
                localStorage.removeItem("correctAnswer_" + index);
                localStorage.removeItem("answerExplanation_" + index);
            }

        }
        function prepareQuestions(index){

            removeContentFromLocalStorage();
            currentNumberOfOptions=4;
            var options = ['A','B','C','D']
            if(objectiveTypes[index].questionDescription1!=null && objectiveTypes[index].questionDescription1!="") {
                document.getElementById("placeHolder").innerHTML = objectiveTypes[index].questionDescription1;
                var question = "Question number " + (index + 1) + "<break time=\"500ms\"/>" + document.getElementById("placeHolder").innerText;
                getAudioAndStore(question, "question_" + index);
            }else{
                document.getElementById("placeHolder").innerHTML = objectiveTypes[index].ps;
                var question = "Question number " + (index + 1) + "<break time=\"500ms\"/>" + document.getElementById("placeHolder").innerText;
                getAudioAndStore(question, "question_" + index);

            }
            if(objectiveTypes[index].description1!=null && objectiveTypes[index].description1!="") {
                document.getElementById("placeHolder").innerHTML = objectiveTypes[index].description1;
                var option1 = "Option A" + "<break time=\"250ms\"/>" + document.getElementById("placeHolder").innerText;
                getAudioAndStore(option1,"option1_"+index);
            }else{
                document.getElementById("placeHolder").innerHTML = objectiveTypes[index].op1;
                var option1 = "Option A" + "<break time=\"250ms\"/>" + document.getElementById("placeHolder").innerText;
                getAudioAndStore(option1,"option1_"+index);
            }
            if(objectiveTypes[index].description2!=null && objectiveTypes[index].description2!="") {
                document.getElementById("placeHolder").innerHTML=objectiveTypes[index].description2;
                var option2 ="Option B"+"<break time=\"250ms\"/>"+document.getElementById("placeHolder").innerText;
                getAudioAndStore(option2,"option2_"+index);
            }else{
                document.getElementById("placeHolder").innerHTML=objectiveTypes[index].op2;
                var option2 ="Option B"+"<break time=\"250ms\"/>"+document.getElementById("placeHolder").innerText;
                getAudioAndStore(option2,"option2_"+index);
            }
            if(objectiveTypes[index].description3!=null && objectiveTypes[index].description3!="") {
                document.getElementById("placeHolder").innerHTML=objectiveTypes[index].description3;
                var option3 ="Option C"+"<break time=\"250ms\"/>"+document.getElementById("placeHolder").innerText;
                getAudioAndStore(option3,"option3_"+index);
            }else{
                document.getElementById("placeHolder").innerHTML=objectiveTypes[index].op3;
                var option3 ="Option C"+"<break time=\"250ms\"/>"+document.getElementById("placeHolder").innerText;
                getAudioAndStore(option3,"option3_"+index);
            }
            if(objectiveTypes[index].description4!=null && objectiveTypes[index].description4!="") {
                document.getElementById("placeHolder").innerHTML=objectiveTypes[index].description4;
                var option4 ="Option D"+"<break time=\"250ms\"/>"+document.getElementById("placeHolder").innerText;
                getAudioAndStore(option4,"option4_"+index);
            }else{
                document.getElementById("placeHolder").innerHTML=objectiveTypes[index].op4;
                var option4 ="Option D"+"<break time=\"250ms\"/>"+document.getElementById("placeHolder").innerText;
                getAudioAndStore(option4,"option4_"+index);
            }
            if(objectiveTypes[index].description5!=null && objectiveTypes[index].description5!="") {
                document.getElementById("placeHolder").innerHTML=objectiveTypes[index].description5;
                var option5 ="Option E"+"<break time=\"250ms\"/>"+document.getElementById("placeHolder").innerText;
                getAudioAndStore(option5,"option5_"+index);
                currentNumberOfOptions = 5;
            }else{
                document.getElementById("placeHolder").innerHTML=objectiveTypes[index].op5;
                var option5 ="Option E"+"<break time=\"250ms\"/>"+document.getElementById("placeHolder").innerText;
                getAudioAndStore(option5,"option5_"+index);
                currentNumberOfOptions = 5;
            }

            // if(objectiveTypes[index].op5!=null&&objectiveTypes[index].op5!=""){
            //
            // }
            var correctAnswerString="";
            var correctAnswer="The correct answer is<break time=\"250ms\"/>";
            if("Hindi"==document.getElementById("language").value) correctAnswer= "सही जवाब है<break time=\"250ms\"/>";
            if("Yes"==objectiveTypes[index].ans1){
                correctOption=1;
                correctAnswerString="<div class=\"d-flex  option-wrapper checked\" >\n" +
                    "                            <p>a.</p>\n" +
                    "                            <p>"+objectiveTypes[index].op1+"</p>\n" +
                    "                            <img src=\"/assets/autovideo/done.svg\" class=\"done\">\n" +
                    "                        </div>";
            } else  if("Yes"==objectiveTypes[index].ans2){
                correctAnswerString="<div class=\"d-flex  option-wrapper checked\">\n" +
                    "                            <p>b.</p>\n" +
                    "                            <p>"+objectiveTypes[index].op2+"</p>\n" +
                    "                            <img src=\"/assets/autovideo/done.svg\" class=\"done\">\n" +
                    "                        </div>";
                correctOption=2;
            } else  if("Yes"==objectiveTypes[index].ans3){
                correctAnswerString=" <div class=\"d-flex  option-wrapper checked\">\n" +
                    "                            <p>c.</p>\n" +
                    "                            <p>"+objectiveTypes[index].op3+"</p>\n" +
                    "                            <img src=\"/assets/autovideo/done.svg\" class=\"done\">\n" +
                    "                        </div>";
                correctOption=3;
            } else  if("Yes"==objectiveTypes[index].ans4){
                correctAnswerString=" <div class=\"d-flex option-wrapper checked\">\n" +
                    "                            <p>d.</p>\n" +
                    "                            <p>"+objectiveTypes[index].op4+"</p>\n" +
                    "                            <img src=\"/assets/autovideo/done.svg\" class=\"done\">\n" +
                    "                        </div>";
                correctOption=4;
            }
            else  if("Yes"==objectiveTypes[index].ans5){
                correctAnswerString=" <div class=\"d-flex option-wrapper checked\">\n" +
                    "                            <p>d.</p>\n" +
                    "                            <p>"+objectiveTypes[index].op5+"</p>\n" +
                    "                            <img src=\"/assets/autovideo/done.svg\" class=\"done\">\n" +
                    "                        </div>";
                correctOption=5;
            }
            correctOptionArray.push(correctOption);
            correctAnswerStringArray.push(correctAnswerString);
            getAudioAndStore(correctAnswer,"correctAnswer_"+index);



            if(objectiveTypes[index].answerDescription1==n){
                document.getElementById("placeHolder").innerHTML=objectiveTypes[index].answerDescription;

                getAudioAndStore(document.getElementById("placeHolder").innerText,"answerExplanation_"+index);
            }


            if(objectiveTypes[index].answerDescription1!=null && objectiveTypes[index].answerDescription1!="") {
                document.getElementById("placeHolder").innerHTML=objectiveTypes[index].answerDescription1;
                getAudioAndStore(document.getElementById("placeHolder").innerText,"answerExplanation_"+index);
            }else if(objectiveTypes[index].answerDescription){
                document.getElementById("placeHolder").innerHTML=objectiveTypes[index].answerDescription;
                getAudioAndStore(document.getElementById("placeHolder").innerText,"answerExplanation_"+index);

            }

        }

        function finishAndShowThankYou(){
            $("#4").hide();
            $("#5").show();
            playNext("thankyou");
        }

        // function showCorrectAnswer(index){
        //     document.getElementById("correctAnswerMoved").innerHTML = correctAnswerStringArray[index];
        //     MathJax.Hub.Queue(["Typeset", MathJax.Hub, "correctAnswerMoved"]);
        //     $("#correctAnswerMoved").slideDown(1500);
        //     document.getElementById("answerExplanation").innerHTML=objectiveTypes[index].answerDescription;
        //     MathJax.Hub.Queue(["Typeset", MathJax.Hub, "correctAnswerExplanation"]);
        //     $("#correctAnswerExplanation").slideDown(1500);
        //     MathJax.Hub.Queue(["Typeset", MathJax.Hub, "answerExplanation"]);
        //     $("#answerExplanation").slideDown(1500);
        // }


        function showCorrectAnswer(index){
            document.getElementById("correctAnswerMoved").innerHTML = correctAnswerStringArray[index];
            MathJax.Hub.Queue(["Typeset", MathJax.Hub, "correctAnswerMoved"]);
            document.getElementById("answerExplanation").innerHTML=objectiveTypes[index].answerDescription;
            MathJax.Hub.Queue(["Typeset", MathJax.Hub, "correctAnswerExplanation"]);
            MathJax.Hub.Queue(["Typeset", MathJax.Hub, "answerExplanation"]);
            $("#correctAnswerMoved").show("slow");
            $("#correctAnswerExplanation").show("slow");
            $("#answerExplanation").show();
        }




        function showQuiz(index){
            currentIndex=index;
            $("#3").hide();
            document.getElementById("questionTracker").innerHTML="Question "+(index+1)+"/"+quizData.results.length;
            var objectiveTypes = quizData.results;
            document.getElementById("question").innerHTML=objectiveTypes[index].ps;
            MathJax.Hub.Queue(["Typeset", MathJax.Hub, "4"]);

            document.getElementById("questionIndex").innerHTML="Q"+(index+1);
            $("#4").slideDown(1500);
            // $("#4").show();
            playNext("question_"+currentIndex);
        }

        function resetDisplay(){
            document.getElementById("questionTracker").innerHTML="";
            document.getElementById("question").innerHTML="";
            $("#correctAnswerMoved").hide();
            $("#answerExplanation").hide();
            $("#correctAnswerExplanation").hide();
            $("#timeup").hide();
            $("#opt1class").removeClass('checked');
            $("#opt2class").removeClass('checked');
            $("#opt3class").removeClass('checked');
            $("#opt4class").removeClass('checked');
            $("#opt5class").removeClass('checked');
            $("#divop1").hide();
            $("#divop2").hide();
            $("#divop3").hide();
            $("#divop4").hide();
            $("#divop5").hide();
        }


    </script>

    <script>
        //circle start
        var progressBar = document.querySelector('.e-c-progress');
        var indicator = document.getElementById('e-indicator');
        var pointer = document.getElementById('e-pointer');
        var length = Math.PI * 2 * 100;
        var audioPaused=false;

        progressBar.style.strokeDasharray = length;

        function update(value, timePercent) {
            var offset = - length - length * value / (timePercent);
            progressBar.style.strokeDashoffset = offset;

        };

        //circle ends
        const displayOutput = document.querySelector('.display-remain-time')
        const pauseBtn = document.getElementById('pause');
        const setterBtns = document.querySelectorAll('button[data-setter]');

        var intervalTimer;
        var timeLeft;
        var wholeTime = 1 * 60; // manage this to set the whole time
        var isPaused = false;
        var isStarted = false;


        update(wholeTime,wholeTime); //refreshes progress bar
        displayTimeLeft(wholeTime);

        function changeWholeTime(seconds){
            if ((wholeTime + seconds) > 0){
                wholeTime += seconds;
                update(wholeTime,wholeTime);
            }
        }

        for (var i = 0; i < setterBtns.length; i++) {
            setterBtns[i].addEventListener("click", function(event) {
                var param = this.dataset.setter;
                switch (param) {
                    case 'minutes-plus':
                        changeWholeTime(1 * 60);
                        break;
                    case 'minutes-minus':
                        changeWholeTime(-1 * 60);
                        break;
                    case 'seconds-plus':
                        changeWholeTime(1);
                        break;
                    case 'seconds-minus':
                        changeWholeTime(-1);
                        break;
                }
                displayTimeLeft(wholeTime);
            });
        }

        function timer (seconds){ //counts time, takes seconds

            var remainTime = Date.now() + (seconds * 1000);
            displayTimeLeft(seconds);

            intervalTimer = setInterval(function(){
                timeLeft = Math.round((remainTime - Date.now()) / 1000);
                if(timeLeft < 0){
                    clearInterval(intervalTimer);
                    displayTimeLeft(wholeTime);
                    clickAudioElement.pause();
                    $("#timer-container").hide();
                    $("#timeup").show();

                    playNext("correctAnswer_"+currentIndex);
                    quizCurrentState="correctAnswer";
                    return ;
                }
                displayTimeLeft(timeLeft);
            }, 1000);
        }
        function pauseTimer(event){
            if(isStarted === false){
                timer(wholeTime);
                isStarted = true;
                this.classList.remove('play');
                this.classList.add('pause');

                setterBtns.forEach(function(btn){
                    btn.disabled = true;
                    btn.style.opacity = 0.5;
                });

            }else if(isPaused){
                this.classList.remove('play');
                this.classList.add('pause');
                timer(timeLeft);
                isPaused = isPaused ? false : true
            }else{
                this.classList.remove('pause');
                this.classList.add('play');
                clearInterval(intervalTimer);
                isPaused = isPaused ? false : true ;
            }
        }

        function displayTimeLeft (timeLeft){ //displays time on the input
            var minutes = Math.floor(timeLeft / 60);
            var seconds = timeLeft % 60;
            var displayString = seconds;
            displayOutput.textContent = displayString;
            update(timeLeft, wholeTime);
        }



        function startTimer(){
            $("#timer-container").show();
            clickAudioElement.play();
            clickAudioElement.volume="1";
            wholeTime =5;
            timer(wholeTime);
        }

        function getQuestionAnswers(resId){
            <g:remoteFunction controller="funlearn" action="quizQuestionAnswers" params="'resId='+resId"
                onSuccess = "setQuiz(data);"/>
        }

        function getDailyTestsQuestionAnswers(){
            var dailytestID = '${params.dailyTestId}';
            var dateInput = '${params.dateInput}';
            <g:remoteFunction controller="prepjoy" action="getDailyTests" params="'dailyTestId='+dailytestID+'&dateInput='+dateInput+'&siteId=${session["siteId"]}'" onSuccess="setQuiz(data);" />
        }

        function setQuiz(data){
            quizData = data;

            document.getElementById("quote").innerHTML = quizData.chapterName;
        }

        <%if(params.resId!=null){%>
        getQuestionAnswers(${params.resId});
        <%}else if (params.dailyTestId!=null){%>
        getDailyTestsQuestionAnswers();
        <%}%>

        document.body.onkeyup = function(e){
            if(e.keyCode == 32){
                //your code
                if(audioPaused){
                    audioPaused=false;
                    audioElement.play();
                }else{
                    audioPaused=true;
                    audioElement.pause();
                }
            }
        }

    </script>
</div>
</body>
</html>
