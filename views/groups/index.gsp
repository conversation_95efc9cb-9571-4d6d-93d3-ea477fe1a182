<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:stylesheet href="groups/groupsStyle.css" async="true"/>
<asset:javascript src="sharer.min.js"/>

<style>
.notification{
    position: relative;
}
.notificationCount{
    position: absolute;
    left: 70%;
    top: 10%;
    background: red;
    width: 15px;
    height: 15px;
    text-align: center;
    border-radius: 50%;
    color: #fff;
    font-size: 10px;
}
.post-card{
    transition: all .2s ease;
}

.post-card:hover{
    transform: scale(1.05);

}
.backgrouncard {
    height: 71px;
    width: 90%;
    background-size: cover;
    background-repeat: no-repeat;
    margin:10px;
    border-radius:5px;
}
</style>

<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>

<section class="admin-panel-header mt-4" id="admin-header">
    <div class="container width-size mt-4">
        <div class="header-wrapper d-flex align-items-center">
            <a href="javascript:window.history.back();" class="back-btn d-flex mr-2">
                <img src="${assetPath(src: 'groups/pink-back.svg')}" class="mr-2"> Back
            </a>
%{--            <a href="notifications" class="notification d-flex">--}%
%{--                <span id="notificationCount" class="notificationCount">0</span>--}%
%{--                <img src="${assetPath(src: 'groups/Notifications.svg')}">--}%
%{--            </a>--}%
        </div>
        <div class="header-contents mt-4 d-flex align-items-center">
            <h2>Study Groups</h2>
            <div class="createGroup-btn text-center ml-3 ml-md-4" id="topcreateBtn">
                <a  href="/groups/groupCreate" class="text-center">
                    <img src="${assetPath(src: 'groups/addIcon.svg')}">
                    Create Group
                </a>
            </div>
        </div>
    </div>
</section>
<div class="container width-size mt-4">
    <div class="search">
        <input type="text" placeholder="Search" id="search"/>
    </div>
</div>

<section class="reportsection mt-4" id="memsssection">
    <div class="container width-size">
        <div class="memsec-header d-flex align-items-center">
            <a href="javascript:myGroups();" class="mr-2 active p-1" id="mygroups">My Groups</a>
            <div class="div mr-2"></div>
            <a href="javascript:discoverGroups();" class="" id="discover-group">Discover Groups</a>
        </div>
    </div>
</section>

<section class="groups-listing">
    <div class="container width-size">
        <div id="groupsList">

        </div>
        <div id="showMoreBtn" class="text-center mt-4">
            <a id="showMoreMyGroups" href="javascript:showMoreMyGroups();" class="btn btn-success" style="display: none;">Show More</a>
            <a id="showMoreDiscoverGroups" href="javascript:showMoreDiscoverGroups();" class="btn btn-success" style="display: none;">Show More</a>
        </div>
    </div>
</section>

<g:render template="/${session['entryController']}/footer_new"></g:render>
<g:render template="/resources/shareContent"></g:render>
<script>
    <%if(session["userdetails"]==null){%>
    $(".loading-icon").addClass("hidden")
    checkLoginAndProceed();
    <%} else { %>

    var myLength = 12;
    var discoverLength = 12;
    var searchTerm;
    var userExist;

    // Tab active classes script
    $("#discover-group").click(function (e) {
        $("#discover-group").addClass("active");
        $("#mygroups").removeClass("active");
        $("#search").val('');
    });
    $("#mygroups").click(function (e) {
        $("#discover-group").removeClass("active");
        $("#mygroups").addClass("active");
        $("#search").val('');
    });

    // On page and onclick my groups
    function myGroups(){
        $(".loading-icon").removeClass("hidden");
        <g:remoteFunction controller="groups" action="getMyGroupsList" params="'start=0&length='+myLength" onSuccess='showGroupsList(data);'/>
    }

    // Onclick discover groups
    function discoverGroups(){
        var siteId="${session.siteId}";
        $(".loading-icon").removeClass("hidden");
        <g:remoteFunction controller="groups" action="getGroupsList" params="'start=0&length='+discoverLength+'&siteId='+siteId" onSuccess='showGroupsList(data);'/>    }

    // Displaying group cards
    function showGroupsList(data) {
        $(".loading-icon").addClass("hidden");
        var list = data.groupsList;



        var htmlStr = "";
        var onlyChannel= "true";
        if (data.status == "success") {
            htmlStr += "<div class='posts-cards mt-4'>";
            for(var i=0;i<list.length;i++) {
                if (list[i].groupType != "channel") {
                    htmlStr += "<div class='post-card' >";
                    onlyChannel="false";
                    if (list[i].postCount > 0) {
                        htmlStr += "<span class=\"post-pending\">" + list[i].postCount + "</span>";
                    }

                    if (list[i].userType == "admin") {
                        htmlStr += "<div class=\"post-card-header\" onclick=\"javascript:goInsideGroup('/groups/groupDtl','" + list[i].id + "');\" class=\"mr-2 group-title\" style='cursor: pointer;'><p>You're admin</p></div>";
                    }
                    if (list[i].colorCode != null) {
                        var imageSrc = "/assets/groups/" + list[i].colorCode;
                        htmlStr += "<div class=\"backgrouncard\" onclick=\"javascript:goInsideGroup('/groups/groupDtl','" + list[i].id + "');\" class=\"mr-2 group-title\" style='background-image:url(" + imageSrc + ");cursor: pointer;'></div>";
                    } else if (list[i].image != null) {
                        var imageSrc = "/groups/showGroupImage?id=" + list[i].id + "&fileName=" + list[i].image;
                        htmlStr += "<div class=\"backgrouncard\" onclick=\"javascript:goInsideGroup('/groups/groupDtl','" + list[i].id + "');\" class=\"mr-2 group-title\" style='background-image:url(" + imageSrc + ");cursor: pointer;''></div>";
                    } else {
                        var imageSrc = "/assets/groups/coverone.png";
                        htmlStr += "<div class=\"backgrouncard\" onclick=\"javascript:goInsideGroup('/groups/groupDtl','" + list[i].id + "');\" class=\"mr-2 group-title\" style='background-image:url(" + imageSrc + ");cursor: pointer;''></div>";
                    }
                    htmlStr += "<div class=\"post-card-body d-flex justify-content-between\">\n" +
                        "<p onclick=\"javascript:goInsideGroup('/groups/groupDtl','" + list[i].id + "');\" class=\"mr-2 group-title\" style='cursor: pointer;'>" + list[i].groupName + "</p>\n";

                    htmlStr += "<a href=\"javascript:showShareModal(" + list[i].id + ")\"><img src=\"${assetPath(src: 'groups/share.svg')}\"></a>\n";


                    htmlStr += "</div>" +
                        "<div class=\"divider\"onclick=\"javascript:goInsideGroup('/groups/groupDtl','" + list[i].id + "');\" class=\"mr-2 group-title\" style='cursor: pointer;'></div>";

                    htmlStr += "<div class=\"post-card-footer d-flex align-items-center justify-content-between\" onclick=\"javascript:goInsideGroup('/groups/groupDtl','" + list[i].id + "');\" class=\"mr-2 group-title\" style='cursor: pointer;'>\n" +
                        "<div class=\"members d-flex align-items-center\">" +
                        "<img src=\"${assetPath(src: 'groups/people_black_24dp.svg')}\" class=\"mr-2\">\n" +
                        "<p>" + list[i].membersCount + "</p>" +
                        "</div>" +
                        "<div class=\"pb-pr d-flex align-items-center\">\n";
                    if (list[i].privacyType == "private") {
                        htmlStr += "<img src=\"${assetPath(src: 'groups/private.svg')}\" class=\"mr-2\">";
                    } else if (list[i].privacyType == "public") {
                        htmlStr += "<img src=\"${assetPath(src: 'groups/public_black_24dp.svg')}\" class=\"mr-2\">";
                    }


                    htmlStr += "<p class='text-capitalize'>" + list[i].privacyType + "</p>" +
                        "</div>" +
                        "</div>";

                    htmlStr += "</div>";

                }
            }
            htmlStr += "</div>";
            document.getElementById("groupsList").innerHTML = htmlStr;
            $("#topcreateBtn").show();
            if(onlyChannel=="true" && list.length>0){
                document.getElementById("groupsList").innerHTML = noGroupsUILayer();
                $("#topcreateBtn").hide();
            }
        } else {
            document.getElementById("groupsList").innerHTML = noGroupsUILayer();
            $("#topcreateBtn").hide();
        }

        if(list.length<myLength) {
            $("#showMoreMyGroups").hide();
        } else {
            if ($("#mygroups").hasClass("active")) {
                $("#showMoreMyGroups").show();
                $("#showMoreDiscoverGroups").hide();
            }
        }
        if(list.length<discoverLength) {
            $("#showMoreDiscoverGroups").hide();
        } else {
            if($("#discover-group").hasClass("active")){
                $("#showMoreDiscoverGroups").show();
                $("#showMoreMyGroups").hide();
            }
        }

    }

    // If no groups displaying this
    function noGroupsUILayer() {
        var htmlStr = "<div class='create-group mt-5 pb-5'><p>There's always a new beginning. <br>Start by creating a new group.</p>\n"+
            "<div class=\"create-group-button text-center mt-4\">\n" +
            "<a href=\"/groups/groupCreate\" class=\"text-center d-flex justify-content-center\">+ Create New Group</a>\n"+
            "</div></div>";
        return htmlStr;
    }

    // On page load call this function
    myGroups();

    function goInsideGroup(url,id) {
        window.location.href = url+'?groupId='+id;
    }

    function showMoreMyGroups(){
        myLength = myLength+5;
        myGroups();
    }
    function showMoreDiscoverGroups() {
        discoverLength = discoverLength+5;
        discoverGroups();
    }

    //Search group
    $("#search").keyup(function (){
        var siteId="${session.siteId}";
        searchTerm = $("#search").val();
        if (searchTerm.length >= 3 || searchTerm.length == 0){
            if ($("#mygroups").hasClass("active")) {
                <g:remoteFunction controller="groups" action="getMyGroupsList" params="'start=0&length='+myLength+'&search='+searchTerm" onSuccess='showSearchList(data);'/>
            } else if($("#discover-group").hasClass("active")) {
                <g:remoteFunction controller="groups" action="getGroupsList" params="'start=0&length='+discoverLength+'&siteId='+siteId+'&search='+searchTerm" onSuccess='showGroupsList(data);'/>            }
        }
    });

    function showSearchList(data){
        if (data.status == "No records"){
            var htmlStr = "";
            htmlStr += "<div class='container text-center mt-5'>";
            htmlStr += "<img src=\"${assetPath(src: 'groups/notfound.svg')}\">\n"+
                "<p class=\"font-weight-bold\" style=\"color:rgba(68, 68, 68, 0.48);\">No groups found</p>" +
                "</div>";
            document.getElementById("groupsList").innerHTML = htmlStr;
            $("#showMoreMyGroups").hide();
            $("#showMoreDiscoverGroups").hide();
        }else {
            showGroupsList(data);
        }
    }

    function showShareModal(id){
        var link = window.location.host+'/groups/groupDtl?groupId='+id
        openShareContentModalGeneric("Interesting groups found. Please click on the link to see the Groups!",link)
    }
    <%}%>
</script>