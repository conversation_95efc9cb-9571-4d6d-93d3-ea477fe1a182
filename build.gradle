buildscript {
    repositories {
        mavenLocal()
        maven { url "https://repo.grails.org/grails/core" }
        maven { url "https://repo.grails.org/grails/plugins/" }
    }
    dependencies {
        classpath "org.grails:grails-gradle-plugin:$grailsVersion"
        classpath "com.bertramlabs.plugins:asset-pipeline-gradle:2.14.1"
        classpath "org.grails.plugins:hibernate5:${gormVersion-".RELEASE"}"
    }
}

version "0.1"
group "wonderslate329"

apply plugin:"eclipse"
apply plugin:"idea"
apply plugin:"war"
apply plugin:"org.grails.grails-web"
apply plugin:"org.grails.grails-gsp"
apply plugin:"asset-pipeline"

repositories {
    mavenLocal()
    maven { url "https://repo.grails.org/grails/core" }
    maven { url "https://repo.grails.org/grails/plugins/" }
    maven { url "https://jcenter.bintray.com" }
}

dependencies {
    compile "org.springframework.boot:spring-boot-starter-logging"
    compile "org.springframework.boot:spring-boot-autoconfigure"
    compile "org.grails:grails-core"
    compile "org.springframework.boot:spring-boot-starter-actuator"
    compile "org.springframework.boot:spring-boot-starter-tomcat"
    compile "org.grails:grails-dependencies"
    compile "org.grails:grails-web-boot"
    compile "org.grails.plugins:cache"
    compile "org.grails.plugins:scaffolding"
    compile "org.grails.plugins:hibernate5"
    compile "org.hibernate:hibernate-core:5.1.3.Final"
    compile "org.hibernate:hibernate-ehcache:5.1.3.Final"
    console "org.grails:grails-console"
    profile "org.grails.profiles:web"
    runtime "com.bertramlabs.plugins:asset-pipeline-grails:2.14.1"
    runtime "com.h2database:h2"
    compile 'org.grails.plugins:spring-security-core:3.1.2'
    compile 'org.grails.plugins:spring-security-rest:2.0.0.M2'
    compile 'org.grails.plugins:spring-security-rest-gorm:2.0.0.M2'



    compile "com.razorpay:razorpay-java:1.0.0"
    compile 'org.grails.plugins:mail:2.0.0.RC6'
    compile 'org.grails.plugins:rendering:2.0.3'
    compile 'org.grails.plugins:html-validator:0.3'
    compile 'net.sourceforge.jexcelapi:jxl:2.6.12'
    runtime 'mysql:mysql-connector-java:8.0.17'
    compile 'org.grails.plugins:redis:2.0.4'
    compile 'org.imgscalr:imgscalr-lib:4.2'
    compile 'org.grails.plugins:redis:2.0.4'
    compile 'domurtag.plugins:grails-simple-captcha:1.0.0-grails3'
    compile 'com.google.firebase:firebase-admin:6.6.0'

    compile 'org.grails:grails-datastore-rest-client'
    compile 'pl.allegro.finance:tradukisto:1.12.0'
    compile files('libs/ccavutil.jar')
    runtime files('libs/ccavutil.jar')
    compile 'com.google.api-client:google-api-client:1.25.0'
    compile 'com.google.apis:google-api-services-youtube:v3-rev212-1.25.0'
    compile "org.grails.plugins:wordpress:0.2.5"
    compile "org.grails.plugins:php:0.1.5"
//    compile group: 'com.gmongo', name: 'gmongo', version: '0.9.3'
//    compile 'org.grails.plugins:mongodb:7.0.0'
    compile 'com.onelogin:java-saml:2.6.0'

    compile files('libs/selling-partner-api-1.0.0.jar')
    runtime files('libs/selling-partner-api-1.0.0.jar')
    compile files('libs/sellingpartner-api-documents-helper-java-1.0.0.jar')
    runtime files('libs/sellingpartner-api-documents-helper-java-1.0.0.jar')
    compile files('libs/sellingpartnerapi-aa-java-1.0.jar')
    runtime files('libs/sellingpartnerapi-aa-java-1.0.jar')
    compile 'com.amazonaws:aws-java-sdk-signer:1.11.610'
    compile 'com.amazonaws:aws-java-sdk-sts:1.11.236'
    compile 'io.gsonfire:gson-fire:1.8.5'
    compile 'com.squareup.okhttp:okhttp:2.7.5'
    compile 'org.sejda.imageio:webp-imageio:0.1.6'
    compile files('libs/paapi5-java-sdk-1.1.0.jar')
    runtime files('libs/paapi5-java-sdk-1.1.0.jar')
    compile files('libs/logging-interceptor-2.7.5.jar')
    runtime files('libs/logging-interceptor-2.7.5.jar')
    compile files('libs/okio-1.6.0.jar')
    runtime files('libs/okio-1.6.0.jar')
    compile "org.jsoup:jsoup:1.15.2"
    compile 'org.apache.pdfbox:pdfbox:3.0.1'
    compile 'com.maxmind.geoip2:geoip2:2.16.0'
    compile 'com.maxmind.db:maxmind-db:2.0.0'
    compile "org.apache.tika:tika-core:2.9.0"
    compile "org.apache.tika:tika-parsers:2.9.0"
    compile 'org.apache.poi:poi-ooxml:5.2.4'
    compile "org.apache.poi:poi:5.2.4"
    compile 'commons-io:commons-io:2.15.1'
    compile 'org.apache.logging.log4j:log4j-api:2.23.0'
    compile 'org.apache.logging.log4j:log4j-core:2.23.0'
    compile 'org.grails.plugins:excel-export:2.1'
    compile 'com.itextpdf:itext7-core:7.1.17'
    compile 'software.amazon.awssdk:cloudfront:2.30.30'
    compile 'org.springframework:spring-websocket:4.3.9.RELEASE'
    compile 'org.springframework:spring-messaging:4.3.9.RELEASE'
}

bootRun {
    jvmArgs('-Dspring.output.ansi.enabled=always')
    addResources = true
    main = 'wonderslate329.Application'
}


assets {
    minifyJs = true
    minifyCss = true
}

grails {
    pathingJar = true
}
