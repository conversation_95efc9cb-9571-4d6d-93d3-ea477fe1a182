<svg width="94" height="70" viewBox="0 0 94 70" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d)">
<ellipse cx="47" cy="33.5" rx="37" ry="22.5" fill="white"/>
</g>
<g opacity="0.65" clip-path="url(#clip0)">
<path d="M60.2402 22.6763C58.5226 22.6763 57.0714 23.8346 56.6238 25.4107H52.9941V21.4079H56.7001C60.7253 21.4079 64 18.1332 64 14.108C64 10.8807 61.8595 8.04559 58.8208 7.12219C57.9023 3.44418 54.5822 0.806261 50.7371 0.806261C49.2465 0.806261 47.8186 1.19065 46.5584 1.92503C45.2239 0.704542 43.4739 0.018898 41.6353 0.018898C37.6914 0.018898 34.468 3.16281 34.3393 7.07584C31.218 7.94831 29 10.8062 29 14.108C29 18.1332 32.2747 21.4079 36.2999 21.4079H40.0059V25.4107H36.3762C35.9287 23.8346 34.4774 22.6763 32.7598 22.6763C30.6866 22.6763 29 24.363 29 26.4361C29 28.5092 30.6866 30.1959 32.7598 30.1959C34.4774 30.1959 35.9286 29.0377 36.3762 27.4615H41.0312C41.5975 27.4615 42.0566 27.0024 42.0566 26.4361V21.4079H45.4746V27.6048C43.8984 28.0523 42.7402 29.5036 42.7402 31.2213C42.7402 33.2944 44.4269 34.981 46.5 34.981C48.5731 34.981 50.2598 33.2944 50.2598 31.2213C50.2598 29.5036 49.1016 28.0524 47.5254 27.6048V21.4079H50.9434V26.4361C50.9434 27.0024 51.4025 27.4615 51.9688 27.4615H56.6238C57.0713 29.0377 58.5226 30.1959 60.2402 30.1959C62.3134 30.1959 64 28.5092 64 26.4361C64 24.363 62.3134 22.6763 60.2402 22.6763ZM32.7598 28.1451C31.8174 28.1451 31.0508 27.3784 31.0508 26.4361C31.0508 25.4938 31.8174 24.7271 32.7598 24.7271C33.7021 24.7271 34.4688 25.4938 34.4688 26.4361C34.4688 27.3784 33.7021 28.1451 32.7598 28.1451ZM48.209 31.2213C48.209 32.1636 47.4423 32.9302 46.5 32.9302C45.5577 32.9302 44.791 32.1636 44.791 31.2213C44.791 30.2789 45.5577 29.5123 46.5 29.5123C47.4423 29.5123 48.209 30.2789 48.209 31.2213ZM36.2999 19.3571C33.4056 19.3571 31.0508 17.0024 31.0508 14.108C31.0508 11.5211 32.979 9.28829 35.5358 8.91437C36.0754 8.83548 36.4588 8.34808 36.4084 7.80517C36.3936 7.645 36.3861 7.48135 36.3861 7.31872C36.3861 4.42445 38.7409 2.06975 41.6352 2.06975C43.1794 2.06975 44.6357 2.74104 45.6305 3.91149C45.9797 4.32239 46.5875 4.39253 47.0212 4.07206C48.0967 3.27718 49.3816 2.85704 50.737 2.85704C53.8561 2.85704 56.45 5.14565 56.9349 8.12276C57.0034 8.54372 57.3255 8.87827 57.7435 8.9629C60.1804 9.45625 61.9491 11.62 61.9491 14.108C61.9491 17.0024 59.5944 19.3571 56.7 19.3571H36.2999ZM60.2402 28.1451C59.2979 28.1451 58.5312 27.3784 58.5312 26.4361C58.5312 25.4938 59.2979 24.7271 60.2402 24.7271C61.1826 24.7271 61.9492 25.4938 61.9492 26.4361C61.9492 27.3784 61.1826 28.1451 60.2402 28.1451Z" fill="#B629DE"/>
</g>
<defs>
<filter id="filter0_d" x="0" y="5" width="94" height="65" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<clipPath id="clip0">
<rect width="35" height="35" fill="white" transform="translate(29)"/>
</clipPath>
</defs>
</svg>
