<svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0)" filter="url(#filter0_i)">
<path d="M17.6976 8.04678L9.62162 0.687958C9.26714 0.364907 8.7326 0.364943 8.37826 0.687922L0.302213 8.04681C0.0182569 8.30556 -0.0755398 8.70424 0.0631865 9.06244C0.201948 9.42065 0.539799 9.65208 0.92395 9.65208H2.21383V17.0251C2.21383 17.3174 2.45085 17.5544 2.74318 17.5544H7.16983C7.46216 17.5544 7.69918 17.3174 7.69918 17.0251V12.5484H10.3008V17.0251C10.3008 17.3174 10.5378 17.5545 10.8302 17.5545H15.2566C15.5489 17.5545 15.7859 17.3175 15.7859 17.0251V9.65208H17.0761C17.4602 9.65208 17.7981 9.42061 17.9368 9.06244C18.0754 8.7042 17.9816 8.30556 17.6976 8.04678Z" fill="white"/>
<path d="M15.6478 1.5022H12.0928L16.1771 5.21596V2.03151C16.1771 1.73919 15.9402 1.5022 15.6478 1.5022Z" fill="white"/>
</g>
<defs>
<filter id="filter0_i" x="0" y="0" width="18" height="20" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow"/>
</filter>
<clipPath id="clip0">
<rect width="18" height="18" fill="white"/>
</clipPath>
</defs>
</svg>
