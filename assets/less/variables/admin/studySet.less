#studySets {
  .study-set-textarea {
    font-size: 16px;
    width: 100%;
    resize: none;
    //background-color: #f8f8f8;
    border: 0;
    border-bottom: 0px solid #455358;
    outline: none;
    overflow: hidden;
  }
  #revisionTitleInput {
    .study-set-textarea {
      font-size: 16px;
      width: 427px;
      resize: none;
      background-color: #f8f8f8;
      border: none;
      border-bottom: 0px solid #455358;
      outline: none;
      overflow: hidden;
      //margin-left: 20px;
    }
  }
  #study-set-wrapper{
    .study-set-textarea {
      font-size: 16px;
      width: 427px;
      resize: none;
      background-color: #f8f8f8;
      border: none;
      border-bottom: 0px solid #455358;
      outline: none;
      overflow: hidden;
      //margin-left: 20px;
    }
  }
  #keyvalueholder {
    //display: inline-flex;
    //width: 100%;

    .study-set-textarea {
      font-size: 16px;
      width: 427px;
      resize: none;
      background-color: #f8f8f8;
      border: 0;
      //border-bottom: 2px solid #455358;
      outline: none;
      overflow: hidden;
      //margin-left: 20px;
    }
  }
}
  #revisionTitle {
    display: flex;
    align-items: center;
  }

  .test {
    display: flex;
    flex-wrap: wrap;
    margin-right: -15px;
    margin-left: 342px;
    width: 100%;
  }

  .study-set-item {
    padding: 16px 24px 16px 48px;
    margin-right: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
  }



  .study-set-wrapper .study-set-item .form-group {
    background: #f8f8f8;
  }

  .study-set-wrapper .add-study-card-btn {
    background: #FFFFFF;
    border-bottom: 2px solid #2EBAC6;
    //box-sizing: border-box;
    //border-radius: 4px;
    padding: 0.5rem 2rem;
    color: #2EBAC6;
    font-family: 'Rubik', sans-serif;
    font-weight: 500;
    text-transform: uppercase;
    font-size: 14px;
    text-decoration: none;
  }

  .study-set-wrapper .add-study-card-btn:hover {
    color: @ws-darkOrange;
    border-bottom: 2px solid @ws-darkOrange;
  }

  //.add-study-card-btn > span {
  //  border-bottom: 2px solid #2EBAC6;
  //  padding-bottom: 8px;
  //  width:100px;
  //}
  .store .tab-content .tab-pane > div > h3 {
    font-size: 18px;
    font-weight: 500;
    color: #444444;
    margin-top: 20px;
    font-family: 'Rubik', sans-serif;
  }

  .store .topSchoolBooks .content-wrapper h3 {
    font-size: 12px;
    font-family: 'Rubik', sans-serif;
    color: #444444;
    font-weight: normal;
    margin-bottom: 0;
    width: 132px;
  }

  .store .topSchoolBooks .content-wrapper h3 {
    height: 63px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
  }

  .store .topSchoolBooks {
    padding: 10px;
    padding-bottom: 10px;
  }
.study-set-wrapper .input-studyset.termDimen{
  width:300px ;
}

/* Revision Mobile UI Reset Styles*/
@media screen and (max-width:767px) {
  .revision_ui {
    .chapternav-wrapper {
      margin-top:0;
      margin-bottom: 1rem;
    }
    .revision_setup {
      ::-webkit-input-placeholder {
        font-size: 16px;
      }
      ::-moz-placeholder {
        font-size: 16px;
      }
      :-ms-input-placeholder {
        font-size: 16px;
      }
      :-moz-placeholder {
        font-size: 16px;
      }
      .study-set-item {
        padding: 0;
        margin-right: 0;
        display: contents;
      }
      .input-studyset {
        width: 300px;
        textarea {
          padding: 5px;
          width: 100% !important;
          padding-left: 0;
        }
        label > span {
          left: 0;
        }
      }
      #study-set-form {
        .study-set-main {
          .term-counter:before {
            width: 40px;
            background-color: #e5e5e5;
          }
          .bg-revCard {
            padding-left: 50px;
            padding-right: 10px;
            margin: 0 auto;
          }
          .input-studyset {
            width: 100%;
          }
          .study-set-textarea {
            margin-left: 0;
            padding-left: 0;
          }
        }
      }
    }
    .study-set-main {
      .user-analytic-data-colored {
        display: block !important;
        padding-bottom: 0;
        div {
          padding: 0 0 10px;
        }
      }
      .term-counter:before {
        width: 40px;
        background-color: #e5e5e5;
      }
      .bg-revCard {
        padding-left: 50px;
        padding-right: 10px;
        margin: 0 auto;
      }
      .study-set-textarea {
        overflow: scroll !important;
      }
    }
  }
}
@media screen and (max-width: 575px) {
  .revision_ui {
    .revision_setup {
      .input-studyset {
        width: 90%;
      }
      #study-set-form .study-set-main .input-studyset {
        width: 90%;
      }
    }
  }
}