@import "../theme/colors.less";
@import "../theme/fonts.less";
@import "../theme/responsive.less";

// Nextexam Page Styles
.nextexam_section {
  .banner-ws {

  }
  .store1_topbanner {
    .publisher_logo {
      border-radius: 10px;
      background-size: cover;
      background-repeat: no-repeat;
      background-position: center bottom;
      height: 250px;
      @media @extraSmallDevices {
        height: 200px;
      }
      div {
        @media @extraSmallDevices {
          width: 100% !important;
        }
      }
      h1, h2 {
        font-family: "Times New Roman" !important;
      }
    }
    .bg-banner {
      border-radius: 10px;
      min-height: 200px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      background: @theme-primary-color;
      position: relative;
      overflow: hidden;
      &:before {
        content: '';
        position: absolute;
        top: -30px;
        right: -30px;
        background-image: url("../../images/ws/library-btn-bg.svg");
        background-repeat: no-repeat;
        width: 150px;
        height: 120px;
        opacity: 0.5;
      }
      &:after {
        content: '';
        position: absolute;
        bottom: -40px;
        left: 0;
        background-image: url("../../images/ws/library-btn-bg.svg");
        background-repeat: no-repeat;
        width: 150px;
        height: 120px;
        opacity: 0.5;
      }
      @media @extraSmallDevices, @smallDevices {
        min-height: 150px;
      }
      h1 {
        color: #E76619;
        font-size: 40px;
        @media @extraSmallDevices, @smallDevices {
          font-size: 30px;
        }
      }
      h2 {
        color: #00FE00;;
        @media @extraSmallDevices, @smallDevices {
          font-size: 24px;
        }
      }
    }
  }
  .store1_index_accordion {
    .card {
      border-radius: 0 0 10px 10px;
      box-shadow: none !important;
      background: none;
    }
    .card-body {
      .name_of_chapter {
        text-align: center;
        a {
          width: 100%;
          height: 100px;
          margin: 0 auto;
          display: flex;
          align-items: center;
          justify-content: center;
          background: @white;
          box-shadow: 0 0 10px @gray-light-shadow;
          border-radius: 4px 15px 15px 15px;
          border: 1px solid;
          padding: 10px;
          font-size: 14px;
          color: @theme-primary-color;
          transition: all 0.2s linear;
          -webkit-transition: all 0.2s linear;
          -moz-transition: all 0.2s linear;
          &:hover {
            box-shadow: 0 5px 10px @gray-light-shadow;
            color: @orange;
            text-decoration: none;
          }
        }
      }
    }
  }
  .monthly-test-btn {
    font-size: 14px;
  }
  h5 {
    color: @black;
  }
  p {
    font-size: 15px;
    margin-bottom: .75rem;
    @media @extraSmallDevices {
      font-size: 14px;
    }
  }
  .show-all, .show-less {
    float: right;
    margin-top: -15px;
    cursor: pointer;
    color: @theme-primary-color;
    &:hover {
      text-decoration: underline;
    }
  }
  #nextExamPrice {
    a {
      &:hover {
        text-decoration: underline;
      }
    }
  }
  .name_of_chapter a {
    cursor: pointer;
  }
  .btn-wrapper{
    display:flex;
    align-items:center;
    justify-content:space-around;
    a {
      cursor:pointer;
      Text-transform: inherit;
      background: @white;
      box-shadow: 0 2px 7px @gray-light-shadow;
      border-radius: 5px;
      color: lighten(@black,35%);
      font-size: 14px;
      line-height: normal;
      padding: 10px 20px;
      border: 1px solid transparent;
      &:hover,&:focus {
        border-color: @theme-primary-color;
      }
    }
  }
}

// New style

//.nextexam_section {
//  -webkit-font-smoothing: antialiased;
//  overflow-x: hidden;
//  &:before {
//    content: '';
//    background-image: url("../../images/wonderslate/map-bg.svg");
//    background-repeat: no-repeat;
//    background-position: center;
//    background-size: cover;
//    width: 100%;
//    height: 100%;
//    position: absolute;
//    opacity: 0.3;
//    top: 0;
//    bottom: 0;
//    left: 0;
//    right: 0;
//    z-index: -1;
//  }
//  .nextexam_content {
//    .nextexam, .prepjoy {
//      padding: 0;
//    }
//    .arrow-animated-image {
//      width: 90%;
//      height: auto;
//      top: 0;
//      margin: 0 auto;
//      -moz-transform: scale(1, -1) rotate(10deg);
//      -o-transform: scale(1, -1) rotate(10deg);
//      -webkit-transform: scale(1, -1) rotate(10deg);
//      transform: scale(1, -1) rotate(10deg);
//      @media @extraSmallDevices, @smallDevices {
//        width: 80%;
//        top: 20px;
//      }
//    }
//    .background-animated-image {
//      width: 150%;
//      height: auto;
//      z-index: -1;
//      top: -20%;
//      left: -20%;
//    }
//    .nextexam-logo, .prepjoy-logo {
//      width: 100%;
//    }
//    h3 {
//      font-weight: 600;
//      @media @extraSmallDevices, @smallDevices {
//        font-size: 20px;
//      }
//    }
//    h5 {
//      line-height: 24px;
//      font-weight: @semi-bold;
//      @media @extraSmallDevices, @smallDevices {
//        font-size: 15px;
//      }
//    }
//    .available-resources {
//      .col-6 {
//        @media @mediumDevices {
//          margin-bottom: 70px;
//          &:last-child {
//            margin-bottom: 0;
//          }
//          h5 {
//            line-height: normal;
//            font-size: 16px;
//          }
//        }
//        @media @extraSmallDevices, @smallDevices {
//          margin-bottom: 70px;
//          &:last-child {
//            margin-bottom: 40px;
//          }
//          h5 {
//            line-height: normal;
//            font-size: 15px;
//          }
//        }
//        &:before {
//          content: '';
//          position: absolute;
//          width: 85px;
//          height: 85px;
//          border-radius: 50px;
//          left: 0;
//          right: 0;
//          top: 0;
//          margin: -35px auto 0;
//        }
//        &:after {
//          content: '';
//          position: absolute;
//          width: 60px;
//          height: 8px;
//          bottom: -4px;
//          left: 0;
//          right: 0;
//          margin: 0 auto;
//          z-index: 2;
//          border-radius: 10px;
//        }
//        &:first-child:before,&:first-child:after {
//          background-color: #69AEF9;
//        }
//        &:nth-child(2):before,&:nth-child(2):after {
//          background-color: #FDA34D;
//        }
//        &:nth-child(3):before,&:nth-child(3):after {
//          background-color: #F26A6A;
//        }
//        &:nth-child(4):before,&:nth-child(4):after {
//          background-color: #0BD2D1;
//        }
//        &:last-child:before,&:last-child:after {
//          background-color: @light-green;
//        }
//        &:first-child i {
//          color: #69AEF9;
//        }
//        &:nth-child(2) i {
//          color: #FDA34D;
//        }
//        &:nth-child(3) i {
//          color: #F26A6A;
//        }
//        &:nth-child(4) i {
//          color: #0BD2D1;
//        }
//        &:last-child i {
//          color: @light-green;
//        }
//      }
//      .resource-list {
//        background: @white;
//        border-radius: 15px;
//        min-height: 150px;
//        position: relative;
//        z-index: 2;
//        box-shadow: 0 2px 4px @gray-dark-shadow;
//        border: solid 1px rgba(245,247,250,0.06);
//        padding: 5px;
//        i {
//          background: @white;
//          padding: 15px;
//          border-radius: 50px;
//          box-shadow: 0 2px 4px @gray-dark-shadow;
//          font-size: 35px;
//          position: relative;
//          top: -30px;
//        }
//      }
//    }
//    .download_app_links {
//      a {
//        margin: 0 10px;
//        @media @extraSmallDevices, @smallDevices {
//          margin: 0 5px 10px;
//        }
//        img {
//          margin-right: 10px;
//          width: 150px;
//          height: auto;
//        }
//        span {
//          line-height: normal;
//          font-size: 16px;
//        }
//      }
//    }
//  }
//}